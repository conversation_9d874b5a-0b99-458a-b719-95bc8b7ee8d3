package com.kaching.annotations;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import com.kaching.platform.discovery.ServiceKind;

@Retention(RUNTIME)
@Target(TYPE)
@Deprecated
public @interface WealthfrontQuery {

  /**
   * Services that use the query.
   */
  Class<? extends ServiceKind>[] usedBy() default {};

}
