package com.kaching.entities;

import com.kaching.platform.common.AbstractIdentifier;
import com.kaching.platform.common.Strings;
import com.kaching.platform.converters.NullHandlingConverter;
import com.twolattes.json.Json;
import com.twolattes.json.MarshalledBy;
import com.twolattes.json.types.NullSafeType;

@MarshalledBy(Comment.JsonType.class)
public class Comment extends AbstractIdentifier<String> {

  private static final long serialVersionUID = 1L;

  public static Comment comment(String template, Object... args) {
    return comment(Strings.format(template, args));
  }

  public static Comment comment(String comment) {
    if (comment == null) {
      return null;
    }
    return new Comment(comment);
  }

  public static Comment comment(Object object) {
    if (object == null) {
      return null;
    }
    return new Comment(object.toString());
  }

  public Comment(String comment) {
    super(comment);
  }

  public static class JsonType extends NullSafeType<Comment, Json.String> {

    @Override
    protected Json.String nullSafeMarshall(Comment comment) {
      return Json.string(comment.getId());
    }

    @Override
    protected Comment nullSafeUnmarshall(Json.String string) {
      return new Comment(string.getString());
    }
  }

  public static class Converter extends NullHandlingConverter<Comment> {

    @Override
    protected Comment fromNonNullableString(String representation) {
      return new Comment(representation);
    }

    @Override
    protected String nonNullableToString(Comment value) {
      return value.getId();
    }
  }
}
