package com.kaching.entities;

import static com.kaching.DefaultKachingMarshallers.createMarshaller;
import static com.kaching.entities.ServiceQueryInfo.QueryInfo.buildQueryInfo;
import static java.lang.reflect.Modifier.isAbstract;
import static java.lang.reflect.Modifier.isPrivate;
import static java.lang.reflect.Modifier.isProtected;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

import java.lang.reflect.Constructor;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.google.common.collect.Streams;
import com.kaching.platform.common.Strings;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.queryengine.QueryProxies;
import com.twolattes.json.Marshaller;
import com.twolattes.json.Value;

@com.twolattes.json.Entity
public class ServiceQueryInfo {

  public static final Marshaller<ServiceQueryInfo> MARSHALLER = createMarshaller(ServiceQueryInfo.class);

  @Value
  private Map<String, QueryInfo> queryNameToInfo;

  @Value
  private String serviceName;

  private ServiceQueryInfo() { /* JSON */ }

  public ServiceQueryInfo(Map<String, QueryInfo> queryNameToInfo, String serviceName) {
    this.queryNameToInfo = queryNameToInfo;
    this.serviceName = serviceName;
  }

  public static ServiceQueryInfo buildServiceQueryInfo(QueryProxies proxies, String serviceName) {
    Map<String, QueryInfo> queryNameToInfo = new HashMap<>();
    proxies.forEach(query ->
        queryNameToInfo.put(query.getQueryClass().getSimpleName(), buildQueryInfo(query.getQueryClass())));

    return new ServiceQueryInfo(queryNameToInfo, serviceName);
  }

  public Map<String, QueryInfo> getQueryNameToInfo() {
    return queryNameToInfo;
  }

  public String getServiceName() {
    return serviceName;
  }

  @com.twolattes.json.Entity
  public static class QueryInfo {

    @Value private String fullQueryName;
    @Value private List<ConstructorInfo> constructorInfos;
    @Value private String returnType;
    @Value private Set<String> annotations;

    private QueryInfo() { /* JSON */ }

    private QueryInfo(String fullQueryName,
        List<ConstructorInfo> constructorInfos,
        String returnType,
        Set<String> annotations) {
      this.fullQueryName = fullQueryName;
      this.constructorInfos = constructorInfos;
      this.returnType = returnType;
      this.annotations = annotations;
    }

    public static QueryInfo buildQueryInfo(Class<? extends Query> queryClass) {
      String returnType;
      try {
        returnType = queryClass.getMethod("process").getGenericReturnType().getTypeName();
      } catch (NoSuchMethodException e) {
        throw new RuntimeException(Strings.format("Query %s has no process method", queryClass.getSimpleName()), e);
      }

      return new QueryInfo(
          queryClass.getName(),
          Arrays.stream(queryClass.getDeclaredConstructors())
              .filter(constructor -> !isPrivate(constructor.getModifiers()) &&
                  !isProtected(constructor.getModifiers()) &&
                  !isAbstract(constructor.getModifiers()))
              .map(ConstructorInfo::buildConstructorInfo)
              .collect(toList()),
          returnType,
          Arrays.stream(queryClass.getDeclaredAnnotations())
              .map(annotation -> annotation.annotationType().getTypeName())
              .collect(toSet())
      );
    }

    public String getFullQueryName() {
      return fullQueryName;
    }

    public List<ConstructorInfo> getConstructorInfos() {
      return constructorInfos;
    }

    public String getReturnType() {
      return returnType;
    }

    public Set<String> getAnnotations() {
      return annotations;
    }

  }

  @com.twolattes.json.Entity
  public static class ConstructorInfo {

    @Value private int modifiers;
    @Value private Set<String> constructorAnnotations;
    @Value private List<ConstructorArgumentInfo> argumentInfos;

    private ConstructorInfo() { /* JSON */ }

    private ConstructorInfo(
        int modifiers,
        Set<String> constructorAnnotations,
        List<ConstructorArgumentInfo> argumentInfos) {
      this.modifiers = modifiers;
      this.constructorAnnotations = constructorAnnotations;
      this.argumentInfos = argumentInfos;
    }

    public static ConstructorInfo buildConstructorInfo(Constructor<?> queryConstructor) {
      return new ConstructorInfo(
          queryConstructor.getModifiers(),
          Arrays.stream(queryConstructor.getAnnotations())
              .map(annotation -> annotation.annotationType().getName())
              .collect(toSet()),
          Streams.zip(
              Arrays.stream(queryConstructor.getGenericParameterTypes()),
              Arrays.stream(queryConstructor.getParameterAnnotations())
                  .map(annotations -> Arrays.stream(annotations)
                      .map(annotation -> annotation.annotationType().getName())
                      .collect(toSet())),
              (argument, annotations) -> new ConstructorArgumentInfo(argument.getTypeName(), annotations))
              .collect(toList())
      );
    }

    public Set<String> getConstructorAnnotations() {
      return constructorAnnotations;
    }

    public List<ConstructorArgumentInfo> getArgumentInfos() {
      return argumentInfos;
    }

    public int getModifiers() {
      return modifiers;
    }

  }

  @com.twolattes.json.Entity
  public static class ConstructorArgumentInfo {

    @Value private String argumentType;
    @Value private Set<String> argumentAnnotations;

    private ConstructorArgumentInfo() { /* JSON */ }

    public ConstructorArgumentInfo(String argumentType, Set<String> argumentAnnotations) {
      this.argumentType = argumentType;
      this.argumentAnnotations = argumentAnnotations;
    }

    public String getArgumentType() {
      return argumentType;
    }

    public Set<String> getArgumentAnnotations() {
      return argumentAnnotations;
    }

  }

}
