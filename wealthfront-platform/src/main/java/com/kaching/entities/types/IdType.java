package com.kaching.entities.types;

import static java.lang.String.format;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.Arrays;

import org.hibernate.HibernateException;
import org.hibernate.id.ResultSetIdentifierConsumer;
import org.hibernate.usertype.UserType;

import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.types.AbstractImmutableType;

/**
 * The Hibernate type for the {@link Id} class.
 * TODO: move to kawala, hibernate/types
 */
public class IdType extends AbstractImmutableType implements UserType, ResultSetIdentifierConsumer {

  private static final int[] SQL_TYPES = {Types.BIGINT};

  public int[] sqlTypes() {
    return Arrays.copyOf(SQL_TYPES, SQL_TYPES.length);
  }

  @SuppressWarnings("rawtypes")
  public Class<Id> returnedClass() {
    return Id.class;
  }

  public Id<?> nullSafeGet(ResultSet rs, String[] names, Object owner)
      throws HibernateException, SQLException {
    BigDecimal n = rs.getBigDecimal(names[0]);
    // deferred read (http://opensource.atlassian.com/projects/hibernate/browse/HB-1433)
    // http://java.sun.com/j2se/1.5.0/docs/api/java/sql/ResultSet.html#wasNull()
    if (rs.wasNull()) {
      return null;
    } else {
      return new Id<>(n.longValue());
    }
  }

  @Override
  public void nullSafeSet(PreparedStatement ps, Object value, int index)
      throws HibernateException, SQLException {
    super.nullSafeSet(ps, value, index);
    if (value == null) {
      ps.setNull(index, Types.BIGINT);
    } else {
      try {
        ps.setLong(index, ((Id<?>) value).getId());
      } catch (ClassCastException e) {
        throw new RuntimeException(format("%s setting value %s", ps, value), e);
      }
    }
  }

  @Override
  public Serializable consumeIdentifier(ResultSet rs) {
    try {
      String columnName = rs.getMetaData().getColumnName(1);
      return nullSafeGet(rs, new String[]{columnName}, null);
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
  }
}
