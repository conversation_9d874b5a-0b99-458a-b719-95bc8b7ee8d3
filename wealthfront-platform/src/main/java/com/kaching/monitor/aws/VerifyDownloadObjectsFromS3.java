package com.kaching.monitor.aws;

import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3Object;
import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.kaching.platform.common.Errors;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.wealthfront.s3.connection.RetryingS3Client;
import com.wealthfront.s3.connection.WFBucket;

public class VerifyDownloadObjectsFromS3 extends AbstractQuery<Errors> {

  private static final Log log = Log.getLog(VerifyDownloadObjectsFromS3.class);

  @Inject AwsSelfTestPagerDutyNotifier awsSelfTestPagerDutyNotifier;
  @Inject StackTraceMonitor stackTraceMonitor;
  @Inject RetryingS3Client s3Client;

  @Inject(optional = true) @Named("aws_self_test.should_page") boolean shouldPage = true;
  @Inject(optional = true) @Named("aws_self_test.rollback_service") boolean rollBackService = true;
  @Inject(optional = true) @Named("aws_self_test.s3_bucket") String s3Bucket = WFBucket.WF_DATA.getName();
  @Inject(optional = true) @Named("aws_self_test.s3_download_key") String s3DownloadKey = "status/self-test/example-download.avro";

  @Override
  public Errors process() {
    String s3ObjectFile = String.format("%s/%s", s3Bucket, s3DownloadKey);
    log.info("Verify download objects from s3 for %s", s3ObjectFile);
    Errors errors = new Errors();
    Option<String> errorMessage = validateAndReturnErrorMessage();
    if (shouldPage) {
      try {
        String pagerSubject = String.format("Communication with objects from S3 failed for object %s", s3ObjectFile);
        awsSelfTestPagerDutyNotifier.notifyPager(errorMessage, pagerSubject);
      } catch (Exception ex) {
        log.error("Exception during notifying pager duty %s", ex.getMessage());
        stackTraceMonitor.add(ex);
      }
    }
    if (rollBackService && errorMessage.isDefined()) {
      errors.addMessage("Could not communicate with objects %s from S3 due to %s",
          s3ObjectFile, errorMessage.getOrThrow());
    }
    return errors;
  }

  @VisibleForTesting
  Option<String> validateAndReturnErrorMessage() {
    String s3ObjectFile = String.format("%s/%s", s3Bucket, s3DownloadKey);
    try {
      S3Object s3Object = s3Client.getObject(s3Bucket, s3DownloadKey);
      ObjectMetadata objectMetadata = s3Object.getObjectMetadata();
      long contentLength = objectMetadata.getContentLength();
      log.info("Verified download from S3. Object size for %s = %s", s3ObjectFile, contentLength);
      return Option.none();
    } catch (Exception e) {
      String exceptionMessage =
          String.format("Could not download object %s from S3 due to exception :: %s", s3ObjectFile, e.getMessage());
      log.error(exceptionMessage);
      stackTraceMonitor.add(e);
      return Option.some(exceptionMessage);
    }
  }

}
