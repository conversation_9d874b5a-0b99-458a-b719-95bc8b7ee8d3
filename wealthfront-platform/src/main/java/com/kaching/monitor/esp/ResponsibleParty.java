package com.kaching.monitor.esp;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import static java.util.Arrays.asList;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.Collections;
import java.util.List;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.Author;
import com.kaching.platform.common.logging.Log;

@Target({TYPE})
@Retention(RUNTIME)
public @interface ResponsibleParty {

  Author[] value(); // alerted users

  class Util {

    private static final Log log = Log.getLog(ResponsibleParty.class);

    public static List<Author> getAlertedContacts(String query) {
      log.info("Getting alerted contacts for: '%s'", query);
      if (query == null) {
        return Collections.emptyList();
      }
      try {
        Class<?> clazz = Class.forName(parseQuery(query));
        return getAlertedContacts(clazz);
      } catch (ClassNotFoundException e) {
        return Collections.emptyList();
      }
    }

    public static List<Author> getAlertedContacts(Class<?> clazz) {
      if (clazz == null) {
        return Collections.emptyList();
      }
      ResponsibleParty annotation = clazz.getAnnotation(ResponsibleParty.class);
      if (annotation != null) {
        return asList(annotation.value());
      }
      return Collections.emptyList();
    }

    @VisibleForTesting
    static String parseQuery(String query) {
      if (query == null) {
        return null;
      }
      int index = query.indexOf('(');
      return index != -1 ? query.substring(0, index) : query;
    }

  }

}
