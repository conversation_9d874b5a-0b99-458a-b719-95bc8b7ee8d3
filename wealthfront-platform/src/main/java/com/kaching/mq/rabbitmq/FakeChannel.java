package com.kaching.mq.rabbitmq;

import java.io.IOException;
import java.util.Map;

import com.rabbitmq.client.AMQP.BasicProperties;
import com.rabbitmq.client.AMQP.Exchange.DeclareOk;
import com.rabbitmq.client.AMQP.Exchange.DeleteOk;
import com.rabbitmq.client.AMQP.Queue.BindOk;
import com.rabbitmq.client.AMQP.Queue.PurgeOk;
import com.rabbitmq.client.AMQP.Queue.UnbindOk;
import com.rabbitmq.client.AMQP.Tx.CommitOk;
import com.rabbitmq.client.AMQP.Tx.RollbackOk;
import com.rabbitmq.client.AMQP.Tx.SelectOk;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.GetResponse;
import com.rabbitmq.client.ReturnListener;
import com.rabbitmq.client.ShutdownListener;
import com.rabbitmq.client.ShutdownSignalException;

class FakeChannel implements Channel {

  @Override
  public void abort() throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void abort(int closeCode, String closeMessage) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void basicAck(long deliveryTag, boolean multiple) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void basicCancel(String consumerTag) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void basicRecoverAsync(boolean b) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public String basicConsume(
      String queue,
      com.rabbitmq.client.Consumer callback) throws IOException {
    return "<FakeChannelTag>";
  }

  @Override
  public String basicConsume(
      String queue, boolean noAck,
      com.rabbitmq.client.Consumer callback) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public String basicConsume(
      String queue, boolean noAck, String consumerTag,
      com.rabbitmq.client.Consumer callback) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public String basicConsume(
      String queue, boolean noAck, String consumerTag,
      boolean noLocal, boolean exclusive,
      com.rabbitmq.client.Consumer callback) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public GetResponse basicGet(String queue, boolean noAck) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void basicPublish(
      String exchange, String routingKey,
      BasicProperties props, byte[] body) throws IOException {
  }

  @Override
  public void basicPublish(
      String exchange, String routingKey,
      boolean mandatory, boolean immediate, BasicProperties props, byte[] body)
      throws IOException {
  }

  @Override
  public void basicQos(int prefetchCount) throws IOException {

  }

  @Override
  public void basicQos(int prefetchSize, int prefetchCount, boolean global)
      throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void close() throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void close(int closeCode, String closeMessage) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public DeclareOk exchangeDeclare(String exchange, String type)
      throws IOException {
    return null;
  }

  @Override
  public DeclareOk exchangeDeclare(
      String exchange, String type,
      boolean durable) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public DeclareOk exchangeDeclare(
      String exchange, String type,
      boolean passive, boolean durable, boolean autoDelete,
      Map<String, Object> arguments) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public DeleteOk exchangeDelete(String exchange) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public DeleteOk exchangeDelete(String exchange, boolean ifUnused)
      throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public int getChannelNumber() {
    throw new UnsupportedOperationException();
  }

  @Override
  public Connection getConnection() {
    throw new UnsupportedOperationException();
  }

  @Override
  public ReturnListener getReturnListener() {
    throw new UnsupportedOperationException();
  }

  @Override
  public BindOk queueBind(String queue, String exchange, String routingKey)
      throws IOException {
    return null;
  }

  @Override
  public BindOk queueBind(
      String queue, String exchange, String routingKey,
      Map<String, Object> arguments) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public com.rabbitmq.client.AMQP.Queue.DeclareOk queueDeclare()
      throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public com.rabbitmq.client.AMQP.Queue.DeclareOk queueDeclare(String queue)
      throws IOException {
    return null;
  }

  @Override
  public com.rabbitmq.client.AMQP.Queue.DeclareOk queueDeclare(
      String queue,
      boolean durable) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public com.rabbitmq.client.AMQP.Queue.DeclareOk queueDeclare(
      String queue,
      boolean passive, boolean durable, boolean exclusive,
      boolean autoDelete, Map<String, Object> arguments) throws IOException {
    return null;
  }

  @Override
  public com.rabbitmq.client.AMQP.Queue.DeleteOk queueDelete(String queue)
      throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public com.rabbitmq.client.AMQP.Queue.DeleteOk queueDelete(
      String queue,
      boolean ifUnused, boolean ifEmpty) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public PurgeOk queuePurge(String queue) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public PurgeOk queuePurge(String queue, boolean nowait) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public UnbindOk queueUnbind(String queue, String exchange, String routingKey)
      throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public UnbindOk queueUnbind(
      String queue, String exchange,
      String routingKey, Map<String, Object> arguments) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void setReturnListener(ReturnListener listener) {
    throw new UnsupportedOperationException();
  }

  @Override
  public CommitOk txCommit() throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public RollbackOk txRollback() throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public SelectOk txSelect() throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void addShutdownListener(ShutdownListener listener) {
    throw new UnsupportedOperationException();
  }

  @Override
  public ShutdownSignalException getCloseReason() {
    throw new UnsupportedOperationException();
  }

  @Override
  public boolean isOpen() {
    throw new UnsupportedOperationException();
  }

  @Override
  public void notifyListeners() {
    throw new UnsupportedOperationException();
  }

  @Override
  public void removeShutdownListener(ShutdownListener listener) {
    throw new UnsupportedOperationException();
  }

}
