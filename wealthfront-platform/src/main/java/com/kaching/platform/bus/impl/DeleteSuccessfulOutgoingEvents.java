package com.kaching.platform.bus.impl;

import static com.kaching.platform.common.logging.Log.getLog;
import static org.hibernate.criterion.Restrictions.lt;

import java.util.List;
import java.util.Set;

import org.hibernate.criterion.Projections;
import org.joda.time.DateTime;
import org.joda.time.Hours;
import org.joda.time.ReadablePeriod;

import com.google.common.base.Function;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.WithReadOnlySessionExpression;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.util.Batcher;

public class DeleteSuccessfulOutgoingEvents extends AbstractQuery<Integer> {

  private static final Log log = getLog(DeleteSuccessfulOutgoingEvents.class);
  private static final int BATCH_SIZE = 200;

  private final Option<Integer> maybeHours;

  DeleteSuccessfulOutgoingEvents(Option<Integer> maybeHours) {
    this.maybeHours = maybeHours;
  }

  @Inject RetryingTransacter retryingTransacter;
  @Inject DateTime now;
  @Inject @Named("outgoing-event-archive-window") ReadablePeriod archiveWindow;

  @Override
  public Integer process() {
    ReadablePeriod actualArchiveWindow =
        maybeHours
            .transform((Function<Integer, ReadablePeriod>) Hours::hours)
            .getOrElse(archiveWindow);

    Set<Id<OutgoingEvent>> eventsToDelete =
        retryingTransacter.execute(1, new WithReadOnlySessionExpression<Set<Id<OutgoingEvent>>>() {
          @Override
          public Set<Id<OutgoingEvent>> run(DbSession session) {
            return session
                .createCriteria(OutgoingEvent.class)
                .add(lt("succeededAt", now.minus(actualArchiveWindow)))
                .<Id<OutgoingEvent>>setProjection(Projections.id())
                .set();
          }
        });

    if (eventsToDelete.isEmpty()) {
      return 0;
    }
    log.info("deleting %s successful events older than %s", eventsToDelete.size(), actualArchiveWindow);
    for (List<Id<OutgoingEvent>> deleteBatch : Batcher.batchList(eventsToDelete, BATCH_SIZE)) {
      retryingTransacter.execute(3, new WithSession() {
        @Override
        public void run(DbSession session) {
          session.createQuery("delete from OutgoingEvent where id in :ids")
              .setParameterList("ids", deleteBatch)
              .executeUpdate();
        }
      });
    }

    return eventsToDelete.size();
  }

}
