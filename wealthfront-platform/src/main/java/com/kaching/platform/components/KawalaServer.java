package com.kaching.platform.components;

import static com.kaching.platform.discovery.Status.UP;

import java.util.Set;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.guice.KachingServer;
import com.kaching.platform.queryengine.QueryEngine;
import com.kaching.platform.queryengine.QueryEngine.QuerySchedule;
import com.kaching.platform.queryengine.SelfTestProvider;

/**
 * Temporary class to turn {@link KachingServer} into a Wealthfront specific extension to Kawala.
 * Should eventually be folded into {@link Kawala}.
 */
public class KawalaServer {

  private static final Log log = Log.getLog(KawalaServer.class);

  private final QueryEngine engine;
  private final Set<QuerySchedule<?>> schedules;
  private final StartupListener[] listeners;
  @VisibleForTesting
  public final SelfTestProvider selfTestProvider;

  public KawalaServer(
      QueryEngine engine,
      SelfTestProvider selfTestProvider,
      Set<QuerySchedule<?>> schedules,
      StartupListener... listeners
  ) {
    this.engine = engine;
    this.selfTestProvider = selfTestProvider;
    this.schedules = schedules;
    /* TODO create an "aggregate listener" to instrument code with single calls
     * aggregatedListerners.onXyz(); instead of a for loop each time. Also important to isolate
     * callbacks from each other as they could fail and cause the process to stop as a whole
     * due to the simplistic way it is currently written.
     */
    this.listeners = listeners;
  }

  public void start() throws Exception {
    try {
      // on pre query engine start
      log.info("pre Query Engine Start");
      for (StartupListener l : listeners) {
        l.onPreQueryEngineStart();
      }

      // query engine start
      log.info("Query Engine Start");
      // TODO should be moved into KachingServer, legacy not Kawala
      if (schedules != null) {
        for (QuerySchedule<?> schedule : schedules) {
          engine.schedule(schedule);
        }
      }
      engine.start();

      // on post query engine start
      log.info("post Query Engine Start");
      for (StartupListener l : listeners) {
        l.onPostQueryEngineStart();
      }

      // running self tests
      log.info("Running self tests");
      StartupResult startupResult = selfTestProvider.get().start();
      log.info("Finished running self tests");
      if (startupResult.getStatus() != UP) {
        engine.getStackTraceMonitor().add(new RuntimeException(Strings.format(
            "Self test (%s) returned with status %s\nReason: %s",
            selfTestProvider.get().getClass().getSimpleName(),
            startupResult.getStatus(),
            startupResult.getMaybeReason().getOrElse("N/A")
        )));
      }

      // on self tests completion
      for (StartupListener l : listeners) {
        l.onSelfTestsCompletion(startupResult);
      }
    } catch (Exception e) {
      // Aborting
      engine.getStackTraceMonitor().add(e);
      log.error(e); // So we duplicate the exception into the main log.
      for (StartupListener l : listeners) {
        l.onAbort(e, Option.some(Strings.format("KawalaServer startup aborted due to exception: %s", e)));
      }
      throw e;
    }
  }

}
