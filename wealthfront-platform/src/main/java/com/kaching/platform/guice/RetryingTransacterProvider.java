package com.kaching.platform.guice;

import java.lang.annotation.Annotation;

import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.Provider;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.Transacter;

public class RetryingTransacterProvider implements Provider<RetryingTransacter> {

  private final Annotation annotation;
  private final Provider<Injector> injectorProvider;

  public RetryingTransacterProvider(Annotation annotation, Provider<Injector> injectorProvider) {
    this.annotation = annotation;
    this.injectorProvider = injectorProvider;
  }

  @Override
  public RetryingTransacter get() {
    Injector injector = injectorProvider.get();
    Transacter annotatedTransacter = injector.getInstance(Key.get(Transacter.class, annotation));

    return new RetryingTransacter(annotatedTransacter);
  }

}
