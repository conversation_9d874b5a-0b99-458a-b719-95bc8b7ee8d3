package com.kaching.platform.hibernate;

import com.google.inject.ImplementedBy;

@ImplementedBy(EntityIdsBatcherProviderImpl.class)
public interface EntityIdsBatcherProvider {

  <E extends HibernateEntity> EntityIdsBatcher<E> get(Class<? extends E> clazz);

  <E extends HibernateEntity> EntityIdsBatcher<E> get(Class<? extends E> clazz, String stageName);

  <E extends HibernateEntity> EntityIdsBatcher<E> get(int numBatches, Class<? extends E> clazz);

  <E extends HibernateEntity> EntityIdsBatcher<E> get(int numBatches, Class<? extends E> clazz, String stageName);

}
