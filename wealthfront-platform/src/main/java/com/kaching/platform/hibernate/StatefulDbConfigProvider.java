package com.kaching.platform.hibernate;

import static com.kaching.platform.hibernate.StatefulSessionFactoryProvider.UNAVAILABLE;

import java.util.function.Supplier;

import org.hibernate.cfg.Configuration;

import com.google.inject.Provider;
import com.kaching.platform.common.Option;
import com.kaching.platform.queryengine.exceptions.TemporarilyUnavailable;
import com.wealthfront.util.concurrent.MutableThunk;

public class StatefulDbConfigProvider implements Provider<Configuration> {

  private final MutableThunk<Option<Configuration>> dbConfiguration;

  public StatefulDbConfigProvider(Supplier<Configuration> initialDbConfigFactory) {
    this.dbConfiguration = new MutableThunk<>(() -> Option.some(initialDbConfigFactory.get()));
  }

  @Override
  public Configuration get() {
    return dbConfiguration.get().getOrThrow(new TemporarilyUnavailable(UNAVAILABLE));
  }

  public void set(Configuration newDbConfig) {
    dbConfiguration.set(Option.of(newDbConfig));
  }

  public void unset() {
    set(null);
  }

}
