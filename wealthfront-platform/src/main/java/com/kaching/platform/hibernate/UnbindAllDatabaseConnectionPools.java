package com.kaching.platform.hibernate;

import java.lang.annotation.Annotation;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Pair;
import com.kaching.platform.discovery.ServiceDescriptor;
import com.kaching.platform.queryengine.AbstractQuery;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.wealthfront.util.objects.DerivedMethods;

public class UnbindAllDatabaseConnectionPools extends AbstractQuery<UnbindAllDatabaseConnectionPools.Result> {

  public enum Promise {
    YES_I_REALLY_WANT_TO_DO_THIS
  }

  @Entity
  public static class Result {

    private static final DerivedMethods<Result> DERIVED_METHODS = new DerivedMethods<>(Result.class);

    @Value(nullable = false) String serviceId;

    @Value(nullable = false) int connectionPoolsUnbound;

    Result() { /* json */ }

    public Result(String serviceId, int connectionPoolsUnbound) {
      this.serviceId = serviceId;
      this.connectionPoolsUnbound = connectionPoolsUnbound;
    }

    public String getServiceId() {
      return serviceId;
    }

    public int getConnectionPoolsUnbound() {
      return connectionPoolsUnbound;
    }

    @Override
    public int hashCode() {
      return DERIVED_METHODS.hashCode(this);
    }

    @Override
    public boolean equals(Object obj) {
      return DERIVED_METHODS.equals(this, obj);
    }

    @Override
    public String toString() {
      return DERIVED_METHODS.toString(this);
    }

  }

  private final Promise promise;
  private final String hostToUnbind;

  public UnbindAllDatabaseConnectionPools(Promise promise, String hostToUnbind) {
    this.promise = promise;
    this.hostToUnbind = hostToUnbind;
  }

  @Inject StatefulHibernateProviderFinder statefulHibernateProviderFinder;
  @Inject ServiceDescriptor descriptor;

  @Override
  public Result process() {
    Set<Option<Annotation>> annotationsToUnbind = statefulHibernateProviderFinder.getUrlsByAnnotation()
        .stream()
        .filter(pair -> pair.left.contains(hostToUnbind))
        .map(Pair::getRight)
        .collect(Collectors.toSet());

    int connectionPoolsUnbound = 0;

    for (Pair<StatefulSessionFactoryProvider, Option<Annotation>> pair :
        statefulHibernateProviderFinder.getSessionFactoryProviders()) {
      if (!annotationsToUnbind.contains(pair.right)) {
        continue;
      }
      pair.getLeft().unset();
      connectionPoolsUnbound += 1;
    }
    for (Pair<StatefulDbConfigProvider, Option<Annotation>> pair :
        statefulHibernateProviderFinder.getDbConfigProviders()) {
      if (!annotationsToUnbind.contains(pair.right)) {
        continue;
      }
      pair.getLeft().unset();
    }

    return new Result(descriptor.getId().getId(), connectionPoolsUnbound);
  }

}
