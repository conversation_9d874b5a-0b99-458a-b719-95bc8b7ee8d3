package com.kaching.platform.hibernate.collections;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collector;
import java.util.stream.LongStream;
import java.util.stream.Stream;

import com.kaching.entities.collections.LongArrayList;
import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;

public class IdList {

  public static <E extends HibernateEntity> List<Id<E>> entityIdList(Collection<E> entities) {
    return idList(entities.stream().mapToLong(hibernateEntity -> hibernateEntity.getId().asLong()));
  }

  public static <E extends HibernateEntity> List<Id<E>> idList(Collection<Id<E>> ids) {
    return idList(ids.stream());
  }

  public static <E extends HibernateEntity> List<Id<E>> idList(Stream<Id<E>> ids) {
    return idList(ids.mapToLong(Id::asLong));
  }

  static <E extends HibernateEntity> List<Id<E>> idList(LongStream idLongs) {
    return LongArrayList.mappingLongs(idLongs, Id::of, Id::asLong);
  }

  public static <E extends HibernateEntity> Collector<E, ?, List<Id<E>>> entitiesToIdList() {
    return LongArrayList.mappingCollector(hibernateEntity -> hibernateEntity.getId().asLong(), Id::of, Id::asLong);
  }

  public static <E extends HibernateEntity> Collector<Id<E>, ?, List<Id<E>>> toIdList() {
    return LongArrayList.mappingCollector(Id::asLong, Id::of, Id::asLong);
  }

  public static <E extends HibernateEntity> Collector<Long, ?, List<Id<E>>> longsToIdList() {
    return LongArrayList.mappingLongCollector(Id::of, Id::asLong);
  }

}