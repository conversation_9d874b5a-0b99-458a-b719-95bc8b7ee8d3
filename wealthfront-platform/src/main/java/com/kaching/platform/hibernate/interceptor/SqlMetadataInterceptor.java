package com.kaching.platform.hibernate.interceptor;

import org.hibernate.EmptyInterceptor;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.kaching.platform.hibernate.DatabaseKind;

@Singleton
public class SqlMetadataInterceptor extends EmptyInterceptor {

  @Inject SqlMetadataCommentGenerator sqlMetadataCommentGenerator;
  @Inject SqlSetStatementGenerator sqlSetStatementGenerator;
  @Inject(optional = true) DatabaseKind dbKind;

  @Override
  public String onPrepareStatement(String originalSql) {
    String resultSql = originalSql;
    for (String serializedSqlMetadata : sqlMetadataCommentGenerator.getSqlMetadataComment()) {
      resultSql += serializedSqlMetadata;
    }

    if (dbKind != null && dbKind.isMysql()) {
      for (String sqlSetStatementPrefix : sqlSetStatementGenerator.getSetStatementSql()) {
        resultSql = sqlSetStatementPrefix + resultSql;
      }
    }
    return resultSql;
  }

}
