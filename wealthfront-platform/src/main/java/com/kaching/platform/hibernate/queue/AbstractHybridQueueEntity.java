package com.kaching.platform.hibernate.queue;

import static com.wealthfront.util.time.DateTimeZones.ET;

import org.joda.time.DateTime;

import com.kaching.platform.hibernate.AbstractHibernateEntity;

public abstract class AbstractHybridQueueEntity extends AbstractHibernateEntity implements HybridQueueEntity {

  public static final DateTime IGNORED_AT_DUMMY_VALUE = new DateTime(1900, 1, 1, 0, 0, ET);

  protected IgnoredAtProvider getIgnoredAtProvider() {
    return new IgnoredAtProvider() {

      public void setIgnoredAt(DateTime ignoredAt) {
        if (ignoredAt == null) {
          if (IGNORED_AT_DUMMY_VALUE.equals(getSentTime())) {
            setSentTime(null);
          }
        } else {
          if (getPolledTime() == null) {
            setPolledTime(ignoredAt);
          }
          setSentTime(IGNORED_AT_DUMMY_VALUE);
        }
      }

      public boolean isIgnored() {
        return IGNORED_AT_DUMMY_VALUE.equals(getSentTime());
      }

    };
  }

  /**
   * It would give incorrect results to override one of these methods and not the other.
   * Override the provider instead, or don't extend this class.
   */
  public final void setIgnoredAt(DateTime ignoredAt) {
    getIgnoredAtProvider().setIgnoredAt(ignoredAt);
  }

  /**
   * It would give incorrect results to override one of these methods and not the other.
   * Override the provider instead, or don't extend this class.
   */
  public final boolean isIgnored() {
    return getIgnoredAtProvider().isIgnored();
  }

  public interface IgnoredAtProvider {

    void setIgnoredAt(DateTime ignoredAt);

    boolean isIgnored();

  }

}
