package com.kaching.platform.hibernate.queue;

import java.util.Map;
import java.util.Set;

import org.apache.http.conn.ConnectTimeoutException;
import org.joda.time.Duration;

import com.google.common.base.Throwables;
import com.google.common.collect.Range;
import com.kaching.entities.PowerOfTwo;
import com.kaching.platform.discovery.ResolutionException;
import com.kaching.util.mail.Pager;
import com.kaching.util.schedule.PagingSchedules;
import com.twolattes.json.Marshaller;

/**
 * Infrastructure to process units of work asynchronously in large batches.
 * 
 * @param <BatchData> Json {@link com.twolattes.json.Entity} class holding properties of an entire batch (commonly job effective date, mode, etc.).
 *                    Use {@link UnitBatchData} if the batch data is not needed.
 * @param <ItemData> Any class marshallable to a Json value, keying work to be done.
 *                   Should be kept as small as possible (prefer a single ID over a complex object)
 */
public interface BatchQueue<BatchData, ItemData> {

  /**
   * Number of items that can process in parallel at once.
   */
  int getParallelism();

  Marshaller<BatchData> getBatchDataMarshaller();

  Marshaller<ItemData> getItemDataMarshaller();

  /**
   * Attempt to process a batch of items outside a DB session, possibly returning a different outcome for each item.
   * If different items have different outcomes, the batch will be split and persisted as multiple batches. Missing keys will be 
   * treated as {@link BatchItemOutcome.ErrorFlag}s. Throwing an exception in this method will call {@link #handleProcessBatchException(Exception)},
   * and the entire batch will adopt the outcome returned by that method.
   * <p>
   * The BatchQueue guarantees at-least-once delivery using this out-of-session method. Any side effects must be idempotent through independent means.
   * <p>
   * See {@link BatchItemOutcome#allSuccess(Set)} for a convenience method. If the whole batch needs to be errored or retried, throw an exception
   * here and handle in {@link #handleProcessBatchException(Exception)}.
   */
  Map<BatchItemId, BatchItemOutcome> processBatch(BatchData batchData, Map<BatchItemId, ItemData> items);

  /**
   * Name for the queue. This name is persisted with batches and should remain stable, otherwise queued batches may be orphaned.
   */
  default String getQueueName() {
    return getClass().getSimpleName();
  }
  
  Pager.Device getPagerDevice();

  default BatchItemOutcome handleProcessBatchException(Exception e) {
    if (e instanceof HybridQueueErrorFlagException) {
      return new BatchItemOutcome.ErrorFlag(e.getMessage());
    }
    return new BatchItemOutcome.Retry(getMaxRetries());
  }

  default int getMaxRetries() {
    return 3;
  }

  /**
   * Distinguish between exceptions caused by failures in individual items (return false)
   * and exceptions that affect the entire batch and therefore other batches (return true).
   * Exceptions that affect entire batches will cause the entire queue to slow down or pause
   * according to {@link #getThrottler()}.
   *
   * If overriding, it's likely that you'll want to call the default method in your implementation:
   * defaultExceptionAffectsEntireBatch(e) || e instanceof MyException
   */
  default boolean exceptionAffectsEntireBatch(Exception e) {
    return defaultExceptionAffectsEntireBatch(e);
  }

  static boolean defaultExceptionAffectsEntireBatch(Exception e) {
    return Throwables.getCausalChain(e).stream()
        .anyMatch(t -> t instanceof ResolutionException || t instanceof ConnectTimeoutException);
  }

  default BatchSizer getBatchSizer() {
    return TargetDurationBatchSizer.create()
        .withName(getQueueName())
        .withMinBatchSize(new PowerOfTwo(16))
        .withMaxBatchSize(new PowerOfTwo(1024))
        .withStartingBatchSize(new PowerOfTwo(64))
        .withTargetDuration(Duration.millis(500))
        .build();
  }
  
  default BatchQueueThrottler getThrottler() {
    return DefaultBatchQueueThrottler.create()
        .withQueuePausingExceptions(this::exceptionAffectsEntireBatch)
        .build();
  }

  default BatchQueueMonitoringConfig getMonitoringConfig() {
    return BatchQueueMonitoringConfig.create()
        .addAlertForErroredItems(PagingSchedules.TWENTY_FOUR_SEVEN, Range.atLeast(1))
        .addAlertForUnsentItems(PagingSchedules.TWENTY_FOUR_SEVEN, Duration.standardMinutes(30), Range.atLeast(1))
        .build();
  }
  
  default BatchQueueDeletionPolicy getDeletionPolicy() {
    return BatchQueueDeletionPolicy.create()
        .withDeleteSentAfter(Duration.standardDays(2))
        .withDeleteIgnoredAfter(Duration.standardDays(7))
        .build();
  }

}