package com.kaching.platform.hibernate.queue;

import org.joda.time.Duration;

import com.kaching.platform.common.Option;
import com.wealthfront.util.objects.DerivedMethods;

public class BatchQueueDeletionPolicy {
  
  private static final DerivedMethods<BatchQueueDeletionPolicy> METHODS = new DerivedMethods<>(BatchQueueDeletionPolicy.class);
  
  private final Duration deleteSentAfter;
  private final Duration deleteIgnoredAfter;

  private BatchQueueDeletionPolicy(Duration deleteSentAfter, Duration deleteIgnoredAfter) {
    this.deleteSentAfter = deleteSentAfter;
    this.deleteIgnoredAfter = deleteIgnoredAfter;
  }

  public Option<Duration> getDeleteSentAfter() {
    return Option.of(deleteSentAfter);
  }

  public Option<Duration> getDeleteIgnoredAfter() {
    return Option.of(deleteIgnoredAfter);
  }
  
  public static Builder create() {
    return new Builder();
  }

  @Override
  public boolean equals(Object o) {
    return METHODS.equals(this, o);
  }

  @Override
  public int hashCode() {
    return METHODS.hashCode(this);
  }

  @Override
  public String toString() {
    return METHODS.toString(this);
  }

  public static class Builder {
    
    private Duration deleteSentAfter;
    private Duration deleteIgnoredAfter;

    public Builder withDeleteSentAfter(Duration deleteSentAfter) {
      this.deleteSentAfter = deleteSentAfter;
      return this;
    }

    public Builder withDeleteIgnoredAfter(Duration deleteIgnoredAfter) {
      this.deleteIgnoredAfter = deleteIgnoredAfter;
      return this;
    }
    
    public BatchQueueDeletionPolicy build() {
      return new BatchQueueDeletionPolicy(deleteSentAfter, deleteIgnoredAfter);
    }
    
  }
  
}
