package com.kaching.platform.hibernate.queue;

import java.util.Map;

import com.google.inject.AbstractModule;
import com.google.inject.Inject;
import com.google.inject.TypeLiteral;
import com.google.inject.multibindings.MapBinder;
import com.kaching.platform.common.Errors;
import com.kaching.platform.common.Option;
import com.kaching.platform.components.Component;
import com.kaching.platform.components.MulticoloStartupListener;
import com.kaching.platform.components.MulticoloWorker;
import com.kaching.platform.components.StartupResult;
import com.kaching.platform.queryengine.AbstractQuery;

@Component(
    modules = {
        HybridQueueComponent.Module.class
    },
    queries = {
        HybridQueueGetAllQueueNames.class,
        HybridQueueGetDebugState.class,
        HybridQueueGetStatus.class,
        HybridQueueGetThreads.class,
        HybridQueueMarkAsSent.class,
        HybridQueueMarkIgnored.class,
        HybridQueueSafelyUnlockExtraUnlock.class,
        HybridQueueSetErrorFlag.class,
        HybridQueueSetThreads.class,
        HybridQueueSignalEnqueueEntities.class,
        HybridQueueSignalEnqueueEntity.class,
        HybridQueueSuspend.class,
    },
    icingaChecks = {
        CheckHybridQueueProcessingRate.class,
        CheckHybridQueuesProcessingIgnoredEntities.class
    },
    startupListener = HybridQueueComponent.StartupListener.class,
    selfTests = HybridQueueComponent.MapInjectionSelfTest.class
)
public class HybridQueueComponent {

  static class StartupListener extends MulticoloStartupListener {

    @Inject Map<String, HybridQueue<?>> hybridQueueMap;
    @Inject MulticoloWorker multicoloWorker;

    @Override
    public void onPreQueryEngineStart() {

    }

    @Override
    public void onPostQueryEngineStart() {

    }

    @Override
    public void onSelfTestsCompletion(StartupResult result) {
      if (result.getStatus().isAvailable()) {
        hybridQueueMap.forEach((name, queue) -> multicoloWorker.startWorker(name, queue));
      }
    }

    @Override
    public void onAbort(Exception e, Option<String> maybeFailureReason) {

    }

  }

  static class MapInjectionSelfTest extends AbstractQuery<Errors> {

    // This injection will fail if someone binds two queues with the same name
    @SuppressWarnings("unused")
    @Inject Map<String, HybridQueue<?>> hybridQueueMap;

    @Override
    public Errors process() {
      return new Errors();
    }

  }

  public static class Module extends AbstractModule {

    @Override
    protected void configure() {
      MapBinder.newMapBinder(binder(), new TypeLiteral<String>() {}, new TypeLiteral<HybridQueue<?>>() {});
    }

  }

}
