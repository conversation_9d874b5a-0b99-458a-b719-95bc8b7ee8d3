package com.kaching.platform.hibernate.queue;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

import com.google.common.base.Joiner;
import com.google.inject.Inject;
import com.kaching.platform.common.Strings;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.util.Batcher;

public class HybridQueueSetErrorFlag extends AbstractQuery<String> {

  private static final int BATCH_SIZE = 100;
  private final String queueName;
  private final Set<Id<? extends HybridQueueEntity>> entityIds;
  private final boolean newErrorFlagValue;

  public HybridQueueSetErrorFlag(
      String queueName, boolean newErrorFlagValue, Set<Id<? extends HybridQueueEntity>> entityIds) {
    this.queueName = queueName;
    this.entityIds = entityIds;
    this.newErrorFlagValue = newErrorFlagValue;
  }

  @Inject Transacter transacter;
  @Inject QueueBinder queueBinder;

  @Override
  public String process() {
    AtomicInteger countFlagsAlreadySet = new AtomicInteger(0);
    AtomicInteger countEntitiesAlreadySent = new AtomicInteger(0);
    AtomicInteger countFlagsChanged = new AtomicInteger(0);
    Set<Id<? extends HybridQueueEntity>> idsFailedToLock = new HashSet<>();

    HybridQueue<?> queue = queueBinder.getQueue(queueName);
    for (List<Id<? extends HybridQueueEntity>> batch : Batcher.batchList(entityIds, getBatchSize())) {
      transacter.executeWithSession(session -> {
        for (Id<? extends HybridQueueEntity> entityId : batch) {
          boolean success = queue.tryLockAndExecute(Id.of(entityId), session, entity -> {
            if (entity.getErrorFlag() == newErrorFlagValue) {
              countFlagsAlreadySet.incrementAndGet();
              return;
            }
            if (newErrorFlagValue && entity.getSentTime() != null) {
              countEntitiesAlreadySent.incrementAndGet();
              return;
            }
            entity.setErrorFlag(newErrorFlagValue);
            countFlagsChanged.incrementAndGet();
          });

          if (!success) {
            idsFailedToLock.add(entityId);
          }
        }
      });
    }

    if (!newErrorFlagValue && countFlagsChanged.get() > 0) {
      queue.notifyEnqueue();
    }

    List<String> messages = new ArrayList<>();
    if (countFlagsChanged.get() > 0) {
      messages.add(Strings.format("Successfully changed %s error flag(s)", countFlagsChanged.get()));
    }
    if (countFlagsAlreadySet.get() > 0) {
      messages.add(Strings.format("%s error flag(s) were already %s", countFlagsAlreadySet.get(), newErrorFlagValue));
    }
    if (countEntitiesAlreadySent.get() > 0) {
      messages.add(Strings.format(
          "%s error flag(s) were not set because entity was already sent", countEntitiesAlreadySent.get()));
    }
    if (!idsFailedToLock.isEmpty()) {
      messages.add("Failed to acquire lock on these id(s). Maybe retry them? " + Joiner.on(",").join(idsFailedToLock));
    }
    return Joiner.on("\n").join(messages);
  }

  private int getBatchSize() {
    return BATCH_SIZE;
  }

}
