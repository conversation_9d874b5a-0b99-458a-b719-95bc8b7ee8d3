package com.kaching.platform.hibernate.queue.impl;

import static com.kaching.DefaultKachingMarshallers.createMarshaller;

import java.util.HashSet;
import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.discovery.ServiceId;
import com.twolattes.json.Entity;
import com.twolattes.json.Marshaller;
import com.twolattes.json.Value;
import com.wealthfront.util.objects.DerivedMethods;

@Entity
@ExposeType(value = {ExposeTo.BACKEND, ExposeTo.LOCAL})
public class BatchQueueMetadata {

  private static final DerivedMethods<BatchQueueMetadata>
      DERIVED_METHODS = new DerivedMethods<>(BatchQueueMetadata.class);
  public static final Marshaller<BatchQueueMetadata> MARSHALLER = createMarshaller(BatchQueueMetadata.class);

  @Value
  private Set<ServiceId> suspendedInstances;

  BatchQueueMetadata() { /* JSON */}

  public BatchQueueMetadata(Set<ServiceId> suspendedInstances) {
    this.suspendedInstances = suspendedInstances;
  }

  public void addInstance(ServiceId instance) {
    if (suspendedInstances == null) {
      suspendedInstances = new HashSet<>();
    }
    this.suspendedInstances.add(instance);
  }

  public void removeInstance(ServiceId instance) {
    this.suspendedInstances.remove(instance);
  }

  public Set<ServiceId> getSuspendedInstances() {
    if (suspendedInstances == null) {
      return new HashSet<>();
    }
    return suspendedInstances;
  }

  @Override
  public boolean equals(Object o) {
    return DERIVED_METHODS.equals(this, o);
  }

  @Override
  public int hashCode() {
    return DERIVED_METHODS.hashCode(this);
  }

}
