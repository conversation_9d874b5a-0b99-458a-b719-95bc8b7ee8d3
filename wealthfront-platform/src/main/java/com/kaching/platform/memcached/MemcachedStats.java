package com.kaching.platform.memcached;

import java.util.Map;

import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
public class MemcachedStats {

  @Value
  private long bytes;

  @Value
  private long maxBytes;

  @Value
  private long totalItems;

  @Value
  private long currItems;

  @Value
  private long evictions;

  @Value
  private long reclaimed;

  @Value
  private int currentConnections;

  @Value
  private long getCmds;

  @Value
  private long setCmds;

  @Value
  private long getHits;

  @Value
  private long getMisses;

  @Value
  private long incrHits;

  @Value
  private long incrMisses;

  @Value
  private long deleteHits;

  @Value
  private long deleteMisses;

  public MemcachedStats() { /* JSON */ }

  public MemcachedStats(Map<String, String> rawStats) {
    this.bytes = Long.valueOf(rawStats.getOrDefault("bytes", "0"));
    this.maxBytes = Long.valueOf(rawStats.getOrDefault("limit_maxbytes", "0"));
    this.totalItems = Long.valueOf(rawStats.getOrDefault("total_items", "0"));
    this.currItems = Long.valueOf(rawStats.getOrDefault("curr_items", "0"));
    this.evictions = Long.valueOf(rawStats.getOrDefault("evictions", "0"));
    this.reclaimed = Long.valueOf(rawStats.getOrDefault("reclaimed", "0"));
    this.currentConnections = Integer.valueOf(rawStats.getOrDefault("curr_connections", "0"));
    this.getCmds = Long.valueOf(rawStats.getOrDefault("cmd_get", "0"));
    this.setCmds = Long.valueOf(rawStats.getOrDefault("cmd_set", "0"));
    this.getHits = Long.valueOf(rawStats.getOrDefault("get_hits", "0"));
    this.getMisses = Long.valueOf(rawStats.getOrDefault("get_misses", "0"));
    this.incrHits = Long.valueOf(rawStats.getOrDefault("incr_hits", "0"));
    this.incrMisses = Long.valueOf(rawStats.getOrDefault("incr_misses", "0"));
    this.deleteHits = Long.valueOf(rawStats.getOrDefault("delete_hits", "0"));
    this.deleteMisses = Long.valueOf(rawStats.getOrDefault("delete_misses", "0"));
  }

  public long getBytes() {
    return bytes;
  }

  public long getMaxBytes() {
    return maxBytes;
  }

  public long getTotalItems() {
    return totalItems;
  }

  public long getCurrItems() {
    return currItems;
  }

  public long getEvictions() {
    return evictions;
  }

  public long getReclaimed() {
    return reclaimed;
  }

  public int getCurrentConnections() {
    return currentConnections;
  }

  public long getGetCmds() {
    return getCmds;
  }

  public long getSetCmds() {
    return setCmds;
  }

  public long getGetHits() {
    return getHits;
  }

  public long getGetMisses() {
    return getMisses;
  }

  public long getIncrHits() {
    return incrHits;
  }

  public long getIncrMisses() {
    return incrMisses;
  }

  public long getDeleteHits() {
    return deleteHits;
  }

  public long getDeleteMisses() {
    return deleteMisses;
  }

}
