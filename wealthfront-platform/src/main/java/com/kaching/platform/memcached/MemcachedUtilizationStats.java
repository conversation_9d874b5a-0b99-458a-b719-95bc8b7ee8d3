package com.kaching.platform.memcached;

import static com.kaching.platform.monitoring.VarzValue.VarzType.GAUGE;
import static java.util.stream.Collectors.toList;

import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.util.Comparator;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;

import com.google.common.collect.Maps;
import com.google.inject.Singleton;
import com.kaching.platform.common.Option;
import com.kaching.platform.monitoring.VarzValue;

@Singleton
public class MemcachedUtilizationStats {

  private final AtomicReference<Map<SocketAddress, Tracker>> utilization = new AtomicReference<>(null);

  @VarzValue(GAUGE)
  public Long getAppMem0CurrItems() {
    return retrieveData(0, Tracker::getCurrItems);
  }

  @VarzValue(GAUGE)
  public Long getAppMem0Bytes() {
    return retrieveData(0, Tracker::getBytes);
  }

  @VarzValue(GAUGE)
  public Long getAppMem0LimitMaxBytes() {
    return retrieveData(0, Tracker::getLimitMaxBytes);
  }

  @VarzValue(GAUGE)
  public Long getAppMem1CurrItems() {
    return retrieveData(1, Tracker::getCurrItems);
  }

  @VarzValue(GAUGE)
  public Long getAppMem1Bytes() {
    return retrieveData(1, Tracker::getBytes);
  }

  @VarzValue(GAUGE)
  public Long getAppMem1LimitMaxBytes() {
    return retrieveData(1, Tracker::getLimitMaxBytes);
  }

  @VarzValue(GAUGE)
  public Long getAppMem2CurrItems() {
    return retrieveData(2, Tracker::getCurrItems);
  }

  @VarzValue(GAUGE)
  public Long getAppMem2Bytes() {
    return retrieveData(2, Tracker::getBytes);
  }

  @VarzValue(GAUGE)
  public Long getAppMem2LimitMaxBytes() {
    return retrieveData(2, Tracker::getLimitMaxBytes);
  }

  @VarzValue(GAUGE)
  public Long getAppMem3CurrItems() {
    return retrieveData(3, Tracker::getCurrItems);
  }

  @VarzValue(GAUGE)
  public Long getAppMem3Bytes() {
    return retrieveData(3, Tracker::getBytes);
  }

  @VarzValue(GAUGE)
  public Long getAppMem3LimitMaxBytes() {
    return retrieveData(3, Tracker::getLimitMaxBytes);
  }

  @VarzValue(GAUGE)
  public Long getAppMem4CurrItems() {
    return retrieveData(4, Tracker::getCurrItems);
  }

  @VarzValue(GAUGE)
  public Long getAppMem4Bytes() {
    return retrieveData(4, Tracker::getBytes);
  }

  @VarzValue(GAUGE)
  public Long getAppMem4LimitMaxBytes() {
    return retrieveData(4, Tracker::getLimitMaxBytes);
  }

  @VarzValue(GAUGE)
  public Long getAppMem5CurrItems() {
    return retrieveData(5, Tracker::getCurrItems);
  }

  @VarzValue(GAUGE)
  public Long getAppMem5Bytes() {
    return retrieveData(5, Tracker::getBytes);
  }

  @VarzValue(GAUGE)
  public Long getAppMem5LimitMaxBytes() {
    return retrieveData(5, Tracker::getLimitMaxBytes);
  }

  public void update(Map<SocketAddress, MemcachedStats> memcachedStats) {
    utilization.set(Maps.transformValues(
        memcachedStats,
        stats -> new Tracker(stats.getCurrItems(), stats.getBytes(), stats.getMaxBytes())));
  }

  private <T> T retrieveData(int nodeNumber, Function<Tracker, T> getter) {
    Map<SocketAddress, Tracker> nodeUtilization = utilization.get();
    if (nodeUtilization == null || nodeNumber < 0 || nodeNumber >= nodeUtilization.size()) {
      return null;
    }
    SocketAddress socketAddress = nodeUtilization.keySet()
        .stream()
        .map(InetSocketAddress.class::cast)
        .sorted(Comparator.comparing(InetSocketAddress::getHostName))
        .collect(toList())
        .get(nodeNumber);
    return Option.of(nodeUtilization.get(socketAddress)).transform(getter::apply).getOrNull();
  }

  private static class Tracker {

    private final Long currItems;
    private final Long bytes;
    private final Long limitMaxBytes;

    Tracker(Long currItems, Long bytes, Long limitMaxBytes) {
      this.currItems = currItems;
      this.limitMaxBytes = limitMaxBytes;
      this.bytes = bytes;
    }

    public Long getCurrItems() {
      return currItems;
    }

    public Long getLimitMaxBytes() {
      return limitMaxBytes;
    }

    public Long getBytes() {
      return bytes;
    }

  }

}
