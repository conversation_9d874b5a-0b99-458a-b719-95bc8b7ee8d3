package com.kaching.platform.monitoring;

import java.sql.SQLException;

import com.google.inject.Inject;
import com.kaching.platform.common.Strings;
import com.kaching.platform.hibernate.C3P0PooledDataSourceFetcher;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.exceptions.InvalidArgumentException;
import com.mchange.v2.c3p0.PooledDataSource;

public class GetC3P0ConnectionPoolLoad extends AbstractQuery<C3P0HealthReport> {

  @Inject C3P0PooledDataSourceFetcher c3P0PooledDataSourceFetcher;

  @Override
  public C3P0HealthReport process() {
    PooledDataSource pooledDataSource = c3P0PooledDataSourceFetcher.get()
        .getOrThrow(new InvalidArgumentException("Service does not have a database connection pool"));

    try {
      return new C3P0HealthReport(pooledDataSource.getNumBusyConnectionsAllUsers(),
          pooledDataSource.getNumIdleConnectionsAllUsers(),
          pooledDataSource.getNumUnclosedOrphanedConnectionsAllUsers(),
          pooledDataSource.getNumThreadsAwaitingCheckoutDefaultUser());
    } catch (SQLException e) {
      throw new IllegalStateException(
          Strings.format("Unable to gather C3P0 Connection Pool data: %s", e));
    }
  }

}
