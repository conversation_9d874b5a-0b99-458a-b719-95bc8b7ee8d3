package com.kaching.platform.monitoring;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.QueryEngineStatus;

public class GetQueryEngineThreadPoolLoad extends AbstractQuery<QueryEngineHealthReport> {

  @Inject QueryEngineStatus queryEngineStatus;
  @Inject @Named("coreThreads") int maxActiveThreads;

  @Override
  public QueryEngineHealthReport process() {
    return new QueryEngineHealthReport(maxActiveThreads,
        queryEngineStatus.getActiveThreadCount(),
        queryEngineStatus.getLeasedThreadCount(),
        queryEngineStatus.getNumberOfRunningQueries());
  }

}
