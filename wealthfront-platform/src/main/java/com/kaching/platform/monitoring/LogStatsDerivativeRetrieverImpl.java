package com.kaching.platform.monitoring;

import static java.util.Collections.emptyMap;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.kaching.platform.common.Pair;
import com.kaching.platform.common.logging.LogStats;

public class LogStatsDerivativeRetrieverImpl implements LogStatsDerivativeRetriever {

  private static final Long THRESHOLD = 700_000L * 100L;
  private static final Set<String> IGNORED_LOGGERS = Set.of(
      "sltlh"
  );

  @Inject LogStats logStats;
  @Inject @Named("previous_bytes_logged") ConcurrentMap<String, LongAdder> previousMap;

  @Override
  public synchronized Map<String, Long> retrieveLogsPastThreshold() {
    if (previousMap.isEmpty()) {
      previousMap.putAll(logStats.getMap());
      return emptyMap();
    }

    Map<String, Long> result = logStats.getMap().entrySet().stream()
        .map(entry -> Pair.of(entry.getKey(),
            entry.getValue().sum() - previousMap.getOrDefault(entry.getKey(), new LongAdder()).sum()))
        .filter(pair -> pair.getRight() > THRESHOLD)
        .filter(pair -> !getIgnoredLoggers().contains(pair.getLeft()))
        .collect(Collectors.toMap(Pair::getLeft, Pair::getRight));

    previousMap.clear();
    previousMap.putAll(logStats.getMap());
    return result;
  }

  @VisibleForTesting
  Set<String> getIgnoredLoggers() {
    return IGNORED_LOGGERS;
  }

}
