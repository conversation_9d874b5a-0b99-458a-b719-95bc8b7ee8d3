package com.kaching.platform.monitoring;

import com.kaching.platform.guice.KachingServices.DMW;
import com.kaching.platform.queryengine.ServiceRevision;
import com.kaching.platform.queryengine.StubQuery;

public class PublishApplicationHealthMetrics extends Stub<PERSON>uery<Boolean, DMW> {

  private final ServiceRevision serviceRevision;
  private final HealthzReport healthzReport;

  public PublishApplicationHealthMetrics(ServiceRevision serviceRevision, HealthzReport healthzReport) {
    this.serviceRevision = serviceRevision;
    this.healthzReport = healthzReport;
  }

}
