package com.kaching.platform.monitoring;

import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Sets;
import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.monitoring.RunningQueriesSummarizer.SortType;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.QueryRuntimeMonitor;
import com.kaching.platform.queryengine.QuerySession;
import com.kaching.platform.queryengine.admin.Param;

public class TopLongRunningQueries extends AbstractQuery<String> {

  private final Option<Long> maybeThresholdMillis;

  public TopLongRunningQueries(@Param("RuntimeThresholdMillis") Option<Long> maybeThresholdMillis) {
    this.maybeThresholdMillis = maybeThresholdMillis;
  }

  @Inject QueryRuntimeMonitor runtimeMonitor;
  @Inject RunningQueriesSummarizer runningQueriesSummarizer;

  @Override
  public String process() {
    Set<QuerySession> runningQueries = Sets.union(
        runtimeMonitor.runningQueries(),
        runtimeMonitor.streamingQueries()
    );

    Set<QuerySession> longRunningQuerySessions = runningQueries.stream()
        .filter(querySession ->
            querySession.uptimeMilli() > maybeThresholdMillis.getOrElse(() -> (long) querySession.getSla().getError()))
        .collect(Collectors.toSet());

    return runningQueriesSummarizer.getSummaryText(longRunningQuerySessions, SortType.time);
  }

}
