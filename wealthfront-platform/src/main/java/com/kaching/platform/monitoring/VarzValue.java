package com.kaching.platform.monitoring;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import org.weakref.jmx.ManagedAnnotation;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.api.ExposeTo;

@Retention(RUNTIME)
@Target(ElementType.METHOD)
@ManagedAnnotation
public @interface VarzValue {

  @ExposeType(value = { ExposeTo.BACKEND }, namespace = RewriteNamespace.DO_NOT_COPY)
  enum VarzType {
    GAUGE,
    COUNTER,
    STRING;
  }

  VarzType value();
  String description() default "";

}
