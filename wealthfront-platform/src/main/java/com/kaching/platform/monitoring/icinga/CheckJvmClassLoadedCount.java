package com.kaching.platform.monitoring.icinga;

import static com.kaching.platform.monitoring.icinga.IcingaMetadata.Interval.THIRTY_SECONDS;
import static com.kaching.platform.monitoring.icinga.IcingaMetadata.TimePeriod.TWENTY_FOUR_X_SEVEN;
import static com.kaching.platform.monitoring.icinga.IcingaMetadata.icingaMetadataBuilder;
import static com.kaching.platform.monitoring.icinga.IcingaOutput.icingaOutputBuilder;

import java.lang.management.ClassLoadingMXBean;

import com.google.inject.Inject;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.monitoring.RunningQueriesSummarizer;
import com.kaching.platform.monitoring.RunningQueriesSummarizer.SortType;
import com.kaching.platform.monitoring.icinga.IcingaOutput.ExitCode;
import com.kaching.util.pagerduty.ServiceKindToPagerDuty;
import com.kaching.util.pagerduty.SiteHealthPagerTransitionUtils;

public class CheckJvmClassLoadedCount implements IcingaCheck {

  private static final Log log = Log.getLog(CheckJvmClassLoadedCount.class);
  private static final int CLASS_LOADED_CRITICAL_COUNT = 100_000;
  private static final int CLASS_LOADED_LOGGING_COUNT = 50_000;

  @Inject ClassLoadingMXBean classLoadingMXBean;
  @Inject ServiceKind serviceKind;
  @Inject RunningQueriesSummarizer runningQueriesSummarizer;

  @Override
  public IcingaMetadata getIcingaMetadata() {
    return icingaMetadataBuilder(CheckJvmClassLoadedCount.class)
        .withInterval(THIRTY_SECONDS)
        .withTimePeriod(TWENTY_FOUR_X_SEVEN)
        .withIcingaGroup(ServiceKindToPagerDuty
            .getPagerDutyDevices(serviceKind, SiteHealthPagerTransitionUtils.getServiceToPagerDeviceMappings()).get(0))
        .withAttempts(1)
        .build();
  }

  @Override
  public IcingaOutput getIcingaOutput() {
    int loadedClassCount = classLoadingMXBean.getLoadedClassCount();
    String statusMessage = Strings
        .format("There are %s loaded classes and the threshold is %s", loadedClassCount, CLASS_LOADED_CRITICAL_COUNT);
    if (loadedClassCount > CLASS_LOADED_LOGGING_COUNT) {
      log.info(statusMessage + "\n" + runningQueriesSummarizer.getSummaryText(SortType.time));
    }
    if (loadedClassCount > CLASS_LOADED_CRITICAL_COUNT) {
      return icingaOutputBuilder()
          .withMessage(statusMessage + "\n" +
              "Please check the logs for a list of queries currently running as PagerDuty will truncate output.")
          .withExitCode(ExitCode.CRITICAL)
          .build();
    }
    return icingaOutputBuilder()
        .withMessage(statusMessage)
        .withExitCode(ExitCode.OKAY)
        .build();
  }

}
