package com.kaching.platform.monitoring.nscaspooler;

import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
public class NscaspoolerServiceRunData {

  @Value
  private String message;

  @Value(name = "exit_code")
  private Integer exitCode;

  private NscaspoolerServiceRunData() { /* JSON */ }

  NscaspoolerServiceRunData(
      String message,
      Integer exitCode) {
    this.message = message;
    this.exitCode = exitCode;
  }

  public String getMessage() {
    return message;
  }

  public Integer getExitCode() {
    return exitCode;
  }

  public static class Builder {

    private String message;
    private Integer exitCode;

    public Builder withMessage(String message) {
      this.message = message;
      return this;
    }

    public Builder withExitCode(Integer exitCode) {
      this.exitCode = exitCode;
      return this;
    }

    public NscaspoolerServiceRunData build() {
      return new NscaspoolerServiceRunData(message, exitCode);
    }

  }

}
