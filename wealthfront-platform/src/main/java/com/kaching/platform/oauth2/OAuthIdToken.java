package com.kaching.platform.oauth2;

import com.google.common.base.Function;
import com.kaching.platform.common.AbstractIdentifier;
import com.kaching.platform.converters.ConvertedBy;
import com.kaching.util.id.StringIdentifiers;
import com.twolattes.json.MarshalledBy;

@ConvertedBy(OAuthIdToken.Converter.class)
@MarshalledBy(OAuthIdToken.JsonType.class)
public class OAuthIdToken extends AbstractIdentifier<String> {

  OAuthIdToken(String id) {
    super(id);
  }

  public static OAuthIdToken of(String string) {
    return new OAuthIdToken(string);
  }

  public static class Converter extends StringIdentifiers.Converter<OAuthIdToken> {

    @Override
    protected Function<String, OAuthIdToken> constructor() {
      return OAuthIdToken::new;
    }

  }

  public static class JsonType extends StringIdentifiers.JsonType<OAuthIdToken> {

    @Override
    protected Function<String, OAuthIdToken> constructor() {
      return OAuthIdToken::new;
    }

  }

}