package com.kaching.platform.pagerduty;

import static com.kaching.DefaultKachingMarshallers.createMarshaller;

import java.util.List;
import java.util.Map;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableMap;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.platform.common.Option;
import com.kaching.util.mail.Pager.Device;
import com.twolattes.json.Entity;
import com.twolattes.json.Marshaller;
import com.twolattes.json.Value;

@Entity
@ExposeType(value = {ExposeTo.BACKEND, ExposeTo.TAOS}, namespace = RewriteNamespace.DO_NOT_COPY)
public class EscalationPolicyInfo {

  public static final Marshaller<EscalationPolicyInfo> MARSHALLER = createMarshaller(EscalationPolicyInfo.class);

  @VisibleForTesting
  static final Map<String, Device> ESCALATION_PAGERS = ImmutableMap.<String, Device>builder()
      .put("P4H97F9", Device.PAGER_ACATS_OPS)
      .put("P6003OD", Device.PAGER_ADVICE_AUTOMATION)
      .put("P7F22SX", Device.PAGER_ADVISOR_OPS)
      .put("PWFY1TZ", Device.PAGER_ANDROID)
      .put("PM4WHYI", Device.PAGER_API)
      .put("PKYF71L", Device.PAGER_AWS_ADMIN)
      .put("PCUV11H", Device.PAGER_BROKERAGE_OPS)
      .put("P7ZSOPU", Device.PAGER_BROKERAGE_OPS_AND_INVESTMENT_SERVICES)
      .put("P5U0QDZ", Device.PAGER_BROKERAGE_OPS_AND_BROKERAGE_PLATFORM)
      .put("PTTC9C9", Device.PAGER_BROKERAGE_PLATFORM)
      .put("PBZV8N6", Device.PAGER_NEW_PRODUCT_1)
      .put("P593DRU", Device.PAGER_CASHIERING_OPS)
      .put("P8BPRJC", Device.PAGER_CASH_SETTLEMENT_OPS)
      .put("P9MUN02", Device.PAGER_CLIENT_REPORTING_OPS)
      .put("PVS4TGL", Device.PAGER_CONSOLIDATED_BREAKS)
      .put("PA07GGZ", Device.PAGER_DATA)
      .put("PPOLJMO", Device.PAGER_ANALYTICS_ENGINEERING)
      .put("P8NBDKY", Device.PAGER_ANALYTICS_ENGINEERING_NON_PROD)
      .put("P33VXNV", Device.PAGER_DATA_LEAD_LOW_PRIORITY)
      .put("PGOEKZS", Device.PAGER_DATA_NONPROD)
      .put("P5ZX15Q", Device.PAGER_DATA_SCIENCE)
      .put("P5XMRBA", Device.PAGER_DAVID)
      .put("PSV3K3W", Device.PAGER_BANKING_PLATFORM)
      .put("PB1963X", Device.PAGER_FRACTIONAL_SHARES)
      .put("PAJX9NO", Device.PAGER_FRAUD_AND_RISK)
      .put("P62VM0N", Device.PAGER_FRAUD_OPS)
      .put("PRPP17R", Device.PAGER_GD_ACCOUNT_LIFECYCLE)
      .put("PI5V9PI", Device.PAGER_GROWTH)
      .put("POEDQIS", Device.PAGER_HYOMIN)
      .put("P1AODPR", Device.PAGER_INVESTMENT_RESEARCH)
      .put("PLDNLVA", Device.PAGER_INVESTMENT_SERVICES)
      .put("PZDQJ3K", Device.PAGER_IOS)
      .put("PFKR2T4", Device.PAGER_IT)
      .put("P2F7K3R", Device.PAGER_JIAQI)
      .put("PWDQ70R", Device.PAGER_LENDING_PLATFORM)
      .put("PGMPESX", Device.PAGER_LINKING_SERVICES)
      .put("PM9PEP8", Device.PAGER_NEW_ACCOUNT_OPS)
      .put("P0A0NE0", Device.PAGER_ONLINE_SERVICES)
      .put("PWAHGR1", Device.PAGER_SITE_OUTAGE_ESCALATIONS)
      .put("PBZI44W", Device.PAGER_SHARED_SERVICES)
      .put("PZ1FMYC", Device.PAGER_SYSTEMS)
      .put("PXEYNOI", Device.PAGER_RESEARCH)
      .put("PD8W22L", Device.PAGER_FUND_OPERATIONS)
      .put("PCOZH13", Device.PAGER_SECURITY)
      .put("PTP38TR", Device.PAGER_TESTING_FAKE)
      .put("PJS2CS8", Device.PAGER_TRADING_OPS)
      .put("PC6RTRX", Device.PAGER_TRADING_PRODUCTS)
      .put("PZ46SMK", Device.PAGER_WEB)
      .put("PWZMTJ1", Device.PAGER_WINDOWS)
      .put("P6TBJ60", Device.PAGER_TAOS)
      .put("P7ULNI9", Device.PAGER_NETWORKING)
      .put("PTQPF24", Device.PAGER_INVESTMENT_PRODUCTS)
      .build();

  @Value private String escalationPolicyName;
  @Value(optional = true) private String id;
  @Value(optional = true) private Device pager;
  @Value private List<String> tier1Names;
  @Value private List<String> tier2Names;
  @Value private List<String> shadowNames;

  @SuppressWarnings("unused")
  private EscalationPolicyInfo() { /* json */ }

  public EscalationPolicyInfo(
      String escalationPolicyName, String id, List<String> tier1Names, List<String> tier2Names,
      List<String> shadowNames) {
    this.escalationPolicyName = escalationPolicyName;
    this.id = id;
    this.pager = ESCALATION_PAGERS.getOrDefault(id, null);
    this.tier1Names = tier1Names;
    this.tier2Names = tier2Names;
    this.shadowNames = shadowNames;
  }

  public String getEscalationPolicyName() {
    return escalationPolicyName;
  }

  public String getId() {
    return id;
  }

  public Device getPager() {
    return pager;
  }

  public List<String> getTier1Names() {
    return tier1Names;
  }

  public List<String> getTier2Names() {
    return tier2Names;
  }

  public List<String> getShadowNames() {
    return shadowNames;
  }

  public static Option<Device> getDeviceByEscalationPolicyId(String escalationPolicyId) {
    return Option.of(ESCALATION_PAGERS.get(escalationPolicyId));
  }

}
