package com.kaching.platform.pagerduty.api.v2;

import org.joda.time.DateTime;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Assignment {

  @JsonProperty("at")
  @JsonDeserialize(using = PagerDutyApiV2DateTimeDeserializer.class)
  private DateTime at;

  @JsonProperty("assignee")
  private Assignee assignee;

  public Assignment() { /* jackson */ }

  public Assignment(DateTime at, Assignee assignee) {
    this.at = at;
    this.assignee = assignee;
  }

  public DateTime getAt() {
    return at;
  }

  public Assignee getAssignee() {
    return assignee;
  }

}
