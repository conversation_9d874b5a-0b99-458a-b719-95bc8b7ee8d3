package com.kaching.platform.pagerduty.api.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Channel {

  @JsonProperty("details")
  private Object details;

  @JsonProperty("type")
  private String type;

  @JsonProperty("summary")
  private String summary;

  @JsonProperty("notification")
  private Notification notification;

  public Channel() { /* json */ }

  public Channel(Object details, String type, String summary, Notification notification) {
    this.details = details;
    this.type = type;
    this.summary = summary;
    this.notification = notification;
  }

  public Object getDetails() {
    return details;
  }

  public String getType() {
    return type;
  }

  public String getSummary() {
    return summary;
  }

  public Notification getNotification() {
    return notification;
  }

  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class NagiosDetails {

    @JsonProperty("CONTACTPAGER")
    private String contactPager;

    @JsonProperty("HOSTALIAS")
    private String hostAlias;

    @JsonProperty("HOSTNAME")
    private String hostname;

    @JsonProperty("NOTIFICATIONTYPE")
    private String notificationType;

    @JsonProperty("SERVICEDESC")
    private String serviceDescription;

    @JsonProperty("SERVICEOUTPUT")
    private String serviceOutput;

    @JsonProperty("SERVICESTATE")
    private String serviceState;

    @JsonProperty("pd_nagios_object")
    private String pdNagiosObject;

    @JsonProperty("pd_version")
    private String pdVersion;

    public String getContactPager() {
      return contactPager;
    }

    public String getHostAlias() {
      return hostAlias;
    }

    public String getHostname() {
      return hostname;
    }

    public String getNotificationType() {
      return notificationType;
    }

    public String getServiceDescription() {
      return serviceDescription;
    }

    public String getServiceOutput() {
      return serviceOutput;
    }

    public String getServiceState() {
      return serviceState;
    }

    public String getPdNagiosObject() {
      return pdNagiosObject;
    }

    public String getPdVersion() {
      return pdVersion;
    }

  }

}
