package com.kaching.platform.queryengine;

import com.kaching.platform.common.logging.Log;
import com.kaching.platform.components.StartupResult;

public abstract class BaseSelfTest {

  private static final Log log = Log.getLog(BaseSelfTest.class);

  public StartupResult start() {
    StartupResult startupResult = null;
    Throwable error = null;
    try {
      startupResult = run();
      return startupResult;
    } catch (Exception e) {
      error = e;
      throw new RuntimeException(e);
    } finally {
      if (error == null) {
        log.info("SelfTest returned: %s", startupResult == null ? "<null>" : startupResult.getStatus().getSignal());
      } else {
        log.error(error, "SelfTest failed");
      }
    }
  }

  protected abstract StartupResult run() throws Exception;

}
