package com.kaching.platform.queryengine;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Streams;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.kaching.platform.converters.Optional;

public class GetClasspathJarNames extends AbstractQuery<String> {

  private final String caseInsensitiveFilter;

  public GetClasspathJarNames(@Optional("") String caseInsensitiveFilter) {
    this.caseInsensitiveFilter = caseInsensitiveFilter;
  }

  @Inject @Named("META-INF.maven-pom.properties-jarNames") List<String> metaInfPomJarNames;

  @Override
  public String process() {
    String lowercaseFilter = caseInsensitiveFilter.toLowerCase(Locale.ROOT);
    Stream<String> classPathJarNames = Arrays.stream(getClasspath().split(":"))
        .map(jarPath -> jarPath.substring(jarPath.lastIndexOf("/") + 1));
    return Streams.concat(classPathJarNames, metaInfPomJarNames.stream())
        .distinct()
        .sorted()
        .filter(jarName -> jarName.toLowerCase(Locale.ROOT).contains(lowercaseFilter))
        .collect(Collectors.joining("\n"));
  }

  @VisibleForTesting
  String getClasspath() {
    return System.getProperty("java.class.path");
  }

}
