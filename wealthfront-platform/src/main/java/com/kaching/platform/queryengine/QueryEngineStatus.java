package com.kaching.platform.queryengine;

import static com.kaching.platform.monitoring.VarzValue.VarzType.COUNTER;
import static com.kaching.platform.monitoring.VarzValue.VarzType.GAUGE;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import org.joda.time.DateTime;
import org.weakref.jmx.Managed;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.kaching.platform.common.Option;
import com.kaching.platform.monitoring.RollingMaximum;
import com.kaching.platform.monitoring.Varz;
import com.kaching.platform.monitoring.VarzValue;
import com.kaching.util.FileSize;

@Singleton
@Varz(interval = 1)
public class QueryEngineStatus {

  @Inject(optional = true)
  @Named("disableScheduler")
  boolean disableScheduler;

  @Inject(optional = true)
  Provider<JettyScheduledQueryExecutorService> jettyScheduledQueryExecutorService;

  AtomicLong processedQueries = new AtomicLong(0L);
  AtomicLong failedQueries = new AtomicLong(0L);
  AtomicLong invalidQueries = new AtomicLong(0L);
  AtomicLong schedulerCounter = new AtomicLong(0L);
  Runtime runtime = Runtime.getRuntime();
  private RollingMaximum runningQueriesMaximum;
  AtomicInteger currentNumberOfRunningQueries = new AtomicInteger(0);

  @Inject
  QueryEngineStatus(Provider<DateTime> clock) {
    runningQueriesMaximum = new RollingMaximum(10, TimeUnit.SECONDS, clock);
  }

  @VarzValue(COUNTER)
  public long getProcessedQueries() {
    return processedQueries.get();
  }

  @VarzValue(COUNTER)
  public long getFailedQueries() {
    return failedQueries.get();
  }

  @VarzValue(COUNTER)
  public long getInvalidQueries() {
    return invalidQueries.get();
  }

  @VarzValue(GAUGE)
  public double getQuerySuccessRatio() {
    if (getProcessedQueries() == 0) {
      return 1.0;
    }
    return (double) (getProcessedQueries() - getFailedQueries())
        / (double) getProcessedQueries();
  }

  @VarzValue(COUNTER)
  public long getSchedulerCounter() {
    return schedulerCounter.get();
  }

  private Option<JettyScheduledQueryExecutorService> maybeGetPool() {
    if (jettyScheduledQueryExecutorService == null) {
      return Option.none();
    }
    return Option.of(jettyScheduledQueryExecutorService.get());
  }

  @VarzValue(GAUGE)
  public int getActiveThreadCount() {
    return maybeGetPool().transform(JettyScheduledQueryExecutorService::getActiveThreads).getOrElse(0);
  }

  @VarzValue(GAUGE)
  public int getIdleThreadCount() {
    return maybeGetPool().transform(JettyScheduledQueryExecutorService::getIdleThreads).getOrElse(0);
  }

  @VarzValue(GAUGE)
  public int getPoolSize() {
    return maybeGetPool().transform(JettyScheduledQueryExecutorService::getThreads).getOrElse(0);
  }

  @VarzValue(GAUGE)
  public int getMaximumPoolSize() {
    return maybeGetPool().transform(JettyScheduledQueryExecutorService::getMaximumPoolSize).getOrElse(0);
  }

  @VarzValue(GAUGE)
  public int getLeasedThreadCount() {
    return Math.max(getActiveThreadCount() - getNumberOfRunningQueries(), 0);
  }

  void recordNumberOfRunningQueries(int numberOfRunningQueries) {
    runningQueriesMaximum.record(numberOfRunningQueries);
    currentNumberOfRunningQueries.set(numberOfRunningQueries);
  }

  @VarzValue(GAUGE)
  public int getRollingMaximumNumberOfRunningQueries() {
    return runningQueriesMaximum.getRollingMaximum();
  }

  @VarzValue(GAUGE)
  public int getNumberOfRunningQueries() {
    return currentNumberOfRunningQueries.get();
  }

  @Managed
  public boolean getSchedulerEnabled() {
    return !disableScheduler;
  }

  public String getUsedMemory() {
    return FileSize.toHumanReadableString(runtime.totalMemory()
        - runtime.freeMemory());
  }

  public String getFreeMemory() {
    return FileSize.toHumanReadableString(runtime.freeMemory());
  }

}
