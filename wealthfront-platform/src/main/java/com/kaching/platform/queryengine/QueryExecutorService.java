package com.kaching.platform.queryengine;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import com.kaching.platform.discovery.ServiceKind;

/**
 * Schedules and provides a future to access the result of executing a query.
 */
public interface QueryExecutorService extends QueryExecutor {

  class QueryResult<Q extends Query<T>, T> {

    private final Q query;
    private final T result;
    private final Throwable error;

    private QueryResult(Q query, T result, Throwable error) {
      this.query = query;
      this.result = result;
      this.error = error;
    }

    public Q getQuery() {
      return query;
    }

    public boolean hasResult() {
      return result != null;
    }

    public T getResult() {
      return result;
    }

    public boolean hasError() {
      return error != null;
    }

    public Throwable getError() {
      return error;
    }

    public static <Q extends Query<T>, T> QueryResult<Q, T> success(Q query, T result) {
      return new QueryResult<>(query, result, null);
    }

    public static <Q extends Query<T>, T> QueryResult<Q, T> error(Q query, Throwable error) {
      return new QueryResult<>(query, null, error);
    }

  }

  class QueryKeyResult<K, Q extends Query<T>, T> {

    private final Class<? extends Q> queryClass;
    private final K key;
    private final T result;
    private final Throwable error;

    private QueryKeyResult(K key, Class<? extends Q> queryClass, T result, Throwable error) {
      this.key = key;
      this.queryClass = queryClass;
      this.result = result;
      this.error = error;
    }

    public Class<? extends Q> getQueryClass() {
      return queryClass;
    }

    public K getKey() {
      return key;
    }

    public boolean hasResult() {
      return result != null;
    }

    public T getResult() {
      return result;
    }

    public T getResultOrThrow() {
      if (hasResult()) {
        return result;
      } else {
        throw new RuntimeException("failed for " + key, error);
      }
    }

    public boolean hasError() {
      return error != null;
    }

    public Throwable getError() {
      return error;
    }

    public static <K, Q extends Query<T>, T> QueryKeyResult<K, Q, T> success(K key, Class<? extends Q> queryClass,
                                                                             T result) {
      return new QueryKeyResult<>(key, queryClass, result, null);
    }

    public static <K, Q extends Query<T>, T> QueryKeyResult<K, Q, T> error(K key, Class<? extends Q> query,
                                                                           Throwable error) {
      return new QueryKeyResult<>(key, query, null, error);
    }

  }

  /**
   * Submits a query for execution and returns a {@link Future} representing the
   * pending result of the query. The Future's {@link Future#get} method will
   * return the query's result upon completion.
   *
   * @param query the query to submit
   * @return a Future representing pending completion of the query
   * @see QueryExecutorServices#getOrFail(Future) if you hate checked exceptions
   */
  <T> CompletableFuture<T> submit(Query<T> query);

  @Deprecated
  default <T, K extends ServiceKind> T submit(StubQuery<T, K> query) {
    throw new UnsupportedOperationException("Cannot invoke stub queries via query executor service");
  }

  /**
   * Submits a query for execution and waits until query has terminated. Has same semantics as
   * {@code QueryExecutorServices.getOrFail(submit(query))}.
   */
  <T> T submitAndGetResult(Query<T> query);

  @Deprecated
  default <T, K extends ServiceKind> T submitAndGetResult(StubQuery<T, K> query) {
    throw new UnsupportedOperationException("Cannot invoke stub queries via query executor service");
  }

  /**
   * Submits a collection of queries to be executed in parallel and waits for completion
   * of all queries.
   */
  <Q extends Query<T>, T> List<QueryResult<Q, T>>
  submitAndGetResults(Iterable<? extends Q> queries, int maxConcurrentQueries);

  /**
   * Submits a collection of queries to be executed in parallel and returns futures.
   */
  <Q extends Query<T>, T> List<Future<List<QueryResult<Q, T>>>>
  submitAndGetFutures(Iterable<? extends Q> queries, int maxConcurrentQueries);

  <Q extends Query<T>, T, K> List<QueryKeyResult<K, Q, T>>
  submitAndGetResultsWithKey(Map<K, ? extends Q> queriesMap, int maxConcurrentQueries);

  void submitAndWaitForCompletion(Iterable<? extends Query> queries, int maxConcurrentQueries);

  /**
   * Initiates an orderly shutdown in which previously submitted queries are
   * executed, but no new queries will be accepted. Invocation has no additional
   * effect if already shut down.
   */
  void shutdown();

  /**
   * Returns {@code true} if this executor has been shut down.
   *
   * @return {@code true} if this executor has been shut down
   */
  boolean isShutdown();

  /**
   * Returns {@code true} if all queries have completed following shut down.
   * Note that {@code isTerminated} is never {@code true} unless either
   * {@code shutdown} or {@code shutdownNow} was called first.
   *
   * @return {@code true} if all tasks have completed following shut down
   */
  boolean isTerminated();

}
