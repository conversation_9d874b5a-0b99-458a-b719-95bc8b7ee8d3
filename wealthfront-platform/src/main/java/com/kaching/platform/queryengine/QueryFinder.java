package com.kaching.platform.queryengine;

import static com.google.common.collect.ImmutableList.copyOf;
import static com.google.common.collect.Iterators.forEnumeration;
import static com.google.common.collect.Lists.newArrayList;
import static java.lang.Thread.currentThread;
import static java.lang.reflect.Modifier.isAbstract;
import static java.util.Arrays.asList;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.net.URLDecoder;
import java.util.Enumeration;
import java.util.List;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

import com.google.common.annotations.VisibleForTesting;

@VisibleForTesting
public class QueryFinder {

  /**
   * Finds queries in the specified packages. Does not look in subpackages.
   */
  @VisibleForTesting
  public static List<Class<? extends Query<?>>> findQueries(String... packageNames) {
    return findQueries(asList(packageNames));
  }

  /**
   * Finds queries in the specified packages. Does not look in subpackages.
   */
  @SuppressWarnings("unchecked")
  @VisibleForTesting
  public static List<Class<? extends Query<?>>> findQueries(Iterable<String> packageNames) {
    try {
      List<String> classNames = newArrayList();
      for (String packageName : packageNames) {
        String packagePath = packageName.replace('.', '/') + '/';
        List<URL> resources = copyOf(forEnumeration(
            currentThread().getContextClassLoader().getResources(packagePath)));
        for (URL dirUrl : resources) {
          if ("jar".equals(dirUrl.getProtocol())) {
            // dirUrl.getPath() looks like file:foo/bar.jar!/com/package
            String jarPath = dirUrl.getPath().substring(5, dirUrl.getPath().indexOf("!"));
            JarFile jar = new JarFile(URLDecoder.decode(jarPath, "UTF-8"));
            Enumeration<JarEntry> entries = jar.entries();
            while (entries.hasMoreElements()) {
              String entryPath = entries.nextElement().getName();
              if (entryPath.startsWith(packagePath) && entryPath.endsWith(".class")) {
                String entryName = entryPath.substring(packagePath.length());
                if (entryName.indexOf('/') == -1 &&
                    entryName.indexOf('$') == -1) {
                  classNames.add(packageName + '.' + entryName.substring(0, entryName.length() - 6));
                }
              }
            }
          } else {
            for (File file : new File(dirUrl.getPath()).listFiles()) {
              String fileName = file.getName();
              if (fileName.endsWith(".class") &&
                  fileName.indexOf('$') == -1) {
                classNames.add(packageName + '.' + fileName.substring(0, fileName.length() - 6));
              }
            }
          }
        }
      }
      List<Class<? extends Query<?>>> queries = newArrayList();
      for (String className : classNames) {
        Class<?> c = Class.forName(className);
        if (Query.class.isAssignableFrom(c) && !isAbstract(c.getModifiers())) {
          queries.add((Class<? extends Query<?>>) c);
        }
      }
      return queries;
    } catch (ClassNotFoundException | IOException e) {
      throw new RuntimeException(e);
    }
  }

}
