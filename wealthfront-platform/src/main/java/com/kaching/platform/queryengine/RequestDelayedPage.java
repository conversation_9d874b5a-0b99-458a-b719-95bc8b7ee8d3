package com.kaching.platform.queryengine;

import org.joda.time.DateTime;

import com.kaching.platform.guice.KachingServices;
import com.kaching.util.mail.Pager;
import com.kaching.util.mail.QueuedPageMetadata;
import com.kaching.util.mail.QueuedPageView;

public class RequestDelayedPage extends StubQuery<QueuedPageView, KachingServices.ESP> {

  private final String subject;
  private final String message;
  private final Pager.Device device;
  private final QueuedPageMetadata metadata;
  private final DateTime delayedUntil;

  public RequestDelayedPage(
      String subject, String message, Pager.Device device, DateTime delayedUntil, QueuedPageMetadata metadata) {
    this.subject = subject;
    this.message = message;
    this.device = device;
    this.metadata = metadata;
    this.delayedUntil = delayedUntil;
  }

}