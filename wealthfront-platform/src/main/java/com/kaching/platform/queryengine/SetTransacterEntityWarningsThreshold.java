package com.kaching.platform.queryengine;

import com.kaching.platform.hibernate.TransacterImpl;

public class SetTransacterEntityWarningsThreshold extends AbstractQuery<Boolean> {

  private final int threshold;

  public SetTransacterEntityWarningsThreshold(int threshold) {
    this.threshold = threshold;
  }

  @Override
  public Boolean process() {
    TransacterImpl.setEntityWarnThreshold(threshold);
    return true;
  }
}
