package com.kaching.platform.queryengine.admin;

import java.io.IOException;
import java.util.regex.Pattern;

import org.apache.commons.lang.StringUtils;
import org.eclipse.jetty.server.handler.ResourceHandler;
import org.eclipse.jetty.util.resource.Resource;

/**
 * Attempt to magically pull a static resource from the classloader
 * and return it in a format jetty understands.
 * <p/>
 * This is gross.
 */
public class QueryEngineStaticResourceHandler extends ResourceHandler {

  private static final Pattern BLACKLIST = Pattern.compile(".*\\.properties$");

  @Override
  public Resource getResource(String path) {
    if (BLACKLIST.matcher(path).matches()) {
      return null;
    }
    String[] pathParts = StringUtils.split(path, "/");
    if (pathParts.length < 2) {
      return null;
    }
    try {
      //strip out /static, load with classloader
      return Resource.newSystemResource("/" + StringUtils.join(pathParts, "/", 1, pathParts.length));
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }
}
