package com.kaching.platform.queryengine.authorization;

import static com.kaching.platform.common.logging.Log.getLog;
import static com.kaching.platform.queryengine.QueryEngineModule.REQUEST_AUTHORIZATION_TOKEN_KEY;
import static com.kaching.platform.queryengine.authorization.AuthorizationTokenDecryptionResult.Expired;
import static java.util.Objects.isNull;

import org.joda.time.DateTime;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.name.Named;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.queryengine.QueryScope;
import com.kaching.platform.queryengine.QueryScoped;
import com.kaching.platform.queryengine.authorization.AuthorizationTokenDecryptionResult.Invalid;
import com.kaching.platform.queryengine.authorization.AuthorizationTokenDecryptionResult.Missing;
import com.kaching.platform.queryengine.authorization.AuthorizationTokenDecryptionResult.Valid;
import com.kaching.security.Cipher;

@QueryScoped
public class AuthorizationTokenDecryptionResultProvider implements Provider<AuthorizationTokenDecryptionResult> {

  private static final Log log = getLog(AuthorizationTokenDecryptionResultProvider.class);

  @Inject DateTime now;
  @Inject QueryScope scope;
  @Inject @Named("RequestAuthorizationToken") Cipher cipher;

  @Override
  public AuthorizationTokenDecryptionResult get() {
    Option<EncryptedRequestAuthorizationToken> encryptedToken =
        scope.get(REQUEST_AUTHORIZATION_TOKEN_KEY);
    if (isNull(encryptedToken) || encryptedToken.isEmpty()) {
      return new Missing();
    }

    RequestAuthorizationToken decrypted;
    try {
      decrypted = encryptedToken.getOrThrow().decrypt(cipher);
    } catch (Throwable t) {
      log.error(t, "invalid token: %s", encryptedToken.getOrThrow().toString());
      return new Invalid();
    }

    if (decrypted.getExpirationTime().isBefore(now)) {
      return new Expired(decrypted);
    }

    return new Valid(decrypted);
  }

}
