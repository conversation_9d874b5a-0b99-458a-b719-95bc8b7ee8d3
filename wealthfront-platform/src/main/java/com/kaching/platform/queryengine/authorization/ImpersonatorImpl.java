package com.kaching.platform.queryengine.authorization;

import static com.kaching.platform.common.Strings.format;
import static com.kaching.platform.functional.Unit.unit;
import static com.kaching.platform.queryengine.QueryEngineModule.REQUEST_AUTHORIZATION_TOKEN_KEY;

import java.util.concurrent.Callable;

import com.google.common.util.concurrent.RateLimiter;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.discovery.ServiceDescriptor;
import com.kaching.platform.functional.Unchecked;
import com.kaching.platform.functional.Unit;
import com.kaching.platform.queryengine.QueryScope;
import com.kaching.user.UserId;

public class ImpersonatorImpl implements Impersonator {

  private static final Log log = Log.getLog(ImpersonatorImpl.class);

  private static final RateLimiter rateLimiter = RateLimiter.create(0.01);

  @Inject Injector injector;
  @Inject QueryScope scope;
  @Inject RequestAuthorizationTokenGenerator generator;
  @Inject Option<EncryptedRequestAuthorizationToken> token;
  @Inject ServiceDescriptor descriptor;

  @Override
  public void doAs(UserId userId, Object obj, Runnable runnable) {
    doAs(userId, obj.getClass(), runnable);
  }

  @Override
  public void doAs(UserId userId, Class<?> clazz, Runnable runnable) {
    doAs(userId, clazz, runnable, null);
  }

  @Override
  public void doAs(UserId userId, Class<?> clazz, Runnable runnable, String actorUserName) {
    doAs(userId, clazz, () -> {
      runnable.run();
      return unit;
    }, actorUserName);
  }

  @Override
  public <T> T doAs(UserId userId, Object obj, Callable<T> callable) {
    return doAs(userId, obj.getClass(), callable);
  }

  @Override
  public <T> T doAs(UserId userId, Class<?> clazz, Callable<T> callable) {
    return doAs(userId, clazz, callable, null);
  }

  @Override
  public <T> T doAs(UserId userId, Class<?> clazz, Callable<T> callable, String actorUserName) {
    boolean queryScopeWasAlreadyStarted = true;
    try {
      if (!scope.isStartedIgnoringScopeRules()) {
        queryScopeWasAlreadyStarted = false;
        scope.begin();
      }
      impersonate(userId, clazz, Option.of(actorUserName));
      return Unchecked.get(callable::call);
    } finally {
      if (queryScopeWasAlreadyStarted) {
        // revert to the original token, if any
        scope.cache(REQUEST_AUTHORIZATION_TOKEN_KEY, token);
      } else {
        scope.end();
      }
    }
  }

  private void impersonate(UserId userId, Class<?> clazz, Option<String> maybeActorUserName) {
    injector.getInstance(AuthorizationTokenDecryptionResult.class).visit(
        new AuthorizationTokenDecryptionResult.Visitor<Unit>() {
          @Override
          public Unit caseMissing(AuthorizationTokenDecryptionResult.Missing missing) {
            log.debug("Expected use of Impersonator -- did not have a valid token yet");
            return unit;
          }

          @Override
          public Unit caseInvalid(AuthorizationTokenDecryptionResult.Invalid value) {
            log.debug("Expected(?) use of Impersonator -- had an invalid token");
            return unit;
          }

          @Override
          public Unit caseExpired(AuthorizationTokenDecryptionResult.Expired value) {
            if (userId.equals(value.getRequestAuthorizationToken().getAuthorizedUserId())) {
              if (rateLimiter.tryAcquire()) {
                log.warn(new RuntimeException("fake exception to log a stack trace"),
                    "Questionable use of Impersonator -- already had a valid token for " + userId +
                        " but it was expired");
              }
            }
            return unit;
          }

          @Override
          public Unit caseValid(AuthorizationTokenDecryptionResult.Valid value) {
            if (userId.equals(value.getRequestAuthorizationToken().getAuthorizedUserId())) {
              if (rateLimiter.tryAcquire()) {
                log.warn(new RuntimeException("fake exception to log a stack trace"),
                    "Questionable use of Impersonator -- already had a valid token for " + userId);
              }
            }
            return unit;
          }
        }
    );
    scope.cache(
        REQUEST_AUTHORIZATION_TOKEN_KEY,
        Option.of(new EncryptedRequestAuthorizationToken(generator.generate(
            userId, formatActorName(clazz, descriptor, maybeActorUserName), Option.none(), Option.none()))));
  }

  private static String formatActorName(Class<?> clazz, ServiceDescriptor descriptor, Option<String> maybeActorUserName) {
    for (String actorUserName : maybeActorUserName) {
      return format("%s using %s on %s", actorUserName, clazz.getSimpleName(), descriptor.getId());
    }
    return format("%s on %s", clazz.getSimpleName(), descriptor.getId());
  }

}