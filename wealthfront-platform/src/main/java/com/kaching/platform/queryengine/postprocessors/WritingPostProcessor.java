package com.kaching.platform.queryengine.postprocessors;

import java.io.IOException;

import com.google.inject.Inject;
import com.kaching.platform.queryengine.ClearingStreamHolder;
import com.kaching.platform.queryengine.PostProcessor;

/**
 * Post-processes the query's result by first writing it to a character stream
 * and then discarding it.
 */
public abstract class WritingPostProcessor<T> implements PostProcessor {

  @Inject @QueryOutput public ClearingStreamHolder clearingStreamHolder;

  /**
   * Writes the query's result to a ClearingStreamHolder and returns <tt>null</tt>.
   */
  @SuppressWarnings("unchecked")
  public final Object postProcess(Object queryResult) {
    try {
      write((T) queryResult, clearingStreamHolder);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    return null;
  }

  /**
   * Writes the query's result to a ClearingStreamHolder.
   */
  protected abstract void write(T queryResult, ClearingStreamHolder streamHolder)
      throws IOException;

}
