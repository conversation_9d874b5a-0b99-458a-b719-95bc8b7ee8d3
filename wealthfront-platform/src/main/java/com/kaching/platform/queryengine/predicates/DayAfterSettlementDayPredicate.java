package com.kaching.platform.queryengine.predicates;

import static com.wealthfront.util.time.DateTimeZones.ET;
import static com.wealthfront.util.time.DateTimeZones.toLocalDate;

import org.joda.time.DateTime;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.kaching.platform.queryengine.Predicate;
import com.wealthfront.entities.SettlementMarket;

public class DayAfterSettlementDayPredicate implements Predicate {

  @Inject Provider<DateTime> clock;
  @Inject SettlementMarket market;
  
  @Override
  public boolean satisfied() {
    return market.isOpeningDay(toLocalDate(clock.get(), ET).minusDays(1));
  }
  
}
