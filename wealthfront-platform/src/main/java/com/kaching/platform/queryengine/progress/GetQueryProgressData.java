package com.kaching.platform.queryengine.progress;

import java.util.List;

import com.google.inject.Inject;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.QueryRuntimeMonitor;
import com.kaching.platform.queryengine.QuerySession;
import com.kaching.platform.queryengine.admin.Param;
import com.kaching.platform.queryengine.progress.ProgressMonitor.QueryProgress;

public class GetQueryProgressData extends AbstractQuery<List<QueryProgress>> {

  private final String queryNameOrSessionId;

  public GetQueryProgressData(@Param("Query name or session id") String queryNameOrSessionId) {
    this.queryNameOrSessionId = queryNameOrSessionId;
  }

  @Inject QueryRuntimeMonitor runtimeMonitor;
  @Inject QueryProgressFormatter formatter;

  @Override
  public List<QueryProgress> process() {
    List<QuerySession> matchingQuerySessions = runtimeMonitor.getQuerySessions(queryNameOrSessionId);
    return formatter.getAllQueryProgress(matchingQuerySessions);
  }

}
