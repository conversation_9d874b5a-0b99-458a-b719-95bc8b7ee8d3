package com.kaching.platform.sentry;

import java.util.List;

import com.kaching.Author;
import com.kaching.platform.common.Option;

import io.sentry.SentryClient;

public class EmptySentryClientProvider implements SentryClientProvider {

  @Override
  public Option<SentryClient> get(Author author) {
    return Option.none();
  }

  @Override
  public Option<SentryClient> get(List<Author> authors) {
    return Option.none();
  }

}
