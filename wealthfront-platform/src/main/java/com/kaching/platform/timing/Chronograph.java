package com.kaching.platform.timing;

import java.util.concurrent.Callable;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.kaching.platform.functional.Unchecked;

@Singleton
public class Chronograph {

  private final ChronographStats stats;

  @Inject
  public Chronograph(ChronographStats stats) {
    this.stats = stats;
  }

  public ScopedChronographProvider scope(String scope) {
    return new ScopedChronographProvider(scope, stats);
  }

  public RunningChronograph start(String scope, String eventName) {
    return new RunningChronograph(scope, eventName, stats);
  }

  public ScopedChronographProvider scope(Class<?> clazz) {
    return scope(clazz.getName());
  }

  public RunningChronograph start(Class<?> clazz, String eventName) {
    return new RunningChronograph(clazz.getName(), eventName, stats);
  }

  public ScopedChronographProvider scope(Object obj) {
    return scope(obj.getClass());
  }

  public RunningChronograph start(Object obj, String eventName) {
    return new RunningChronograph(obj.getClass().getName(), eventName, stats);
  }

  public <T> T time(Object scope, String eventName, Callable<T> c) {
    return time(scope.getClass(), eventName, c);
  }

  public <T> T time(Class<?> scope, String eventName, Callable<T> c) {
    return time(scope.getName(), eventName, c);
  }

  public <T> T time(String scope, String eventName, Callable<T> c) {
    RunningChronograph sw = start(scope, eventName);
    try {
      return Unchecked.get(c::call);
    } finally {
      sw.stop();
    }
  }

  public void time(Object scope, String eventName, Runnable r) {
    time(scope.getClass(), eventName, r);
  }

  public void time(Class<?> scope, String eventName, Runnable r) {
    time(scope.getName(), eventName, r);
  }

  public void time(String scope, String eventName, Runnable r) {
    RunningChronograph sw = start(scope, eventName);
    try {
      Unchecked.run(r::run);
    } finally {
      sw.stop();
    }
  }

  public static class ScopedChronographProvider {

    private final String scope;
    private final ChronographStats stats;

    public ScopedChronographProvider(String scope, ChronographStats stats) {
      this.scope = scope;
      this.stats = stats;
    }

    public RunningChronograph start(String eventName) {
      return new RunningChronograph(scope, eventName, stats);
    }

    @VisibleForTesting
    public String getScope() {
      return scope;
    }

  }

  @VisibleForTesting
  public static Chronograph forTesting() {
    return new Chronograph(new ChronographStats());
  }
}
