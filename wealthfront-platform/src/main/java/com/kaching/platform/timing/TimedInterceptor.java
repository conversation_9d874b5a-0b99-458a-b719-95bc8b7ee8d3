package com.kaching.platform.timing;

import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;

import com.google.inject.Inject;

public class TimedInterceptor implements MethodInterceptor {

  @Inject Chronograph chronograph;

  @Override
  public Object invoke(final MethodInvocation invocation) throws Throwable {
    Class<?> cls = invocation.getMethod().getDeclaringClass();
    RunningChronograph sw = chronograph.start(cls, invocation.getMethod().getName());
    try {
      return invocation.proceed();
    } finally {
      sw.stop();
    }
  }

}
