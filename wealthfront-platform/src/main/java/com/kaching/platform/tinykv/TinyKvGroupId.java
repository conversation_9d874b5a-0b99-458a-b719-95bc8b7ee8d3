package com.kaching.platform.tinykv;

import java.util.function.Function;

import com.kaching.platform.common.AbstractIntIdentifier;
import com.kaching.platform.converters.ConvertedBy;
import com.kaching.util.id.IntIdentifiers;
import com.twolattes.json.MarshalledBy;

@ConvertedBy(TinyKvGroupId.Converter.class)
@MarshalledBy(TinyKvGroupId.JsonType.class)
public class TinyKvGroupId extends AbstractIntIdentifier {

  public TinyKvGroupId(int id) {
    super(id);
  }
  
  public static class Converter extends IntIdentifiers.IntConverter<TinyKvGroupId> {

    @Override
    protected Function<Integer, TinyKvGroupId> constructor() {
      return TinyKvGroupId::new;
    }
    
  }
  
  public static class JsonType extends IntIdentifiers.IntJsonType<TinyKvGroupId> {
    @Override
    protected Function<Integer, TinyKvGroupId> constructor() {
      return TinyKvGroupId::new;
    }
  }
  
}
