package com.kaching.platform.tinykv;

import static com.google.common.collect.ImmutableListMultimap.toImmutableListMultimap;
import static com.google.common.collect.ImmutableMap.toImmutableMap;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;

import com.google.common.collect.ListMultimap;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Thunk;

public enum TinyKvStoreGroup {
  
  // For testing
  TEST_GROUP_1(1),
  TEST_GROUP_2(2),
  
  // Production use cases
  ROUTING_NUMBER_BANK_NAME_STORE(3),
  LOCAL_INSTRUMENT_MASTER(4),
  CORPORATE_ACTION_DETAILS(5),
  EXPERIMENT_FRAMEWORK(6),
  GREEN_DOT_SCHEDULED_DOWNTIME(7);
  
  private final int groupId;
  
  private static final Map<TinyKvGroupId, TinyKvStoreGroup> IDS_TO_GROUPS = Arrays.stream(values())
      .collect(toImmutableMap(TinyKvStoreGroup::getGroupId, Function.identity())); 
  
  public static final Thunk<ListMultimap<TinyKvGroupId, TinyKvStoreId>> GROUPS_TO_STORES = Thunk.thunk(() -> Arrays.stream(TinyKvStoreName.values())
      .collect(toImmutableListMultimap(name -> name.getGroup().getGroupId(), TinyKvStoreName::getStoreId)));
  
  TinyKvStoreGroup(int groupId) {
    this.groupId = groupId;
  }
  
  public TinyKvGroupId getGroupId() {
    return new TinyKvGroupId(groupId);
  }
  
  public Option<TinyKvStoreGroup> getById(TinyKvGroupId id) {
    return Option.of(IDS_TO_GROUPS.get(id));
  }

}
