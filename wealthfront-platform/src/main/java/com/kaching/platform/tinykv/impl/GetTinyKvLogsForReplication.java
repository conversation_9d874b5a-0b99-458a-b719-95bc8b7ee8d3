package com.kaching.platform.tinykv.impl;

import static com.google.common.collect.MoreCollectors.onlyElement;
import static com.kaching.util.Preconditions.checkQueryArgument;
import static com.twolattes.json.Entity.RecordStyle.ARRAY_UNNAMED_FIELDS;

import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

import com.google.common.collect.Range;
import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.hibernate.WithReadOnlySessionExpression;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.exceptions.NotFoundException;
import com.kaching.platform.tinykv.TinyKvGroupId;
import com.kaching.platform.tinykv.TinyKvLogId;
import com.kaching.platform.tinykv.TinyKvStoreId;
import com.kaching.platform.util.Ranges;
import com.kaching.platform.util.WrappedBytes;
import com.kaching.util.Base64;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

public class GetTinyKvLogsForReplication extends AbstractQuery<GetTinyKvLogsForReplication.Result> {
  
  private final TinyKvGroupId groupId;
  private final TinyKvLogId fromIncl;
  private final TinyKvLogId toExcl;

  public GetTinyKvLogsForReplication(TinyKvGroupId groupId, TinyKvLogId fromIncl, TinyKvLogId toExcl) {
    this.groupId = groupId;
    this.fromIncl = fromIncl;
    this.toExcl = toExcl;
  }
  
  @Inject TinyKvRoles roles;
  @Inject Transacter transacter;

  @Override
  public Result process() {
    if (roles.getLogStorageRole(groupId) != TinyKvRoles.LogStorage.FULL_LOGS) {
      throw new IllegalArgumentException(Strings.format("This service does not have full logs for TinyKv group %s. Has %s",
          groupId, roles.getLogStorageRole(groupId)));
    }
    checkQueryArgument(fromIncl.compareTo(TinyKvLogId.FIRST_ID) >= 0, "fromId must be at least %s: %s", TinyKvLogId.FIRST_ID, fromIncl);
    TinyKvLogList logs = transacter.execute(new WithReadOnlySessionExpression<>() {

      @Inject TinyKvRawReader rawReader;

      @Override
      public TinyKvLogList run(DbSession session) {
        Option<TinyKvLogId> latestOffset = rawReader.getOffset(groupId);
        if (latestOffset.isEmptyOr(offset -> toExcl.asLong() - 1 > offset.asLong())) {
          throw new IllegalArgumentException(Strings.format("Requested logs %s to %s but only have up to %s", fromIncl, toExcl, latestOffset.getOrNull()));
        }
        return rawReader.getLogs(groupId, fromIncl, toExcl).getOrThrow(new NotFoundException("No logs found for store " + groupId));
      }

    });
    checkQueryArgument(logs.getIdFromIncl().equals(fromIncl), "Expected logs starting at %s but got %s", fromIncl, logs.getIdFromIncl());
    checkQueryArgument(logs.getIdToExcl().equals(toExcl), "Expected logs ending at %s but got %s", toExcl, logs.getIdToExcl());
    return Result.fromLogs(logs);
  }
  
  @Entity(recordStyle = ARRAY_UNNAMED_FIELDS)
  record LogForReplication(TinyKvStoreId storeId, String key, String valueBase64) {}

  @Entity
  public record Result(@Value(nullable = false) TinyKvGroupId groupId, 
                       @Value(nullable = false) TinyKvLogId fromIncl,
                       @Value(nullable = false) TinyKvLogId toExcl, 
                       @Value(nullable = false) List<LogForReplication> logs) {

    public TinyKvLogList toLogs() {
      Range<TinyKvLogId> range = Range.closedOpen(fromIncl, toExcl);
      checkQueryArgument(Ranges.size(TinyKvLogId.DISCRETE_DOMAIN, range) == logs.size(),
          "Size mismatch: %s != %s", Ranges.size(TinyKvLogId.DISCRETE_DOMAIN, range), logs.size());
      AtomicLong id = new AtomicLong(fromIncl.asLong());
      List<TinyKvLog> mapped = logs.stream()
          .map(log -> {
            Option<WrappedBytes> bytes = Option.of(log.valueBase64())
                .transform(Base64::decode)
                .transform(WrappedBytes::wrap);
            return new TinyKvLog(new TinyKvLogId(id.getAndIncrement()), groupId, log.storeId(), log.key(), bytes.isEmpty(), null, bytes);
          }).toList();
      return TinyKvLogList.fromLogs(mapped);
    }
    
    public static Result fromLogs(TinyKvLogList logs) {
      List<LogForReplication> mapped = logs.getLogs()
          .stream()
          .map(log -> new LogForReplication(log.storeId(), log.storeKey(), log.isDelete() ? null : Base64.encodeBytes(log.value().getOrThrow().getBytes())))
          .toList();
      TinyKvGroupId groupId = logs.getLogs()
          .stream()
          .map(TinyKvLog::groupId)
          .distinct()
          .collect(onlyElement());
      return new Result(groupId, logs.getIdFromIncl(), logs.getIdToExcl(), mapped);
    }
  }

}
