package com.kaching.platform.tinykv.impl;

import java.util.List;

import com.google.common.collect.Range;
import com.google.inject.Inject;
import com.kaching.platform.functional.Unit;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.tinykv.TinyKvGroupId;
import com.kaching.platform.tinykv.TinyKvLogId;
import com.twolattes.json.Entity;

public class InvalidateTinyKvCache extends AbstractQuery<Unit> {
  
  @Entity
  public record StoreAndKeyList(List<StoreAndKey> keys) {}

  private final TinyKvGroupId groupId;
  private final TinyKvLogId fromIncl;
  private final TinyKvLogId toExcl;
  private final StoreAndKeyList keys;

  public InvalidateTinyKvCache(TinyKvGroupId groupId, TinyKvLogId fromIncl, TinyKvLogId toExcl, StoreAndKeyList keys) {
    this.groupId = groupId;
    this.fromIncl = fromIncl;
    this.toExcl = toExcl;
    this.keys = keys;
  }
  
  @Inject TinyKvReadCache readCache;
  @Inject AllTinyKvLogWorkers allWorkers;

  @Override
  public Unit process() {
    readCache.invalidate(groupId, Range.closedOpen(fromIncl, toExcl), keys.keys());
    allWorkers.notifyWorkersOfNewOffset(groupId, toExcl.plus(-1));
    return Unit.unit;
  }
  
}
