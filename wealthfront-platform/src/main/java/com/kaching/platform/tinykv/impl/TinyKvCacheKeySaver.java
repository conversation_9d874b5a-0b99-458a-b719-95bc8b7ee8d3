package com.kaching.platform.tinykv.impl;

import java.util.Map;

import com.google.inject.ImplementedBy;
import com.kaching.platform.tinykv.TinyKvStoreId;

@ImplementedBy(TinyKvCacheKeySaverImpl.class)
public interface TinyKvCacheKeySaver {
  
  void saveCachedKeys(TinyKvStoreId storeId, Iterable<String> keys);

  Iterable<String> getSavedKeys(TinyKvStoreId storeId);

  Map<TinyKvStoreId, Integer> getNumBytesSaved();
}
