package com.kaching.platform.tinykv.impl;

import static com.google.common.base.Preconditions.checkState;
import static com.kaching.platform.tinykv.TinyKvComponent.TINYKV_INTER_SERVICE_CLIENT_ANNOTATION;
import static com.kaching.platform.tinykv.impl.TinyKvReplicationStateFetcher.State.REPLICATION_ON;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

import org.joda.time.Duration;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Range;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Provider;
import com.google.inject.name.Named;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.discovery.Leader;
import com.kaching.platform.discovery.LocalAnnouncement;
import com.kaching.platform.discovery.ResolutionException;
import com.kaching.platform.discovery.ServiceDatabase;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.WithReadOnlySessionExpression;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.platform.queryengine.client.BaseSmartClientModule;
import com.kaching.platform.queryengine.client.SmartClient;
import com.kaching.platform.tinykv.TinyKvGroupId;
import com.kaching.platform.tinykv.TinyKvLogId;
import com.kaching.util.BackoffSleeperCreator;
import com.kaching.util.BackoffSleeperCreator.BackoffSleeper;
import com.kaching.util.RandomSleeper;
import com.kaching.util.UncheckedInterruptedException;

public class TinyKvLogWorkerImpl implements TinyKvLogWorker {
  
  private static final Log log = Log.getLog(TinyKvLogWorkerImpl.class);
  
  private static final Duration WAIT_FOR_ANNOUNCEMENT_SLEEP = Duration.standardSeconds(10);
  private static final List<Duration> EXCEPTION_SLEEP_PROGRESSION = ImmutableList.of(
      Duration.standardSeconds(5), 
      Duration.standardSeconds(10),
      Duration.standardSeconds(30),
      Duration.standardSeconds(60)
  ); 
  
  @VisibleForTesting int BATCH_SIZE = 1024;
  @VisibleForTesting int SEND_RESOLUTION_EXCEPTIONS_TO_STM_EVERY = 30;
  
  @VisibleForTesting final ReentrantLock lock = new ReentrantLock(false);
  @VisibleForTesting final Condition offsetChanged = lock.newCondition();

  private volatile boolean initialized = false;
  @VisibleForTesting volatile Long localOffset;
  @VisibleForTesting volatile Long remoteOffset;
  
  @VisibleForTesting final TinyKvGroupId groupId;
  @VisibleForTesting final ServiceDatabase remoteDatabase;
  @VisibleForTesting final Class<? extends ServiceKind> remoteService;

  public TinyKvLogWorkerImpl(TinyKvGroupId groupId, ServiceDatabase remoteDatabase, Class<? extends ServiceKind> remoteService) {
    this.groupId = groupId;
    this.remoteDatabase = remoteDatabase;
    this.remoteService = remoteService;
  }
  
  @Inject RandomSleeper sleeper;
  @Inject Injector injector;
  @Inject StackTraceMonitor stm;
  @Inject RetryingTransacter transacter;
  @Inject @Named("tinykv") ServiceDatabase localDatabase;
  @Inject TinyKvReplicationStateFetcher stateFetcher;
  @Inject LocalAnnouncement localAnnouncement;
  @Inject @Leader Provider<Boolean> isLeader;
  @Inject BackoffSleeperCreator sleeperCreator;
  
  @Override
  public void runForever() {
    runNumTimes(Long.MAX_VALUE);
  }
  
  @VisibleForTesting
  void runNumTimes(long n) {
    BackoffSleeper backoffSleeper = sleeperCreator.createWithSequence(EXCEPTION_SLEEP_PROGRESSION);
    int numResolutionExceptions = 0;
    for (long i = 0; i < n; i++) {
      try {
        runOnce();
        backoffSleeper.reset();
        numResolutionExceptions = 0;
      } catch (UncheckedInterruptedException ex) {
        stm.add(ex);
        throw ex;
      } catch (ResolutionException ex) {
        numResolutionExceptions++;
        log.error(ex, "Remote service %s down. Num exceptions: %s", remoteService, numResolutionExceptions);
        if (numResolutionExceptions % SEND_RESOLUTION_EXCEPTIONS_TO_STM_EVERY == 0) {
          long seconds = SEND_RESOLUTION_EXCEPTIONS_TO_STM_EVERY * WAIT_FOR_ANNOUNCEMENT_SLEEP.getStandardSeconds();
          stm.add(new ResolutionException(Strings.format("TinyKvLogWorker for store %s has been unable to send updates to %s for over %s seconds. " +
              "%s's copy of the store is likely stale.", groupId, KachingServices.abbreviationOf(remoteService), seconds, remoteDatabase)));
        }
        sleeper.sleep(WAIT_FOR_ANNOUNCEMENT_SLEEP);
      } catch (Exception ex) {
        stm.add(ex);
        log.error(ex, "Unexpected exception in runOnce. Will continue.");
        backoffSleeper.sleepRandom();
      }
    }
  }
  
  @Override
  public void notifyNewOffset(TinyKvLogId newLocalOffset) {
    lock.lock();
    try {
      if (localOffset == null || newLocalOffset.asLong() > localOffset) {
        localOffset = newLocalOffset.asLong();
        offsetChanged.signalAll();
      }
    } finally {
      lock.unlock();
    }
  }
  
  @Override
  public String getName() {
    return "TinyKvWorker-" + groupId + "-" + remoteDatabase;
  }
  
  @VisibleForTesting
  void runOnce() {
    while (!calculateShouldRun()) {
      sleeper.sleep(WAIT_FOR_ANNOUNCEMENT_SLEEP);
    }
    lock.lock();
    try {
      if (!initialized) {
        initialize();
      }
    } finally {
      lock.unlock();
    }
    Range<TinyKvLogId> workToDo = getWorkToDoBlocking();
    ReceiveTinyKvUpdate.Result result = getClient().invoke(new ReceiveTinyKvUpdate(groupId, workToDo.lowerEndpoint(), workToDo.upperEndpoint()));
    remoteOffset = result.offset() == null ? null : result.offset().asLong();
    checkState(result.success(), "Failed to push update for store %s: %s. Will retry, but this should be investigated.", groupId, workToDo);
  }

  private void initialize() {
    remoteOffset = getClient().invoke(new GetTinyKvDatabaseLogOffset(groupId)).transform(TinyKvLogId::getId).getOrNull();
    localOffset = readLocalOffset().transform(TinyKvLogId::getId).getOrNull();
    initialized = true;
    log.info("Successfully initialized worker for store %s. Local offset: %s on %s, remote offset: %s on %s", groupId, localOffset,
        localDatabase, remoteOffset, remoteDatabase);
  }

  private Option<TinyKvLogId> readLocalOffset() {
    return transacter.execute(new WithReadOnlySessionExpression<Option<TinyKvLogId>>() {
      @Inject TinyKvRawReader rawReader;

      @Override
      public Option<TinyKvLogId> run(DbSession session) {
        return rawReader.getOffset(groupId);
      }
    });
  }

  @VisibleForTesting
  Range<TinyKvLogId> getWorkToDoBlocking() {
    lock.lock();
    try {
     validateOffsetCoherence();
     while (Objects.equals(localOffset, remoteOffset)) {
       offsetChanged.await();
     }
     validateOffsetCoherence();
     long startIncl = remoteOffset == null ? TinyKvLogId.FIRST_ID.asLong() : remoteOffset + 1L;
     long endIncl = localOffset;
     endIncl = Math.min(endIncl, startIncl + BATCH_SIZE - 1);
     return Range.closed(new TinyKvLogId(startIncl), new TinyKvLogId(endIncl)).canonical(TinyKvLogId.DISCRETE_DOMAIN);
    } catch (InterruptedException ex) {
      throw new UncheckedInterruptedException(ex);
    } finally {
      lock.unlock();
    }
  }
  
  private void validateOffsetCoherence() {
    if (remoteOffset != null) {
      checkState(localOffset != null && localOffset >= remoteOffset, "Remote server and db (%s, %s) has offset %s, which is somehow greater than " +
              "the local offset %s on %s, the writer database, for TinyKvStore %s", KachingServices.abbreviationOf(remoteService), remoteDatabase, remoteOffset,
          localOffset, localDatabase, groupId);
    }
  }
  
  private SmartClient<?> getClient() {
    return injector.getInstance(BaseSmartClientModule.getSmartClientKey(remoteService, TINYKV_INTER_SERVICE_CLIENT_ANNOTATION));
  }
  
  @VisibleForTesting
  boolean calculateShouldRun() {
    boolean shouldRun = isLeader.get() && localAnnouncement.isAnnounced() && stateFetcher.getCachedState(groupId) == REPLICATION_ON;
    if (!shouldRun && initialized) {
      localOffset = null;
      remoteOffset = null;
      initialized = false;
    }
    return shouldRun;
  }
  
}
