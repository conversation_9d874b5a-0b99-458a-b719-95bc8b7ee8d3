package com.kaching.platform.tinykv.impl;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkNotNull;
import static com.google.common.collect.ImmutableSet.toImmutableSet;
import static com.google.common.collect.Iterables.getOnlyElement;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import com.google.common.collect.ImmutableSet;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.kaching.platform.common.Thunk;
import com.kaching.platform.discovery.ServiceDatabase;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.tinykv.TinyKvGroupId;
import com.kaching.platform.tinykv.TinyKvStore;
import com.kaching.platform.tinykv.TinyKvStore.ReaderDatabaseGroup;
import com.kaching.platform.tinykv.TinyKvStoreGroup;
import com.kaching.platform.tinykv.TinyKvStoreId;

public class TinyKvRoles {
  
  public enum LogStorage {
    FULL_LOGS,
    KEYS_ONLY,
    NO_LOGS
  }
  
  @Inject Map<TinyKvStoreId, TinyKvStore<?, ?>> configMap;
  @Inject @Named("tinykv") ServiceDatabase database;
  
  private final Thunk<Set<TinyKvStoreId>> cacheEnabledStores = Thunk.thunk(() -> configMap.values().stream()
      .filter(store -> store.getCacheSettings().isEnabled())
      .map(store -> store.getName().getStoreId())
      .collect(toImmutableSet()));
  
  public LogStorage getLogStorageRole(TinyKvGroupId groupId) {
    boolean isWriterDb = configMap.values().stream()
        .filter(store -> store.getName().getGroup().getGroupId().equals(groupId))
        .anyMatch(store -> store.getWriterGroup().database() == database);
    if (isWriterDb) {
      return LogStorage.FULL_LOGS;
    }
    boolean isFollowerDb = configMap.values().stream()
        .filter(store -> store.getName().getGroup().getGroupId().equals(groupId))
        .flatMap(store -> store.getReaderGroups().stream())
        .anyMatch(readerGroup -> readerGroup.database() == database);
    if (isFollowerDb) {
      return LogStorage.KEYS_ONLY;
    }
    return LogStorage.NO_LOGS;
  }
  
  public Class<? extends ServiceKind> getLogSenderService(TinyKvGroupId groupId) {
    return getOnlyElement(configMap.values().stream()
        .filter(store -> store.getName().getGroup().getGroupId().equals(groupId))
        .map(store -> store.getWriterGroup().logSender())
        .distinct()
        .toList());
  }

  public Map<ServiceDatabase, Class<? extends ServiceKind>> getLogReceiverServices(TinyKvGroupId groupId) {
    Map<ServiceDatabase, Class<? extends ServiceKind>> result = new HashMap<>();
    for (TinyKvStore<?, ?> store : configMap.values()) {
      if (!store.getName().getGroup().getGroupId().equals(groupId)) {
        continue;
      }
      for (ReaderDatabaseGroup readerGroup : store.getReaderGroups()) {
        if (result.containsKey(readerGroup.database())) {
          checkArgument(result.get(readerGroup.database()).equals(readerGroup.logReceiver()),
              "Multiple log receivers for database %s in group %s", readerGroup.database(), groupId);
        } else {
          result.put(readerGroup.database(), readerGroup.logReceiver());
        }
      }
    }
    return result;
  }
  
  public boolean isCacheEnabled(TinyKvStoreId storeId) {
    return cacheEnabledStores.get().contains(storeId);
  }
  
  public Set<Class<? extends ServiceKind>> getAllReadersOnCurrentDb(TinyKvGroupId groupId) {
    ImmutableSet.Builder<Class<? extends ServiceKind>> builder = ImmutableSet.builder();
    for (TinyKvStoreId storeId : TinyKvStoreGroup.GROUPS_TO_STORES.get().get(groupId)) {
      TinyKvStore<?, ?> config = configMap.get(storeId);
      if (config == null) {
        continue;
      }
      config.getReaderGroups().stream()
          .filter(rg -> rg.database() == database)
          .flatMap(rg -> rg.getAllReaders().stream())
          .forEach(builder::add);
      if (config.getWriterGroup().database() == database) {
        builder.addAll(config.getWriterGroup().getAllReaders());
      } 
    }
    return builder.build();
  }
  
  public Set<Class<? extends ServiceKind>> getAllReaderServices(TinyKvGroupId groupId) {
    ImmutableSet.Builder<Class<? extends ServiceKind>> builder = ImmutableSet.builder();
    for (TinyKvStore<?, ?> config : configMap.values()) {
      if (!config.getName().getGroup().getGroupId().equals(groupId)) {
        continue;
      }
      config.getWriterGroup().getAllReaders().forEach(builder::add);
      config.getReaderGroups().stream()
          .flatMap(rg -> rg.getAllReaders().stream())
          .forEach(builder::add);
    }
    return builder.build();
  }
  
  public TinyKvStore<?, ?> getConfig(TinyKvStoreId storeId) {
    return checkNotNull(configMap.get(storeId), "No config for store %s", storeId);
  }

}
