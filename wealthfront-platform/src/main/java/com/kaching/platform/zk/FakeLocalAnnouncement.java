package com.kaching.platform.zk;

import java.io.IOException;

import com.kaching.platform.common.Option;
import com.kaching.platform.discovery.LocalAnnouncement;
import com.kaching.platform.discovery.Status;

public class FakeLocalAnnouncement implements LocalAnnouncement {

  private final boolean isAnnounced;

  public FakeLocalAnnouncement() {
    this(true);
  }

  public FakeLocalAnnouncement(boolean isAnnounced) {
    this.isAnnounced = isAnnounced;
  }

  @Override
  public boolean isAnnounced() {
    return isAnnounced;
  }

  @Override
  public void start() {
    throw new UnsupportedOperationException();
  }

  @Override
  public void stop() {
    throw new UnsupportedOperationException();
  }

  @Override
  public void announce() throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void unannounce() {
    throw new UnsupportedOperationException();
  }

  @Override
  public int isConnected() {
    throw new UnsupportedOperationException();
  }

  @Override
  public void setStatus(Status status) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void setStatus(Status status, Option<String> maybeFailureReason) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Status getStatus() {
    throw new UnsupportedOperationException();
  }

}
