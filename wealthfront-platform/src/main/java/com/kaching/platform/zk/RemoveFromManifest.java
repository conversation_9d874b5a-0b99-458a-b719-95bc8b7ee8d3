package com.kaching.platform.zk;

import static com.kaching.DefaultKachingMarshallers.createEntityMarshaller;
import static com.kaching.platform.zk.ZkNodes.MANIFEST;
import static com.kaching.platform.zk.ZkNodes.marshall;

import org.I0Itec.zkclient.ZkClient;

import com.google.inject.Inject;
import com.kaching.platform.discovery.Manifest;
import com.kaching.platform.discovery.ServiceId;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.admin.Param;

public class RemoveFromManifest extends AbstractQuery<Boolean> {

  private final ServiceId id;

  public RemoveFromManifest(
      @Param("id") ServiceId id) throws ClassNotFoundException {
    this.id = id;
  }

  @Inject ZkClient client;
  @Inject Manifest manifest;

  @Override
  public Boolean process() {
    manifest.removeAllocation(id);
    client.writeData(MANIFEST, marshall(createEntityMarshaller(Manifest.class), manifest));
    return true;
  }

}
