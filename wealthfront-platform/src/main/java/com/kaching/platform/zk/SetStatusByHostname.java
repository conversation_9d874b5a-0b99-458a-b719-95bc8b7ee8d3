package com.kaching.platform.zk;

import static com.kaching.DefaultKachingMarshallers.createEntityMarshaller;
import static com.kaching.platform.zk.ZkNodes.announcementPathOf;
import static java.util.stream.Collectors.toSet;

import java.util.Objects;
import java.util.Set;

import org.I0Itec.zkclient.ZkClient;

import com.google.inject.Inject;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.discovery.Discovery;
import com.kaching.platform.discovery.ServiceAllocation;
import com.kaching.platform.discovery.ServiceDescriptor;
import com.kaching.platform.discovery.ServiceId;
import com.kaching.platform.discovery.ServiceIdJsonType;
import com.kaching.platform.discovery.Status;
import com.kaching.platform.queryengine.AbstractQuery;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.api.ExposeTo;

public class SetStatusByHostname extends AbstractQuery<SetStatusByHostname.Result> {

  private static final Log log = Log.getLog(SetStatusByHostname.class);

  private final String hostname;
  private final Status status;

  public SetStatusByHostname(String hostname, Status status) {
    this.hostname = hostname;
    this.status = status;
  }

  @Inject Discovery discovery;
  @Inject ZkClient zkClient;

  @Override
  public Result process() {
    Set<ServiceId> serviceIds = discovery.getManifest()
        .getAllocations().stream()
        .filter(allocation -> hostname.equalsIgnoreCase(allocation.getMachineHost()))
        .map(ServiceAllocation::getId)
        .map(discovery::getDescriptor)
        .filter(Objects::nonNull)
        .peek(serviceDescriptor -> {
          Status previousStatus = serviceDescriptor.getStatus();
          serviceDescriptor.setStatus(status);
          String path = announcementPathOf(serviceDescriptor);
          byte[] bytes = ZkNodes.marshall(createEntityMarshaller(ServiceDescriptor.class), serviceDescriptor);
          zkClient.writeData(path, bytes);
          log.info("Status of %s set from %s to %s", serviceDescriptor.getId(), previousStatus, status);
          discovery.updated(serviceDescriptor);
        })
        .map(ServiceDescriptor::getId)
        .collect(toSet());
    return new Result(serviceIds);
  }

  @Entity
  @ExposeType(value = { ExposeTo.BACKEND }, namespace = RewriteNamespace.DO_NOT_COPY)
  static class Result {

    @Value(type = ServiceIdJsonType.class)
    private Set<ServiceId> serviceIds;

    private Result() {/* JSON */}

    Result(Set<ServiceId> serviceIds) {
      this.serviceIds = serviceIds;
    }

    public Set<ServiceId> getServiceIds() {
      return serviceIds;
    }

  }

}
