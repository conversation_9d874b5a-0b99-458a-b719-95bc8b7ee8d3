package com.kaching.platform.zk;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import org.I0Itec.zkclient.IZkStateListener;
import org.I0Itec.zkclient.ZkClient;
import org.apache.zookeeper.Watcher.Event.KeeperState;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.kaching.platform.common.logging.Log;

@Singleton
class ZkConnectionState {

  private static final Log log = Log.getLog(ZkConnectionState.class);
  final AtomicReference<KeeperState> ref = new AtomicReference<>();
  final AtomicBoolean isStarted = new AtomicBoolean(false);

  @Inject ZkClient client;
  private IZkStateListener listener;

  synchronized void start() {
    if (isStarted.compareAndSet(false, true)) {
      log.info("Starting to listen for zk connection state");
      listener = new IZkStateListener() {
        @Override
        public void handleStateChanged(KeeperState state) throws Exception {
          log.info("ZooKeeper session state changed from %s to %s", ref.get(), state);
          ref.set(state);
        }

        @Override
        public void handleNewSession() throws Exception {}
      };
      client.subscribeStateChanges(listener);
    }
  }

  synchronized void stop() {
    if (isStarted.compareAndSet(true, false)) {
      log.info("Stopping listening for zk connection state");
      client.unsubscribeStateChanges(listener);
    }
  }

  public boolean isConnected() {
    return ref.get() == KeeperState.SyncConnected;
  }
}
