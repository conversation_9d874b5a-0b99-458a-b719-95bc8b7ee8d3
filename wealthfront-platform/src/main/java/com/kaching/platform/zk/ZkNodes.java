package com.kaching.platform.zk;

import static java.lang.String.format;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.util.Collections;
import java.util.List;

import com.kaching.platform.discovery.ServiceDescriptor;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.util.IORuntimeException;
import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;

class ZkNodes {

  static final String ROOT = "/discovery";
  static final String MANIFEST = format("%s/manifest", ROOT);
  static final String ANNOUNCEMENTS = format("%s/announce", ROOT);
  static final String ELECTIONS = format("%s/elections", ROOT);

  static String announcementPathOf(ServiceDescriptor descriptor) {
    return format("%s/%s", ANNOUNCEMENTS, descriptor.getId());
  }

  static String electionPathOf(Class<? extends ServiceKind> kind) {
    return format("%s/%s", ELECTIONS, kind.getSimpleName());
  }

  /*
  * IOExceptions in marshall/unmarshall: The input / output streams pass
  * through APIs that accept real IO objects, and therefore declare and pass
  * through IOExceptions, but in all cases the underlying object is a byte
  * array, which does not in fact ever throw an IOException.
  */
  static <T> T unmarshall(Marshaller<T> marshaller, byte[] data) {
    try {
      return marshaller.unmarshall(Json.read(
          new InputStreamReader(new ByteArrayInputStream(data))));
    } catch (IOException e) {
      throw new IORuntimeException(e);
    }
  }

  static <T> List<T> unmarshallList(Marshaller<T> marshaller, byte[] data) {
    try {
      return data == null ?
          Collections.emptyList() :
          marshaller.unmarshallList((Json.Array) Json.read(
              new InputStreamReader(new ByteArrayInputStream(data))));
    } catch (IOException e) {
      throw new IORuntimeException(e);
    }
  }

  static <T> byte[] marshall(Marshaller<T> marshaller, T object) {
    try {
      ByteArrayOutputStream baos = new ByteArrayOutputStream();
      OutputStreamWriter writer = new OutputStreamWriter(baos);
      marshaller.marshall(object).write(writer);
      writer.flush();
      writer.close();
      return baos.toByteArray();
    } catch (IOException e) {
      throw new IORuntimeException(e);
    }
  }

  static <T> byte[] marshall(Marshaller<T> marshaller, List<T> object) {
    try {
      ByteArrayOutputStream baos = new ByteArrayOutputStream();
      OutputStreamWriter writer = new OutputStreamWriter(baos);
      marshaller.marshallList(object).write(writer);
      writer.flush();
      writer.close();
      return baos.toByteArray();
    } catch (IOException e) {
      throw new IORuntimeException(e);
    }
  }
}
