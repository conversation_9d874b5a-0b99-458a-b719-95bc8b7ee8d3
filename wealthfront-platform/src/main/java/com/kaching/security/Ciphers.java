package com.kaching.security;

import static com.google.common.base.Preconditions.checkArgument;

import java.security.SecureRandom;
import java.util.List;
import java.util.Set;

import com.google.inject.Provider;

/**
 * Factories for creating thread-safe ciphers.
 */
public class Ciphers {

  public static final Set<Class<?>> VALIDATING_CIPHER_TYPES = Set.of(
      Aes.class
  );

  /**
   * Creates a thread-safe {@link Aes} cipher.
   */
  public static Cipher aes(final byte[] key, final SecureRandom random) {
    return new Cipher((Provider<Aes>) () -> new Aes(key, random), Aes.class, key);
  }

  /**
   * Creates a thread-safe {@link Blowfish} cipher.
   */
  public static Cipher blowfish(final byte[] key) {
    return new Cipher((Provider<Blowfish>) () -> new Blowfish(key), Blowfish.class, key);
  }

  /**
   * Creates a thread-safe {@link OpenSslAesCbc} cipher.
   */
  public static Cipher opensslAesCbc(final byte[] key, final int nKeyBits, final SecureRandom random) {
    return new Cipher((Provider<OpenSslAesCbc>) () -> new OpenSslAesCbc(key, nKeyBits, random), OpenSslAesCbc.class, key);
  }

  public static Cipher plaintext() {
    return new Cipher(PlaintextUnsafeCipher::new, PlaintextUnsafeCipher.class, "plaintxt".getBytes());
  }

  public static Cipher cipherGroup(Cipher writeCipher, List<Cipher> readCiphers) {
    checkArgument(isValidatingCipher(writeCipher), "Cipher is not validating. Use validating cipher or provide validator predicate");
    readCiphers.forEach(cipher -> checkArgument(isValidatingCipher(cipher), "Cipher is not validating. Use validating cipher or provide validator predicate"));
    return new Cipher(() -> new CipherGroup(writeCipher, readCiphers), writeCipher.getCipherClass(), writeCipher.getFingerprint());
  }

  public static boolean isValidatingCipher(Cipher cipher) {
    return isValidatingCipher(cipher.getCipherClass());
  }

  public static boolean isValidatingCipher(Class<?> cipherClass) {
    return VALIDATING_CIPHER_TYPES.contains(cipherClass);
  }

}
