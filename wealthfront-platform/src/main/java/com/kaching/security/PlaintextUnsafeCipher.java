package com.kaching.security;

import static com.google.common.base.Charsets.UTF_8;

public class PlaintextUnsafeCipher implements UnsafeCipher {
  @Override
  public byte[] encryptOntoBytes(byte[] message) {
    return message;
  }

  @Override
  public String encrypt(byte[] message) {
    return new String(message, UTF_8);
  }

  @Override
  public byte[] decrypt(byte[] decodedMessage) {
    return decodedMessage;
  }

  @Override
  public byte[] decrypt(String message) {
    return message.getBytes(UTF_8);
  }

}
