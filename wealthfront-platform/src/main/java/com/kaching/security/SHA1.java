package com.kaching.security;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.security.MessageDigest;

import com.google.inject.BindingAnnotation;

/**
 * Annotates SHA1 implementations of {@link MessageDigest}.
 * <AUTHOR>
 * @version $Id$
 */
@Retention(RUNTIME)
@Target({ElementType.FIELD, ElementType.PARAMETER })
@BindingAnnotation
public @interface SHA1 {
}
