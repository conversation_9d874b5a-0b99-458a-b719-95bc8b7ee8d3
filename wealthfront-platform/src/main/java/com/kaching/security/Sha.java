package com.kaching.security;

import static java.security.MessageDigest.getInstance;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * SHA-1 cryptographic hash function.
 *
 * <p>This implementation is thread-safe.
 *
 * @see <a href="http://en.wikipedia.org/wiki/Sha-1">SHA-1 at Wikipedia</a>
 */
public class Sha {

  /**
   * Produces a 160-bit digest of {@code message}.
   */
  public byte[] digest(byte[] message) {
    try {
      MessageDigest sha = getInstance("SHA");
      sha.update(message);
      return sha.digest();
    } catch (NoSuchAlgorithmException e) {
      throw new RuntimeException(e);
    }
  }

}
