package com.kaching.security.pgp;

import static org.bouncycastle.jcajce.provider.symmetric.util.PBE.SHA1;
import static org.bouncycastle.openpgp.PGPUtil.getDecoderStream;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.SecureRandom;
import java.security.SignatureException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Set;

import org.bouncycastle.openpgp.*;
import org.bouncycastle.openpgp.operator.PGPContentSignerBuilder;
import org.bouncycastle.openpgp.operator.PGPContentVerifierBuilderProvider;
import org.bouncycastle.openpgp.operator.PublicKeyDataDecryptorFactory;
import org.bouncycastle.openpgp.operator.bc.BcPGPContentSignerBuilder;
import org.bouncycastle.openpgp.operator.bc.BcPGPContentVerifierBuilderProvider;
import org.bouncycastle.openpgp.operator.bc.BcPGPDataEncryptorBuilder;
import org.bouncycastle.openpgp.operator.bc.BcPublicKeyDataDecryptorFactory;
import org.bouncycastle.openpgp.operator.bc.BcPublicKeyKeyEncryptionMethodGenerator;

import com.google.common.io.ByteStreams;
import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.functional.Unchecked;
import com.kaching.security.pgp.keys.KeyringConfig;
import com.kaching.util.collections.Iterables;
import com.kaching.util.functional.Pointer;

public class PgpReferenceImpl implements PgpReference {

  private final KeyringConfig keyring;

  @Inject
  public PgpReferenceImpl(KeyringConfig keyring) {
    this.keyring = keyring;
  }

  @Override
  public byte[] encrypt(byte[] data, long publicKeyId) {
    return maybeEncryptAndMaybeSign(data, publicKeyId, null);
  }

  @Override
  public byte[] sign(byte[] data, long keyId) {
    return maybeEncryptAndMaybeSign(data, null, keyId);
  }

  @Override
  public byte[] encryptAndSign(byte[] data, long encryptionKeyId, long signingKeyId) {
    return maybeEncryptAndMaybeSign(data, encryptionKeyId, signingKeyId);
  }

  @Override
  public byte[] decrypt(byte[] data) {
    return maybeDecryptAndMaybeVerify(data, Collections.emptySet(), true);
  }

  @Override
  public byte[] verify(byte[] data, Set<Long> requiredSigningKeyIds) {
    return maybeDecryptAndMaybeVerify(data, requiredSigningKeyIds, false);
  }

  @Override
  public byte[] decryptAndVerify(byte[] data, Set<Long> requiredSigningKeyIds) {
    return maybeDecryptAndMaybeVerify(data, requiredSigningKeyIds, true);
  }

  private byte[] maybeDecryptAndMaybeVerify(byte[] data, Set<Long> requiredSigningKeyIds, boolean shouldDecrypt) {
    return Unchecked.get(() -> {
      InputStream decoderStream = getDecoderStream(new ByteArrayInputStream(data));
      PGPObjectFactory pgpFactory = new PGPObjectFactory(decoderStream, keyring.getKeyFingerPrintCalculator());

      ArrayList<PGPSignature> signaturesPtr = new ArrayList<>();
      Pointer<Boolean> didActuallyDecrypt = Pointer.pointer(false);
      byte[] clearText = extractCleartextFromFactories(safeFactoryIterator(pgpFactory), signaturesPtr, didActuallyDecrypt).getOrThrow();

      for (long requiredSigningKeyId : requiredSigningKeyIds) {
        PGPSignature signature = signaturesPtr.stream()
            .filter(sig -> sig.getKeyID() == requiredSigningKeyId)
            .findFirst()
            .orElseThrow(() -> new SignatureException(Strings.format("Signature for signing key %s not found", requiredSigningKeyId)));
        verifySignature(clearText, signature);
      }
      if (shouldDecrypt && !didActuallyDecrypt.get()) {
        throw new IllegalStateException("Expected to have to decrypt data, but was plaintext");
      } else if (!shouldDecrypt && didActuallyDecrypt.get()) {
        throw new IllegalStateException("Unexpectedly had to decrypt data");
      }

      return clearText;
    });
  }

  private void verifySignature(byte[] data, PGPSignature signature) throws Exception {
    PGPPublicKey publicKey = keyring.getPublicKeyRings().getPublicKey(signature.getKeyID());
    if (publicKey == null) {
      throw new SignatureException(Strings.format("Keyring doesn't contain public key to verify %s", signature.getKeyID()));
    }

    PGPContentVerifierBuilderProvider pgpContentVerifierBuilderProvider = new BcPGPContentVerifierBuilderProvider();
    signature.init(pgpContentVerifierBuilderProvider, publicKey);
    signature.verify();
    signature.update(data);
    signature.verify();
  }

  private Option<byte[]> extractCleartextFromFactories(Iterator<?> iterator,
                                                       List<PGPSignature> signatureListPtr,
                                                       Pointer<Boolean> didActuallyDecrypt) {
    return Unchecked.get(() -> {
      Object obj;
      Option<byte[]> clearData = Option.none();
      while (iterator.hasNext()) {
        obj = iterator.next();

        if (obj instanceof PGPEncryptedDataList) {
          PGPEncryptedDataList list = (PGPEncryptedDataList) obj;
          Option<byte[]> result =  extractCleartextFromFactories(list.getEncryptedDataObjects(), signatureListPtr, didActuallyDecrypt);
          clearData = clearData.isDefined() ? clearData : result;

        } else if (obj instanceof PGPPublicKeyEncryptedData) {
          PGPPublicKeyEncryptedData encryptedData = (PGPPublicKeyEncryptedData) obj;
          long encryptionKeyIdUsed = encryptedData.getKeyID();
          String password = keyring.getSecretKeyPassword(encryptionKeyIdUsed)
              .getOrThrow(Strings.format("Don't have password for encryption key: %s", encryptionKeyIdUsed));
          PGPPrivateKey encryptionKey = PgpUtilities.findSecretKey(keyring.getSecretKeyRings(), encryptionKeyIdUsed, password);

          if (encryptedData.isIntegrityProtected() && !encryptedData.verify()) {
            throw new RuntimeException("message failed integrity check");
          }
          didActuallyDecrypt.set(true);

          PublicKeyDataDecryptorFactory publicKeyDataDecryptorFactory = new BcPublicKeyDataDecryptorFactory(encryptionKey);
          InputStream clearText = encryptedData.getDataStream(publicKeyDataDecryptorFactory);
          PGPObjectFactory clearFactory = new PGPObjectFactory(clearText, keyring.getKeyFingerPrintCalculator());
          Option<byte[]> result = extractCleartextFromFactories(safeFactoryIterator(clearFactory), signatureListPtr, didActuallyDecrypt);
          clearData = clearData.isDefined() ? clearData : result;

        } else if (obj instanceof PGPCompressedData) {
          PGPCompressedData compressedData = (PGPCompressedData) obj;
          PGPObjectFactory uncompressedFactory = new PGPObjectFactory(compressedData.getDataStream(), keyring.getKeyFingerPrintCalculator());
          Option<byte[]> result = extractCleartextFromFactories(safeFactoryIterator(uncompressedFactory), signatureListPtr, didActuallyDecrypt);
          clearData = clearData.isDefined() ? clearData : result;

        } else if (obj instanceof PGPOnePassSignatureList) {
          // If we were streaming, and we wanted to do everything in one pass,
          // we would use this sig list to start verifying the literal data that comes next.
          // In the reference impl, we have the luxury of doing everything afterwards with the normal list

        } else if (obj instanceof PGPLiteralData) {
          PGPLiteralData literalData = (PGPLiteralData) obj;
          clearData = Option.some(ByteStreams.toByteArray(literalData.getInputStream()));

        } else if (obj instanceof PGPSignatureList) {
          signatureListPtr.addAll(Iterables.toList((PGPSignatureList) obj));

        } else {
          throw new RuntimeException("Unknown PGP Data type: " + obj.getClass().getSimpleName());
        }
      }
      return clearData;
    });
  }

  private byte[] maybeEncryptAndMaybeSign(byte[] data, Long encryptionKeyId, Long signingKeyId) {
    return Unchecked.get(() -> {
      byte[] signatureHeader = null;
      byte[] signature = null;
      if (signingKeyId != null) {
        String password = keyring.getSecretKeyPassword(signingKeyId)
            .getOrThrow(Strings.format("Don't have password for encryption key: %s", signingKeyId));
        PGPPrivateKey signingKey = PgpUtilities.findSecretKey(keyring.getSecretKeyRings(), signingKeyId, password);
        PGPContentSignerBuilder pgpContentSignerBuilder =
            new BcPGPContentSignerBuilder(signingKey.getPublicKeyPacket().getAlgorithm(), SHA1);
        PGPSignatureGenerator pgpSignatureGenerator = new PGPSignatureGenerator(pgpContentSignerBuilder);
        pgpSignatureGenerator.init(PGPSignature.BINARY_DOCUMENT, signingKey);

        signatureHeader = pgpSignatureGenerator.generateOnePassVersion(false).getEncoded();
        pgpSignatureGenerator.update(data);
        signature = pgpSignatureGenerator.generate().getEncoded();
      }

      PGPLiteralDataGenerator literalDataGenerator = new PGPLiteralDataGenerator();
      ByteArrayOutputStream literalOutput = new ByteArrayOutputStream();
      OutputStream literalWrite = literalDataGenerator.open(literalOutput, PGPLiteralDataGenerator.BINARY,
          PGPLiteralDataGenerator.CONSOLE, data.length, new Date());
      literalWrite.write(data);
      literalWrite.close();
      byte[] literalBytes = literalOutput.toByteArray();

      PGPCompressedDataGenerator compressedDataGenerator =
          new PGPCompressedDataGenerator(PGPCompressedDataGenerator.ZIP);
      ByteArrayOutputStream compressedOutput = new ByteArrayOutputStream();
      OutputStream compressedWrite = compressedDataGenerator.open(compressedOutput);
      if (signingKeyId != null) {
        compressedWrite.write(signatureHeader);
      }
      compressedWrite.write(literalBytes);
      if (signingKeyId != null) {
        compressedWrite.write(signature);
      }
      compressedWrite.close();
      byte[] compressedBytes = compressedOutput.toByteArray();

      if (encryptionKeyId == null) {
        return compressedBytes;
      }

      BcPGPDataEncryptorBuilder builder = new BcPGPDataEncryptorBuilder(PGPEncryptedData.CAST5);
      builder.setSecureRandom(new SecureRandom());
      PGPEncryptedDataGenerator pgpEncryptedDataGenerator = new PGPEncryptedDataGenerator(builder);
      PGPPublicKey encryptionKey = PgpUtilities.findFirstPublicKey(keyring.getPublicKeyRings(),
          key -> key.isEncryptionKey() && key.getKeyID() == encryptionKeyId);
      BcPublicKeyKeyEncryptionMethodGenerator publicKeyKeyEncryptionMethodGenerator =
          new BcPublicKeyKeyEncryptionMethodGenerator(encryptionKey);
      pgpEncryptedDataGenerator.addMethod(publicKeyKeyEncryptionMethodGenerator);

      ByteArrayOutputStream encryptedOutput = new ByteArrayOutputStream();
      OutputStream encryptedWrite = pgpEncryptedDataGenerator.open(encryptedOutput, new byte[1 << 12]);
      encryptedWrite.write(compressedBytes);
      encryptedWrite.close();
      return encryptedOutput.toByteArray();
    });
  }

  private static Iterator<Object> safeFactoryIterator(PGPObjectFactory factory) {
    Pointer<Object> safePeek = Pointer.pointer();
    return new Iterator<Object>() {

      @Override
      public boolean hasNext() {
        safelyPeek();
        return !safePeek.isEmpty();
      }

      @Override
      public Object next() {
        safelyPeek();
        if (safePeek.isEmpty()) {
          throw new NoSuchElementException();
        }
        Object obj = safePeek.get();
        safePeek.set(null);
        return obj;
      }

      private void safelyPeek() {
        if (safePeek.isEmpty()) {
          try {
            safePeek.set(factory.nextObject());
          } catch (Exception ex) {
            // do nothing
          }
        }
      }

    };
  }

}
