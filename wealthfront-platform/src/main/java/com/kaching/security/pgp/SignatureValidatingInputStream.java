package com.kaching.security.pgp;

import static java.util.stream.Collectors.joining;

import java.io.FilterInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.SignatureException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.bouncycastle.openpgp.PGPException;
import org.bouncycastle.openpgp.PGPObjectFactory;
import org.bouncycastle.openpgp.PGPOnePassSignature;
import org.bouncycastle.openpgp.PGPSignature;
import org.bouncycastle.openpgp.PGPSignatureList;

import com.kaching.platform.functional.Unchecked;

/*
  Inspired by Jen<PERSON> Neuhalfen's bouncy-gpg. See https://github.com/neuhalje/bouncy-gpg
  Original license:

  Copyright 2017 Jens Neuhalfen

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
*/
class SignatureValidatingInputStream extends FilterInputStream {

  private final DecryptionState state;
  private final Set<Long> requireSignaturesForKeys;

  SignatureValidatingInputStream(InputStream inputStream, DecryptionState state, Set<Long> requireSignaturesForKeys) {
    super(inputStream);
    this.state = state;
    this.requireSignaturesForKeys = requireSignaturesForKeys;
  }

  @Override
  public int read() throws IOException {
    int data = super.read();
    boolean endOfStream = data == -1;
    if (endOfStream) {
      validateSignature();
    } else {
      state.updateOnePassSignatures((byte) data);
    }
    return data;
  }

  @Override
  public int read(byte[] b) throws IOException {
    return read(b, 0, b.length);
  }

  @Override
  public int read(byte[] b, int off, int len) throws IOException {
    int read = super.read(b, off, len);

    boolean endOfStream = read == -1;
    if (endOfStream) {
      validateSignature();
    } else {
      state.updateOnePassSignatures(b, off, read);
    }
    return read;
  }

  private void validateSignature() {
    Unchecked.run(() -> validateSignatures(state.getSignatureFactory(), state.getOnePassSignatures()));
  }

  private void validateSignatures(PGPObjectFactory factory, Map<Long, PGPOnePassSignature> onePassSignatures) throws Exception {
    PGPSignatureList signatureList = (PGPSignatureList) factory.nextObject();

    if (signatureList == null || signatureList.isEmpty()) {
      throw new PGPException("No signatures found!");
    }

    Set<Long> signaturesNeedingToBeVerified = new HashSet<>(requireSignaturesForKeys);
    for (PGPSignature messageSignature : signatureList) {
      PGPOnePassSignature ops = onePassSignatures.get(messageSignature.getKeyID());

      boolean isHasPubKeyForSignature = ops != null;
      if (isHasPubKeyForSignature) {
        boolean isThisSignatureGood = ops.verify(messageSignature);
        if (isThisSignatureGood) {
          signaturesNeedingToBeVerified.remove(messageSignature.getKeyID());
        }
      }
    }

    if (!signaturesNeedingToBeVerified.isEmpty()) {
      String missingSignatures = signaturesNeedingToBeVerified.stream()
          .map(keyId -> Long.toString(keyId))
          .collect(joining(","));
      throw new SignatureException("The following signatures (key IDs) could not be verified: " + missingSignatures);
    }
  }

  @Override
  public long skip(long n) throws IOException {
    throw new UnsupportedOperationException("Skipping not supported");
  }

  @Override
  public synchronized void mark(int readlimit) {
    throw new UnsupportedOperationException("mark not supported");
  }

  @Override
  public synchronized void reset() throws IOException {
    throw new UnsupportedOperationException("reset not supported");
  }

  @Override
  public boolean markSupported() {
    return false;
  }

  static class DecryptionState {

    private final Map<Long, PGPOnePassSignature> onePassSignatures = new HashMap<>();
    private PGPObjectFactory signatureFactory;

    PGPObjectFactory getSignatureFactory() {
      return signatureFactory;
    }
    
    void setSignatureFactory(PGPObjectFactory signatureFactory) {
      this.signatureFactory = signatureFactory;
    }
    
    void updateOnePassSignatures(byte data) {
      for (PGPOnePassSignature sig : onePassSignatures.values()) {
        sig.update(data);
      }
    }
    
    void updateOnePassSignatures(byte[] b, int off, int len) {
      for (PGPOnePassSignature sig : onePassSignatures.values()) {
        sig.update(b, off, len);
      }
    }
    
    Map<Long, PGPOnePassSignature> getOnePassSignatures() {
      return onePassSignatures;
    }

    void addSignature(PGPOnePassSignature signature) {
      onePassSignatures.put(signature.getKeyID(), signature);
    }

    int numVerifiableSignatures() {
      return onePassSignatures.size();
    }

  }
}
