package com.kaching.supermap.client;

import java.util.Map;
import java.util.Properties;

import org.perf4j.StopWatch;

import com.google.common.base.Function;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.kaching.platform.common.Option;
import com.kaching.platform.guice.Configuration;
import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;

import voldemort.versioning.Versioned;

@Singleton
public class SuperMapEntityIdClientImpl<E extends HibernateEntity, V> implements SuperMapClient<Id<E>, V> {

  private final SuperMapClientWithKeyFunction<Id<E>, V> delegate;

  @Inject
  public SuperMapEntityIdClientImpl(
      @Configuration Properties conf, SuperMapStoreType store, Provider<StopWatch> stopper) {
    delegate = new SuperMapClientWithKeyFunction<>(new SuperMapStringKeyClientImpl<>(conf, store, stopper),
        new Function<Id<E>, String>() {

          @Override
          public String apply(Id<E> input) {
            return String.valueOf(input.getId());
          }
        });
  }

  @Override
  public Versioned<V> get(Id<E> key) {
    return delegate.get(key);
  }

  @Override
  public Map<Id<E>, Versioned<V>> getAll(Iterable<Id<E>> keys) {
    return delegate.getAll(keys);
  }

  @Override
  public void put(Id<E> key, V value) {
    delegate.put(key, value);
  }

  @Override
  public void put(Id<E> key, Versioned<V> value) {
    delegate.put(key, value);
  }

  @Override
  public boolean delete(Id<E> key) {
    return delegate.delete(key);
  }

  @Override
  public Option<Versioned<V>> getIfExists(Id<E> key) {
    return delegate.getIfExists(key);
  }
}
