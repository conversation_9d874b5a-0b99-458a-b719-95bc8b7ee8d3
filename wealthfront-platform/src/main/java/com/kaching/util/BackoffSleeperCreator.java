package com.kaching.util;

import static java.util.stream.Collectors.toList;

import java.util.Arrays;
import java.util.List;

import org.joda.time.Duration;

import com.google.inject.ImplementedBy;

@ImplementedBy(BackoffSleeperCreatorImpl.class)
public interface BackoffSleeperCreator {
  
  BackoffSleeper createWithSequence(List<Duration> durationSequence);

  default BackoffSleeper createWithMillisSequence(int... millis) {
    return createWithSequence(Arrays.stream(millis)
        .mapToObj(Duration::millis)
        .collect(toList()));
  }
  
  interface BackoffSleeper {

    void sleepExact() throws UncheckedInterruptedException;
    
    void sleepRandom() throws UncheckedInterruptedException;
    
    void reset();
    
    Duration getNextSleepDuration();

    boolean isEndOfSequence();
    
  }

}
