package com.kaching.util;

/**
 * File size.
 */
public class FileSize {

  private static final long KB = 1024;
  private static final long MB = KB * KB;
  private static final long GB = KB * MB;

  private final long size;

  /**
   * Create a file size from a number of bytes.
   *
   * @param size the size in bytes
   */
  public FileSize(long size) {
    this.size = size;
  }

  /**
   * Returns a human readable string representing the size.
   */
  public String toHumanReadableString() {
    if (size < KB) {
      return Long.toString(size) + "B";
    } else if (size < MB) {
      return Long.toString(size / KB) + "KB";
    } else if (size < GB) {
      return Long.toString(size / MB) + "MB";
    } else {
      return Long.toString(size / GB) + "GB";
    }
  }

  /**
   * Returns a human readable string representing the size.
   */
  public static String toHumanReadableString(long size) {
    return new FileSize(size).toHumanReadableString();
  }

}
