package com.kaching.util.concurrent;

import java.math.RoundingMode;

import com.google.common.base.Preconditions;
import com.google.common.math.IntMath;
import com.google.common.primitives.Ints;
import com.google.common.util.concurrent.Striped;

public class StripedWrapper<T, L> {
  
  private final int mask;
  private final Striped<L> delegate;

  public StripedWrapper(Striped<L> delegate) {
    Preconditions.checkArgument(delegate.size() <= Ints.MAX_POWER_OF_TWO, "Size must be <= 2^30)");
    this.delegate = delegate;
    this.mask = ceilToPowerOfTwo(delegate.size()) - 1;
  }

  public L get(T key) {
    return delegate.getAt(indexFor(key));
  }

  public L getAt(int index) {
    return delegate.getAt(index);
  }

  public int size() {
    return delegate.size();
  }

  public int indexFor(T key) {
    int hash = smear(key.hashCode());
    return hash & mask;
  }

  private static int ceilToPowerOfTwo(int x) {
    return 1 << IntMath.log2(x, RoundingMode.CEILING);
  }

  private static int smear(int hashCode) {
    hashCode ^= (hashCode >>> 20) ^ (hashCode >>> 12);
    return hashCode ^ (hashCode >>> 7) ^ (hashCode >>> 4);
  }
  
}
