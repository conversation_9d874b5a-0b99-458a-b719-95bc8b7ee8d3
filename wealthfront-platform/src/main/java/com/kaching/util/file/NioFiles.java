package com.kaching.util.file;

import java.nio.file.OpenOption;
import java.nio.file.Path;
import java.util.List;
import java.util.stream.Stream;

public interface NioFiles {
  
  Path write(Path path, byte[] bytes, OpenOption... options);
  
  Path write(Path path, String bytes, OpenOption... options);
  
  byte[] readAllBytes(Path path);
  
  List<String> readAllLines(Path path);

  Stream<String> lines(Path path);
  
}
