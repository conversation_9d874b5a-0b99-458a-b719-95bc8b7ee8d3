package com.kaching.util.http;

import java.io.IOException;
import java.util.List;

import com.kaching.platform.queryengine.Query;

abstract class QueryHttpClient {

  private final QueryStringFormatter formatter;

  QueryHttpClient(QueryStringFormatter formatter) {
    this.formatter = formatter;
  }

  @SuppressWarnings({"unchecked", "rawtypes"})
  protected List<Parameter> formatParameters(
      Class<? extends Query> queryClass, String... parameters) {
    return formatter.format((Class<? extends Query<?>>) queryClass, parameters);
  }

  interface Action {

    byte[] execute() throws IOException;
  }

}
