package com.kaching.util.id;

import com.kaching.platform.common.DataEnvironment;
import com.kaching.platform.common.DataEnvironment.DataEnvironmentVisitor;
import com.kaching.platform.common.Identifier;
import com.kaching.platform.common.Option;

public class FakeAwareLongIdExternalizer<T extends Identifier<Long>> extends AbstractIdentifierExternalizer<T> {

  private final LongIdExternalizer<T> realExternalizer;
  private final FakeLongIdExternalizerWrapper<T> fakeExternalizer;
  private final Class<T> clazz;
  private final String fakePrefix;

  public FakeAwareLongIdExternalizer(
      Class<T> clazz,
      String prefix,
      String password,
      String fakePrefix,
      String fakePassword) {
    this.clazz = clazz;
    this.fakePrefix = fakePrefix;
    this.realExternalizer = new LongIdExternalizer<>(clazz, prefix, password);
    this.fakeExternalizer = new FakeLongIdExternalizerWrapper<>(
        fakePrefix,
        new LongIdExternalizer<>(clazz, prefix, fakePassword));
  }

  @Override
  public String externalize(T internal) {
    throw new UnsupportedOperationException(String.format(
        "%s<%s> does not support the externalize method without DataEnvironment",
        getClass().getSimpleName(), clazz.getSimpleName()));
  }

  @Override
  public External<T> externalizeId(T internal) {
    throw new UnsupportedOperationException(String.format(
        "%s<%s> does not support the externalizeId method without DataEnvironment",
        getClass().getSimpleName(), clazz.getSimpleName()));
  }

  @Override
  T instantiateNewIdentifier(long value) {
    return realExternalizer.instantiateNewIdentifier(value);
  }

  public String externalize(DataEnvironment dataEnvironment, T internal) {
    return dataEnvironment.visit(new DataEnvironmentVisitor<String>() {
      @Override
      public String caseFake() {
        return fakeExternalizer.externalize(internal);
      }

      @Override
      public String caseReal() {
        return realExternalizer.externalize(internal);
      }
    });
  }

  public External<T> externalizeId(DataEnvironment dataEnvironment, T internal) {
    return dataEnvironment.visit(new DataEnvironmentVisitor<External<T>>() {
      @Override
      public External<T> caseFake() {
        return fakeExternalizer.externalizeId(internal);
      }

      @Override
      public External<T> caseReal() {
        return realExternalizer.externalizeId(internal);
      }
    });
  }

  @Override
  public Option<T> internalize(String externalized) {
    if (externalized != null && externalized.startsWith(fakePrefix)) {
      return fakeExternalizer.internalize(externalized);
    } else {
      return realExternalizer.internalize(externalized);
    }
  }

}
