package com.kaching.util.io;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

public class UncheckedIo {

  public static <T> T get(IoSupplier<T> ioSupplier) {
    try {
      return ioSupplier.get();
    } catch (IOException e) {
      throw new UncheckedIOException(e);
    }
  }

  public static <T, R> R apply(T t, IoFunction<T, R> ioFunction) {
    try {
      return ioFunction.apply(t);
    } catch (IOException e) {
      throw new UncheckedIOException(e);
    }
  }

  public static <T> void accept(T t, IoConsumer<T> ioConsumer) {
    try {
      ioConsumer.accept(t);
    } catch (IOException e) {
      throw new UncheckedIOException(e);
    }
  }

  public static void run(IoRunnable ioRunnable) {
    try {
      ioRunnable.run();
    } catch (IOException e) {
      throw new UncheckedIOException(e);
    }
  }

  public static <T> Supplier<T> supplier(IoSupplier<T> supplier) {
    return () -> UncheckedIo.get(supplier);
  }

  public static <T, R> Function<T, R> function(IoFunction<T, R> ioFunction) {
    return t -> UncheckedIo.apply(t, ioFunction);
  }

  public static <T> Consumer<T> consumer(IoConsumer<T> ioConsumer) {
    return t -> UncheckedIo.accept(t, ioConsumer);
  }

  public static Runnable runnable(IoRunnable ioRunnable) {
    return () -> UncheckedIo.run(ioRunnable);
  }

  public interface IoSupplier<T> {
    T get() throws IOException;
  }

  public interface IoFunction<T, R> {
    R apply(T t) throws IOException;
  }

  public interface IoConsumer<T> {
    void accept(T t) throws IOException;
  }

  public interface IoRunnable {
    void run() throws IOException;
  }

}
