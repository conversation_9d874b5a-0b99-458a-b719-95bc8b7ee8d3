package com.kaching.util.mail;

import static com.kaching.platform.common.logging.Log.getLog;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.util.functional.Tuple3;
import com.kaching.util.pagerduty.PagerDutyPager;

public class PagerImpl implements Pager {

  private static final Log log = getLog(PagerImpl.class);

  @Inject PagerDutyPager pagerDuty;
  @Inject PageHelper pageHelper;
  @Inject(optional = true) StackTraceMonitor monitor;

  @Override
  public void alert(String subject, String message, Device device) {
    try {
      pagerDuty.alert(subject, message, device);
    } catch (Exception e) {
      logError(e, subject, message);
    }
    pageHelper.sendMailAndEventsForPage(subject, message, device, Option.none(), Option.none());
  }

  @Override
  public void resolve(String subject, String message, Device device) {
    if (device.getPagerDutyApiKeys().isEmpty()) {
      throw new UnsupportedOperationException("The pagerDuty API key is not defined");
    }
    pagerDuty.resolve(subject, message, device);
  }

  @Override
  public void resolveIfOpen(String subject, String message, Device device) {
    if (device.getPagerDutyApiKeys().isEmpty()) {
      throw new UnsupportedOperationException("The pagerDuty API key is not defined");
    }
    pagerDuty.resolve(subject, message, device);
  }

  @Override
  public void resolveIfOpen(Set<Tuple3<String, String, Device>> alerts) {
    Set<String> errorDevices = new HashSet<>();
    for (Tuple3<String, String, Device> alert : alerts) {
      if (alert._3.getPagerDutyApiKeys().isEmpty()) {
        errorDevices.add(alert._3.toString());
      }
    }
    if (!errorDevices.isEmpty()) {
      throw new UnsupportedOperationException(
          String.format("The pagerDuty API key is not defined for %s", String.join(", ", errorDevices)));
    }
    for (Tuple3<String, String, Device> alert : alerts) {
      pagerDuty.resolve(alert._1, alert._2, alert._3);
    }
  }

  @Override
  public void alert(String subject, String message, Throwable t, Device device) {
    alert(subject, pageHelper.getMessageWithException(message, t), device);
  }

  @Override
  public List<String> getOpenIncidentTitlesBestEffort() {
    return pagerDuty.getOpenIncidentTitlesBestEffort();
  }

  private void logError(Exception e, String subject, String message) {
    log.error(e, "could not send message to pager. %s; %s", subject, message);
    if (monitor != null) {
      monitor.add(e);
    }
  }

}
