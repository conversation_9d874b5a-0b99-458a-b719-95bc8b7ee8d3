package com.kaching.util.parsing;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import com.kaching.platform.converters.Converter;
import com.kaching.util.types.StringConverter;

@Retention(RUNTIME)
@Target({FIELD})
public @interface FixedWidth {

  char CHAR_NOT_SPECIFIED = '\0';

  enum FillOption {
    LEFT_PAD,
    RIGHT_PAD,
    THROW_IF_TOO_SHORT
  }

  enum DeserializationBehavior {
    REQUIRE_FULL_LENGTH,
    AUTO_FILL_WITH_PAD_CHAR
  }

  int startIncl();

  int endExcl();

  FillOption fill() default FillOption.THROW_IF_TOO_SHORT;

  char padChar() default CHAR_NOT_SPECIFIED;

  Class<? extends Converter> converter() default StringConverter.class;

  DeserializationBehavior deserializationBehavior() default DeserializationBehavior.REQUIRE_FULL_LENGTH;

}
