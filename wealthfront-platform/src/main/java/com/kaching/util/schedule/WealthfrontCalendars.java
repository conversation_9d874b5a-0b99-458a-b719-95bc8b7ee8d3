package com.kaching.util.schedule;

import static com.kaching.util.time.Year.year;
import static com.wealthfront.util.time.calendar.CalendarCombinators.and;
import static com.wealthfront.util.time.calendar.CalendarCombinators.named;
import static com.wealthfront.util.time.calendar.CalendarCombinators.not;
import static com.wealthfront.util.time.calendar.CalendarCombinators.or;
import static com.wealthfront.util.time.calendar.CalendarCombinators.predicate;
import static com.wealthfront.util.time.calendar.CalendarCombinators.startYearFrom;
import static com.wealthfront.util.time.calendar.Calendars.ALWAYS;
import static com.wealthfront.util.time.calendar.Calendars.DAY_AFTER_THANKSGIVING;
import static com.wealthfront.util.time.calendar.Calendars.GOOD_FRIDAY;
import static com.wealthfront.util.time.calendar.Calendars.INDEPENDENCE_DAY_OBSERVED;
import static com.wealthfront.util.time.calendar.Calendars.JUNETEENTH_OBSERVED;
import static com.wealthfront.util.time.calendar.Calendars.LABOR_DAY;
import static com.wealthfront.util.time.calendar.Calendars.MARTIN_LUTHER_KING_JR;
import static com.wealthfront.util.time.calendar.Calendars.MEMORIAL_DAY;
import static com.wealthfront.util.time.calendar.Calendars.NEVER;
import static com.wealthfront.util.time.calendar.Calendars.NEW_YEARS_OBSERVED;
import static com.wealthfront.util.time.calendar.Calendars.THANKSGIVING;
import static com.wealthfront.util.time.calendar.Calendars.WASHINGTONS_BIRTHDAY;
import static com.wealthfront.util.time.calendar.Calendars.WEEKEND;

import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.kaching.util.time.Year;
import com.wealthfront.util.time.calendar.Calendar;

public class WealthfrontCalendars {

  public static final Year JUNETEENTH_OBSERVED_START_YEAR = year(2020);

  public static final String WEALTHFRONT_JUNETEENTH_OBSERVED_CALENDAR_NAME = "WealthfrontJuneteenthObserved";
  public static final Calendar WEALTHFRONT_JUNETEENTH_OBSERVED = named(WEALTHFRONT_JUNETEENTH_OBSERVED_CALENDAR_NAME,
      startYearFrom(JUNETEENTH_OBSERVED, JUNETEENTH_OBSERVED_START_YEAR.getYear()));

  public static final String WEALTHFRONT_HOLIDAY_BREAK_CALENDAR_NAME = "WealthfrontHolidayBreak";
  public static final Calendar WEALTHFRONT_HOLIDAY_BREAK = named(WEALTHFRONT_HOLIDAY_BREAK_CALENDAR_NAME,
      and(
          predicate(date -> date.getMonthOfYear() == 12 && date.getDayOfMonth() >= 24, ALWAYS, NEVER),
          not(WEEKEND)
      ));

  public static final String WEALTHFRONT_HOLIDAY_CALENDAR_NAME = "WealthfrontHoliday";
  public static final Calendar WEALTHFRONT_HOLIDAY = named(WEALTHFRONT_HOLIDAY_CALENDAR_NAME,
      or(
          NEW_YEARS_OBSERVED,
          MARTIN_LUTHER_KING_JR,
          WASHINGTONS_BIRTHDAY,
          GOOD_FRIDAY,
          MEMORIAL_DAY,
          WEALTHFRONT_JUNETEENTH_OBSERVED,
          INDEPENDENCE_DAY_OBSERVED,
          LABOR_DAY,
          THANKSGIVING,
          DAY_AFTER_THANKSGIVING,
          WEALTHFRONT_HOLIDAY_BREAK
      ));

  public static final String WEALTHFRONT_WORKING_DAY_CALENDAR_NAME = "WealthfrontWorkingDay";
  public static final Calendar WEALTHFRONT_WORKING_DAY = named(WEALTHFRONT_WORKING_DAY_CALENDAR_NAME,
      and(
          not(WEEKEND),
          not(WEALTHFRONT_HOLIDAY)
      ));

  public static final List<Calendar> ALL_CALENDARS =
      Collections.unmodifiableList(Arrays.stream(WealthfrontCalendars.class.getDeclaredFields())
          .filter(field -> Modifier.isPublic(field.getModifiers()))
          .filter(field -> Modifier.isStatic(field.getModifiers()))
          .filter(field -> field.getType().isAssignableFrom(Calendar.class))
          .map(field -> {
            try {
              return (Calendar) field.get(null);
            } catch (IllegalAccessException var2) {
              throw new RuntimeException(var2);
            }
          })
          .collect(Collectors.toList()));

}
