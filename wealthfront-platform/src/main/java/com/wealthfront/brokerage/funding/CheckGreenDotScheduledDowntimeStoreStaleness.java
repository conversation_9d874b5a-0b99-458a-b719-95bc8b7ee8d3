package com.wealthfront.brokerage.funding;

import static com.kaching.platform.tinykv.TinyKvStoreGroup.GREEN_DOT_SCHEDULED_DOWNTIME;
import static com.kaching.util.mail.Pager.Device.PAGER_BANKING_PLATFORM;

import com.kaching.platform.tinykv.TinyKvStoreGroup;
import com.kaching.platform.tinykv.impl.CheckTinyKvStoreGroupStaleness;
import com.kaching.util.mail.Pager;

public class CheckGreenDotScheduledDowntimeStoreStaleness extends CheckTinyKvStoreGroupStaleness {

  @Override
  public TinyKvStoreGroup getStoreGroup() {
    return GREEN_DOT_SCHEDULED_DOWNTIME;
  }

  @Override
  public Pager.Device getDevice() {
    return PAGER_BANKING_PLATFORM;
  }

}
