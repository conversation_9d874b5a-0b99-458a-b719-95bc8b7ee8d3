package com.wealthfront.util;

import static java.util.Map.entry;

import java.util.Map;

import org.joda.time.DateTime;

import com.google.inject.ImplementedBy;

@ImplementedBy(DateTimeFormatHelperImpl.class)
public interface DateTimeFormatHelper {

  Map<String, String> TIME_ZONE_MAP = Map.ofEntries(
      entry("EST", "ET"),
      entry("EDT", "ET"),
      entry("CST", "CT"),
      entry("CDT", "CT"),
      entry("MST", "MT"),
      entry("MDT", "MT"),
      entry("PST", "PT"),
      entry("PDT", "PT"),
      entry("AKST", "AKT"),
      entry("AKDT", "AKT"),
      entry("HST", "HT"),
      entry("HDT", "HT")
  );

  String getTimeTextWithTimeZone(DateTime time);

}
