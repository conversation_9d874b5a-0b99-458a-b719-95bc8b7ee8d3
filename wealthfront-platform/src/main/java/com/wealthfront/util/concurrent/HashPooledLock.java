package com.wealthfront.util.concurrent;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

public class HashPooledLock<T> {

  private final ReentrantLock[] locks;

  public HashPooledLock(int numLocks) {
    this.locks = new ReentrantLock[numLocks];
    for (int i = 0; i < locks.length; i++) {
      this.locks[i] = new ReentrantLock();
    }
  }

  public ReentrantLock getLock(T obj) {
    int idx = Math.abs(obj.hashCode() & Integer.MAX_VALUE) % locks.length;
    return locks[idx];
  }

  public void lock(T obj) {
    getLock(obj).lock();
  }

  public void unlock(T obj) {
    getLock(obj).unlock();
  }

  public void lockInterruptibly(T obj) throws InterruptedException {
    getLock(obj).lockInterruptibly();
  }

  public boolean tryLock(T obj) {
    return getLock(obj).tryLock();
  }

  public boolean tryLock(T obj, long time, TimeUnit unit) throws InterruptedException {
    return getLock(obj).tryLock(time, unit);
  }

  public boolean isLocked(T obj) {
    return getLock(obj).isLocked();
  }

}
