package com.wealthfront.util.concurrent;

import static com.google.common.base.Preconditions.checkArgument;

import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableRangeSet;
import com.google.common.collect.RangeSet;
import com.google.common.collect.TreeRangeSet;
import com.kaching.util.UncheckedInterruptedException;

public class RangeMultiLockImpl<T extends Comparable> implements RangeMultiLock<T> {
  
  private final ReentrantLock lock = new ReentrantLock();
  private final AutoLock autoLock = new DelegatingAutoLock(lock);
  private final Condition condition = lock.newCondition();
  @VisibleForTesting final RangeSet<T> allLockedSets = TreeRangeSet.create();

  @Override
  public Object lock(RangeSet<T> ranges) {
    Key key = new Key(ranges);
    return autoLock.withLock(() -> {
      while (!canLock(key)) {
        try {
          condition.await();
        } catch (InterruptedException ex) {
          throw new UncheckedInterruptedException(ex);
        }
      }
      allLockedSets.addAll(ranges);
      return key;
    });
  }

  @Override
  public Object tryLock(RangeSet<T> ranges) {
    return autoLock.withLock(() -> {
      Key key = new Key(ranges);
      if (!canLock(key)) {
        return null;
      }
      allLockedSets.addAll(ranges);
      return key;
    });
  }

  @Override
  public void unlock(Object key) {
    checkArgument(key.getClass().equals(Key.class), "Should be a Key");
    Key key2 = (Key) key;
    checkArgument(key2.locked != null, "Key has already been used");
    lock.lock();
    try {
      allLockedSets.removeAll(key2.locked);
      condition.signalAll();
    } finally {
      key2.locked = null;
      lock.unlock();
    }
  }

  @Override
  public boolean isLocked(RangeSet<T> ranges) {
    return autoLock.withLock(() -> !canLock(new Key(ranges)));
  }

  @Override
  public RangeSet<T> getAllLockedSets() {
    return autoLock.withLock(() -> ImmutableRangeSet.copyOf(allLockedSets));
  }

  private boolean canLock(Key key) {
    return allLockedSets.complement().enclosesAll(key.locked);
  }
  
  private class Key {
    
    private RangeSet<T> locked;

    private Key(RangeSet<T> locked) {
      this.locked = locked;
    }
    
  }
  
}
