package com.wealthfront.util.objects;

import static com.kaching.platform.common.Strings.format;
import static java.lang.reflect.Modifier.isStatic;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.RecordComponent;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.function.Supplier;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.kaching.platform.common.Option;
import com.kaching.platform.functional.Unchecked;
import com.kaching.platform.functional.Unchecked.ThrowingFunction;
import com.kaching.util.functional.Result;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.wealthfront.util.objects.RecordInspector.FindableGetter;

public class Builder<O extends Record> {

  @Retention(RetentionPolicy.RUNTIME)
  @Target(ElementType.FIELD)
  public @interface Nullable {}

  public static class SetField<O extends Record, T> {

    private final Builder<O> builder;
    private final String field;
    private final Function<T, ?> wrap;

    private SetField(Builder<O> builder, String field, Function<T, ?> wrap) {
      this.builder = builder;
      this.field = field;
      this.wrap = wrap;
    }

    public Builder<O> to(T value) {
      return builder.assign(field, wrap.apply(value));
    }

  }

  public static class UpdateField<O extends Record, T> {

    private final Builder<O> builder;
    private final String field;

    private UpdateField(Builder<O> builder, String field) {
      this.builder = builder;
      this.field = field;
    }

    @SuppressWarnings("unchecked")
    public Builder<O> with(ThrowingFunction<T, T> fn) {
      return builder.assign(field, Unchecked.apply((T) builder.assignments.get(field), fn));
    }

  }

  private record BuilderInfo<O extends Record>(
      Class<O> clazz,
      Constructor<O> constructor,
      List<String> fields,
      List<Function<O, Object>> getters,
      List<Boolean> isNullable,
      Map<String, Supplier<Object>> defaultAssignments
  ) {}

  private static final LoadingCache<Class<? extends Record>, Result<BuilderInfo<? extends Record>>> BUILDER_INFO_CACHE =
      CacheBuilder.newBuilder()
          .build(new CacheLoader<>() {
            @Override
            @SuppressWarnings("unchecked")
            public Result<BuilderInfo<? extends Record>> load(Class<? extends Record> clazz) {
              return Result.compute(() -> {
                Constructor<Record> constructor =
                    (Constructor<Record>) Arrays.asList(clazz.getDeclaredConstructors()).get(0);
                constructor.setAccessible(true);

                List<String> fields = new ArrayList<>();
                List<Function<Record, Object>> getters = new ArrayList<>();
                List<Boolean> isNullable = new ArrayList<>();
                Map<String, Supplier<Object>> defaultAssignments = new HashMap<>();

                for (RecordComponent recordComponent : clazz.getRecordComponents()) {
                  Method method = recordComponent.getAccessor();
                  method.setAccessible(true);
                  getters.add(Unchecked.function(method::invoke));
                }
                for (Field field : clazz.getDeclaredFields()) {
                  if (!isStatic(field.getModifiers())) {
                    String fieldName = field.getName();
                    fields.add(fieldName);
                    isNullable.add(
                        field.isAnnotationPresent(Nullable.class) ||
                            (field.isAnnotationPresent(Value.class) &&
                                field.getAnnotation(Value.class).nullable()) ||
                            (clazz.isAnnotationPresent(Entity.class) &&
                                !field.isAnnotationPresent(Value.class) &&
                                !field.getType().isPrimitive())
                    );

                    if (field.getType().equals(Option.class)) {
                      defaultAssignments.put(fieldName, Option::none);
                    }
                  }
                }

                return new BuilderInfo<>((Class<Record>) clazz, constructor, fields, getters, isNullable,
                    defaultAssignments);
              });
            }
          });

  private final BuilderInfo<O> builderInfo;
  private final Map<String, Object> assignments = new HashMap<>();

  @SuppressWarnings("unchecked")
  public Builder(Class<O> clazz) {
    this.builderInfo = (BuilderInfo<O>) BUILDER_INFO_CACHE.getUnchecked(clazz).getOrThrow();
  }

  public Builder<O> from(O other) {
    for (int i = 0; i < builderInfo.fields.size(); i += 1) {
      assignments.put(builderInfo.fields.get(i), builderInfo.getters.get(i).apply(other));
    }
    return this;
  }

  public Builder<O> from(Builder<O> other) {
    for (String field : builderInfo.fields) {
      if (other.assignments.containsKey(field)) {
        assignments.put(field, other.assignments.get(field));
      }
    }
    return this;
  }

  public <T> SetField<O, T> set(FindableGetter<O, T> getter) {
    return new SetField<>(this, RecordInspector.findGetter(getter).getName(), Function.identity());
  }

  public <T> SetField<O, T> setOption(FindableGetter<O, Option<T>> getter) {
    return new SetField<>(this, RecordInspector.findGetter(getter).getName(), Option::of);
  }

  public <T> UpdateField<O, T> update(FindableGetter<O, T> getter) {
    return new UpdateField<>(this, RecordInspector.findGetter(getter).getName());
  }

  private <T> Builder<O> assign(String field, T value) {
    if (value == null) {
      assignments.remove(field);
    } else {
      assignments.put(field, value);
    }
    return this;
  }

  public O build() {
    Object[] args = new Object[builderInfo.fields.size()];
    for (int i = 0; i < builderInfo.fields.size(); i += 1) {
      String field = builderInfo.fields.get(i);
      Object assignment = assignments.get(field);

      if (assignment == null && builderInfo.defaultAssignments.containsKey(field)) {
        assignment = builderInfo.defaultAssignments.get(field).get();
      }

      if (assignment == null && !builderInfo.isNullable.get(i)) {
        throw new IllegalStateException(format("builder %s required field %s not set",
            builderInfo.clazz, builderInfo.fields.get(i)));
      }
      args[i] = assignment;
    }
    try {
      return builderInfo.constructor.newInstance(args);
    } catch (InstantiationException | IllegalAccessException | InvocationTargetException e) {
      throw new RuntimeException(e);
    }
  }

  @Override
  public int hashCode() {
    return Objects.hash(builderInfo.clazz, assignments);
  }

  @Override
  public boolean equals(Object obj) {
    if (obj instanceof Builder<?> other) {
      return builderInfo.clazz.equals(other.builderInfo.clazz) &&
             assignments.equals(other.assignments);
    }
    return false;
  }

  @Override
  public String toString() {
    return "Builder<" +
           builderInfo.clazz.getSimpleName() +
           ">" +
           new TreeMap<>(assignments);
  }

}
