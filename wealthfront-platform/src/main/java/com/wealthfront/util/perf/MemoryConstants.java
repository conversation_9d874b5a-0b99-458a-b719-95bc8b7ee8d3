package com.wealthfront.util.perf;

/**
 * These values change over time 
 */
public class MemoryConstants {

  /**
   * 16 bytes for an empty object, but jumps up to 24 when you add a field, and stays there
   * through a couple more fields probably for alignment.
   */
  public static final int OBJECT_OVERHEAD_BYTES = 24;
  
  /*
  For bounded, never expiring caches with strong keys 
  https://github.com/ben-manes/caffeine/wiki/Memory-overhead
   */
  public static final int GUAVA_CACHE_ENTRY_OVERHEAD_BYTES = 80;
  
  public static final int CONCURRENT_HASH_MAP_ENTRY_OVERHEAD_BYTES = 64;

}
