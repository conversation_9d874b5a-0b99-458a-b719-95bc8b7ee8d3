package com.wealthfront.util.perf;

import static com.kaching.util.Preconditions.checkQueryArgument;

import java.util.Set;
import java.util.function.Supplier;

import org.joda.time.Duration;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.math.StatsAccumulator;
import com.google.common.util.concurrent.RateLimiter;
import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.util.Sleeper;
import com.kaching.util.UncheckedInterruptedException;
import com.kaching.util.time.NanoTimeProvider;

public class ProfileCollectorImpl implements ProfileCollector {

  private static final double MAX_SAMPLES_PER_SECOND = 100.0;
  private static final Duration MIN_SLEEP_TIME = Duration.millis(5);

  @Inject NanoTimeProvider nanoTimeProvider;
  @Inject Sleeper sleeper;

  @Override
  public String collectProfile(
      StackTraceRecording recording,
      double samplesPerSecond,
      int stopNumSeconds,
      Option<Integer> stopWhenAllWorkersIdleForSeconds,
      Supplier<Set<Thread>> getWorkerThreads
  ) {
    checkQueryArgument(samplesPerSecond <= MAX_SAMPLES_PER_SECOND,
        "samplesPerSecond must be <= " + MAX_SAMPLES_PER_SECOND);
    checkQueryArgument(stopNumSeconds >= 0, "stopNumSeconds must be >= 0");
    checkQueryArgument(stopWhenAllWorkersIdleForSeconds.isEmptyOr(i -> i >= 0),
        "stopWhenAllWorkersIdleForSeconds must be >= 0");

    StatsAccumulator samplingTimeStats = new StatsAccumulator();

    RateLimiter rateLimiter = RateLimiter.create(samplesPerSecond);
    long endNanoTime = nanoTimeProvider.get() + stopNumSeconds * 1_000_000_000L;
    Long idleStartNanoTime = null;
    while (true) {
      long nowNs = nanoTimeProvider.get();
      if (nowNs >= endNanoTime) {
        return getExitMessage("Profiling stopped due to time", samplingTimeStats);
      }
      Set<Thread> workerThreads = getWorkerThreads.get();
      if (workerThreads.isEmpty()) {
        if (idleStartNanoTime == null) {
          idleStartNanoTime = nowNs;
        }
        if (stopWhenAllWorkersIdleForSeconds.isDefined() && nowNs >= idleStartNanoTime + stopWhenAllWorkersIdleForSeconds.getOrThrow() * 1_000_000_000L) {
          return getExitMessage("Profiling stopped due to idle workers", samplingTimeStats);
        }
      } else {
        idleStartNanoTime = null;
        recording.recordStackTraces(workerThreads);
      }
      long endNanos = nanoTimeProvider.get();
      samplingTimeStats.add(endNanos - nowNs);
      sleeper.sleep(MIN_SLEEP_TIME);

      acquireRateLimiter(rateLimiter);
      if (Thread.interrupted()) {
        throw new UncheckedInterruptedException();
      }
    }
  }

  private String getExitMessage(String exitReason, StatsAccumulator samplingTimeStats) {
    if (samplingTimeStats.count() == 0) {
      return String.format("%s. No samples collected", exitReason);
    }
    return String.format("%s. Num samples: %s, max sample time (ms): %.2f, mean sample time (ms): %.2f, std dev: %.2f",
        exitReason, samplingTimeStats.count(), samplingTimeStats.max() / 1_000_000, samplingTimeStats.mean() / 1_000_000,
        samplingTimeStats.sampleStandardDeviation() / 1_000_000);
  }

  @VisibleForTesting
  void acquireRateLimiter(RateLimiter rateLimiter) {
    rateLimiter.acquire();
  }

}
