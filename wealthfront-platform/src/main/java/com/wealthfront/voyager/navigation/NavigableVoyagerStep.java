package com.wealthfront.voyager.navigation;

import com.kaching.platform.hibernate.Id;
import com.wealthfront.voyager.model.VoyagerRecord;
import com.kaching.util.functional.Either;

public interface NavigableVoyagerStep<R extends VoyagerRequest, T extends VoyagerStepArguments> extends VoyagerStep<T> {

  Either<VoyagerStep<?>, VoyagerErrors> updateAndGetNext(Id<VoyagerRecord> id, R payload);

  @Override
  default Object visit(VoyagerStepVisitor<?> visitor) {
    return visitor.caseNavigableVoyagerStep(this);
  }

}
