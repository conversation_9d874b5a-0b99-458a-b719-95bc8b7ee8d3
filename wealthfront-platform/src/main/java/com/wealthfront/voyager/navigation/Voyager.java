package com.wealthfront.voyager.navigation;

import static com.wealthfront.voyager.model.VoyagerRecord.State.TERMINATED;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Parameter;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.joda.time.DateTime;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.Provider;
import com.google.inject.TypeLiteral;
import com.kaching.entities.ApiRequestMetadata;
import com.kaching.entities.AppCapabilitiesProvider;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Pair;
import com.kaching.platform.functional.Unit;
import com.kaching.platform.guice.TypeLiterals;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.queryengine.Owned;
import com.kaching.platform.queryengine.OwnerExtractor;
import com.kaching.platform.queryengine.Ownership;
import com.kaching.platform.queryengine.authorization.UserVerifier;
import com.kaching.platform.queryengine.exceptions.UnauthenticatedQueryException;
import com.kaching.platform.timing.Chronograph;
import com.kaching.user.UserId;
import com.kaching.util.functional.Either;
import com.kaching.util.functional.EitherVisitor;
import com.kaching.util.id.FlexId;
import com.kaching.util.id.IdExternalizer;
import com.wealthfront.voyager.model.VoyagerRecord;
import com.wealthfront.voyager.model.VoyagerRecordView;
import com.wealthfront.voyager.model.VoyagerType;
import com.wealthfront.voyager.navigation.VoyagerStep.ResultBuilder;

public abstract class Voyager<T extends Enum<T> & VoyagerRoute, R extends AbstractVoyagerResult<?>> {

  private static final String UNAUTHORIZED_ACCESS_ERROR = "Unauthorized access to owned resource in VoyagerStepArguments";

  @Inject public Provider<Option<ApiRequestMetadata>> apiRequestMetadataProvider;
  @Inject public RetryingTransacter transacter;
  @Inject public IdExternalizer<VoyagerRecord> voyagerRecordIdExternalizer;
  @Inject public Provider<DateTime> nowProvider;
  @Inject public Injector injector;
  @Inject VoyagerRouteExtractor routeExtractor;
  @Inject UserVerifier userVerifier;
  @Inject Chronograph chronograph;

  public CreateVoyagerResult<R> start(UserId userId, VoyagerStep<?> step, VoyagerStepId initialStepId) {
    return chronograph.time(getChronographScope("start"), step.getRoute().getStepPath(), () -> {
      Id<VoyagerRecord> id = transacter.executeWithSessionExpression(session -> {
        VoyagerRecord entity = new VoyagerRecord(
            userId,
            getVoyagerType(),
            initialStepId,
            nowProvider.get(),
            VoyagerRecord.State.ACTIVE);
        return session.save(entity);
      });

      return new CreateVoyagerResult<>(id, navigateTo(id, step, initialStepId));
    });
  }

  @SuppressWarnings({"unchecked", "rawtypes"})
  public R updateAndGetNext(FlexId<VoyagerRecord> voyagerRecordFlexId, VoyagerRequest request) {
    Id<VoyagerRecord> id = voyagerRecordIdExternalizer.internalize(voyagerRecordFlexId);
    Pair<VoyagerRoute, VoyagerStepArguments> routeAndArguments = getRouteAndArguments(request.getStepId());

    VoyagerStepArguments stepArguments = routeAndArguments.getRight();
    if (!isAuthorized(stepArguments)) {
      throw new UnauthenticatedQueryException(UNAUTHORIZED_ACCESS_ERROR);
    }

    Class<? extends VoyagerStep<?>> stepClass = routeAndArguments.getLeft().getStepClass();
    VoyagerStep<?> step = injector.getInstance(stepClass);

    return chronograph.time(getChronographScope("updateAndGetNext"), step.getRoute().getStepPath(), () -> {
      return (R) step.visit(new VoyagerStepVisitor<R>() {
        @Override
        public R caseNavigableVoyagerStep(NavigableVoyagerStep navigableVoyagerStep) {
          Either<VoyagerStep<?>, VoyagerErrors> result = navigableVoyagerStep.updateAndGetNext(id, request);
          return result.visit(new EitherVisitor<>() {
            @Override
            public R left(VoyagerStep<?> left) {
              return navigateTo(id, left, request.getStepId());
            }

            @Override
            public R right(VoyagerErrors right) {
              return setResultMetadata(id, request.getStepId(), getErrorResult(right), step.getRoute().getStepPath());
            }
          });
        }

        @Override
        public R caseTerminalVoyagerStep(TerminalVoyagerStep terminalVoyagerStep) {
          throw new IllegalStateException("Terminal voyager steps do not have a next step.");
        }
      });
    });
  }

  @SuppressWarnings("unchecked")
  public R navigateTo(Id<VoyagerRecord> id, VoyagerStep<?> step, VoyagerStepId previousStepId) {
    injector.injectMembers(step);
    ResultBuilder<?> resultBuilder = step.createStep(id, previousStepId);
    VoyagerStepArguments stepArguments = resultBuilder.stepArguments();
    VoyagerStepId newStepId = VoyagerStepId.of(step.getRoute(), stepArguments);

    if (!isAuthorized(stepArguments)) {
      throw new UnauthenticatedQueryException(UNAUTHORIZED_ACCESS_ERROR);
    }

    if (!isCompatibleWithStep(step)) {
      return setResultMetadata(id, newStepId, getIncompatibleResult(), step.getRoute().getStepPath());
    }

    R result = (R) resultBuilder.result();
    transacter.executeWithSession(session -> {
      VoyagerRecord record = session.get(VoyagerRecord.class, id).getOrThrow();
      record.setCurrentStep(newStepId, nowProvider.get());
      step.visit(new VoyagerStepVisitor<Unit>() {
        @Override
        public Unit caseNavigableVoyagerStep(NavigableVoyagerStep<?, ?> navigableVoyagerStep) {
          return Unit.unit;
        }

        @Override
        public Unit caseTerminalVoyagerStep(TerminalVoyagerStep<?> terminalVoyagerStep) {
          record.setState(TERMINATED, nowProvider.get());
          return Unit.unit;
        }
      });
    });
    return setResultMetadata(id, newStepId, result, step.getRoute().getStepPath());
  }

  @SuppressWarnings({"unchecked", "rawtypes"})
  public R resumeStep(FlexId<VoyagerRecord> voyagerRecordFlexId, Option<VoyagerStepId> maybeStepId) {
    Id<VoyagerRecord> id = voyagerRecordIdExternalizer.internalize(voyagerRecordFlexId);
    VoyagerRecordView voyagerRecordView = transacter.executeWithReadOnlySessionExpression(
        session -> session.get(VoyagerRecord.class, id).getOrThrow().toView());
    VoyagerStepId stepId = maybeStepId.getOrElse(voyagerRecordView.currentStep());
    if (voyagerRecordView.state().equals(TERMINATED)) {
      stepId = voyagerRecordView.currentStep();
    }
    Pair<VoyagerRoute, VoyagerStepArguments> routeAndArguments = getRouteAndArguments(stepId);

    Map<VoyagerRoute, VoyagerRoute> expiredRoutes = getExpiredRoutes();
    if (expiredRoutes.containsKey(routeAndArguments.getLeft())) {
      return navigateTo(id, injector.getInstance(expiredRoutes.get(routeAndArguments.getLeft()).getStepClass()), stepId);
    }

    VoyagerStepArguments stepArguments = routeAndArguments.getRight();
    if (!isAuthorized(stepArguments)) {
      throw new UnauthenticatedQueryException(UNAUTHORIZED_ACCESS_ERROR);
    }

    Class<? extends VoyagerStep<?>> stepClass = routeAndArguments.getLeft().getStepClass();
    VoyagerStep step = injector.getInstance(stepClass);
    R result = !isCompatibleWithStep(step) ? getIncompatibleResult() : (R) step.resumeStep(id, stepArguments.getClass().cast(stepArguments));
    return setResultMetadata(id, stepId, result, step.getRoute().getStepPath());
  }

  @SuppressWarnings({"rawtypes"})
  @VisibleForTesting
  public Boolean isCompatibleWithStep(VoyagerStep step) {
    return apiRequestMetadataProvider.get().transform(metadata -> {
      Boolean isStepCompatible = AppCapabilitiesProvider.isBetaOrOnVersions(
          metadata.getUserPlatform(),
          metadata.getAppVersion(),
          metadata.getAppVersion(),
          step.getCompatibilityRequirement().minAndroidVersion(),
          step.getCompatibilityRequirement().minIOSVersion(),
          AppCapabilitiesProvider.isClientAppInternal(Option.some(metadata)));

      Boolean isVoyagerCompatible = AppCapabilitiesProvider.isBetaOrOnVersions(
          metadata.getUserPlatform(),
          metadata.getAppVersion(),
          metadata.getAppVersion(),
          this.getCompatibilityRequirement().minAndroidVersion(),
          this.getCompatibilityRequirement().minIOSVersion(),
          AppCapabilitiesProvider.isClientAppInternal(Option.some(metadata)));

      return isStepCompatible && isVoyagerCompatible;
    }).getOrElse(true);
  }

  @VisibleForTesting
  boolean isAuthorized(VoyagerStepArguments stepArguments) {
    if (Option.of(stepArguments).isEmpty()) {
      return true;
    }

    Class<?> stepArgumentsClazz = stepArguments.getClass();
    Map<String, Owned> ownedFields = getOwnedFields(stepArgumentsClazz);

    List<OwnedFieldInfo> ownedFieldInfos = new ArrayList<>();
    for (Field field : stepArgumentsClazz.getDeclaredFields()) {
      field.setAccessible(true);

      Option<Owned> maybeOwned = Option.of(ownedFields.get(field.getName()));
      if (maybeOwned.isEmpty()) {
        continue;
      }

      Option<Object> maybeValue = Option.none();
      try {
        maybeValue = Option.of(field.get(stepArguments));
      } catch (IllegalAccessException e) {
        throw new RuntimeException("Failed to access field " + field.getName(), e);
      }

      if (maybeValue.isEmpty()) {
        return false;
      }

      Type fieldType = field.getGenericType();
      TypeLiteral<?> typeLiteral = TypeLiteral.get(fieldType);
      OwnerExtractor<?> extractor;
      try {
        extractor = injector.getInstance(Key.get(TypeLiterals.get(OwnerExtractor.class, typeLiteral)));
      } catch (Exception e) {
        throw new RuntimeException("Failed to get OwnerExtractor for field " + field.getName() + " of type " + fieldType, e);
      }
      ownedFieldInfos.add(new OwnedFieldInfo(maybeValue.getOrThrow(), maybeOwned.getOrThrow(), extractor));
    }

    return verifyOwnership(ownedFieldInfos);
  }

  @VisibleForTesting
  @SuppressWarnings({"unchecked", "rawtypes"})
  boolean verifyOwnership(List<OwnedFieldInfo> ownedFieldInfos) {
    if (ownedFieldInfos.isEmpty()) {
      return true;
    }

    return transacter.executeWithReadOnlySessionExpression(session -> {
      for (OwnedFieldInfo ownedFieldInfo : ownedFieldInfos) {
        OwnerExtractor extractor = ownedFieldInfo.extractor();
        Ownership ownership = extractor.extract(ownedFieldInfo.value(), session);
        try {
          if (!userVerifier.verify(ownership, ownedFieldInfo.ownedAnnotation().value())) {
            return false;
          }
        } catch (UnauthenticatedQueryException e) {
          return false;
        }
      }

      return true;
    });
  }

  @VisibleForTesting
  Map<String, Owned> getOwnedFields(Class<?> clazz) {
    Map<String, Owned> ownedFields = new HashMap<>();
    for (Constructor<?> constructor : clazz.getDeclaredConstructors()) {
      for (Parameter parameter : constructor.getParameters()) {
        Option<Owned> maybeOwned = Option.of(parameter.getAnnotation(Owned.class));
        if (maybeOwned.isDefined()) {
          ownedFields.put(parameter.getName(), maybeOwned.getOrThrow());
        }
      }
    }

    return ownedFields;
  }

  @VisibleForTesting
  record OwnedFieldInfo(Object value, Owned ownedAnnotation, OwnerExtractor<?> extractor) {}

  private R setResultMetadata(Id<VoyagerRecord> id, VoyagerStepId stepId, R result, String stepPath) {
    result.setVoyagerMetadata(voyagerRecordIdExternalizer.externalizeId(id), stepId, Option.some(stepPath));
    return result;
  }

  public abstract Map<VoyagerRoute, VoyagerRoute> getExpiredRoutes();

  public abstract VoyagerType getVoyagerType();

  public abstract T[] getRoutes();

  Pair<VoyagerRoute, VoyagerStepArguments> getRouteAndArguments(VoyagerStepId key) {
    return routeExtractor.extractRouteAndArguments(getRoutes(), key);
  }

  public abstract R getErrorResult(VoyagerErrors voyagerErrors);

  public abstract R getIncompatibleResult();

  public AppCompatibilityRequirement getCompatibilityRequirement() {
    return new AppCompatibilityRequirement(Option.none(), Option.none());
  }

}
