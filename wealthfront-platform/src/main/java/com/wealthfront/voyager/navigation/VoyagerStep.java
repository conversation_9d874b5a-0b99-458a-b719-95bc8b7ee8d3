package com.wealthfront.voyager.navigation;

import javax.annotation.Nonnull;

import com.kaching.platform.hibernate.Id;
import com.wealthfront.voyager.model.VoyagerRecord;

public interface VoyagerStep<T extends VoyagerStepArguments> {

  Object visit(VoyagerStepVisitor<?> visitor);

  ResultBuilder<T> createStep(Id<VoyagerRecord> voyagerRecordId, VoyagerStepId previousStepId);

  VoyagerResult<?> resumeStep(Id<VoyagerRecord> voyagerRecordId, T stepArguments);

  @Nonnull
  VoyagerRoute getRoute();

  AppCompatibilityRequirement getCompatibilityRequirement();

  record ResultBuilder<T extends VoyagerStepArguments>(VoyagerResult<?> result, T stepArguments) {}

}
