package com.wealthfront.voyager.queries;

import static com.wealthfront.voyager.model.VoyagerRecord.State.ACTIVE;

import com.wealthfront.voyager.queries.GetActiveVoyagerRecordsForUser.Response;

import java.util.List;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.WithReadOnlySessionExpression;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.Owned;
import com.kaching.user.UserId;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.wealthfront.voyager.model.VoyagerRecord;
import com.wealthfront.voyager.model.VoyagerType;

public class GetActiveVoyagerRecordsForUser extends AbstractQuery<Response> {

  private final UserId userId;

  public GetActiveVoyagerRecordsForUser(@Owned UserId userId) {
    this.userId = userId;
  }

  @Inject RetryingTransacter transacter;

  @Override
  public Response process() {
    return transacter.executeWithReadOnlySessionExpression(new WithReadOnlySessionExpression<>() {
      @Inject VoyagerRepository repo;

      @Override
      public Response run(DbSession session) {
        List<VoyagerRecordDetails> details = repo.getVoyagersByUserIdAndState(userId, ACTIVE)
            .stream()
            .map(voyagerRecord -> new VoyagerRecordDetails(voyagerRecord.getId(), voyagerRecord.getVoyagerType()))
            .toList();
        return new Response(details);
      }
    });
  }

  @ExposeType(value = {ExposeTo.LOCAL, ExposeTo.BACKEND,
      ExposeTo.API_SERVER}, namespace = ExposeType.RewriteNamespace.QUERY)
  @Entity
  public record VoyagerRecordDetails(
      @Value(nullable = false) Id<VoyagerRecord> voyagerRecordId,
      @Value(nullable = false) VoyagerType voyagerType
  ) {}

  @ExposeType(value = {ExposeTo.LOCAL, ExposeTo.BACKEND,
      ExposeTo.API_SERVER}, namespace = ExposeType.RewriteNamespace.QUERY)
  @Entity
  public static class Response {

    @Value(nullable = false)
    private List<VoyagerRecordDetails> voyagerDetails;

    public Response(List<VoyagerRecordDetails> voyagerDetails) {
      this.voyagerDetails = voyagerDetails;
    }

    public Response() {/* json */}

    @VisibleForTesting
    public List<VoyagerRecordDetails> getResponseList() {
      return voyagerDetails;
    }

  }

}
