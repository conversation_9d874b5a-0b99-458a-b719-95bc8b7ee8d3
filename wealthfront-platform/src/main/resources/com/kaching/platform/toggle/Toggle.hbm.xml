<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping default-access="field" package="com.kaching.platform.toggle">
  <class name="Toggle" table="toggles">
    <id name="id" column="id" type="Id">
      <generator class="identity" />
    </id>
    <property name="name" column="name" not-null="true"/>
    <kawala:archived
        name="toggleState"
        denormalized="true"
        current-column="state"
        parent-column="toggle_id"
        version-table="version_toggle_state" >
      <property name="value" type="boolean" not-null = "true"/>
    </kawala:archived>
  </class>
</hibernate-mapping>