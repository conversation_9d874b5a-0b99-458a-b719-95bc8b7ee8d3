package com.kaching.entities;

import static com.kaching.DefaultKachingMarshallers.createEntityMarshaller;
import static com.kaching.entities.AnnualPercentageRate.ZERO_PERCENT;
import static com.kaching.entities.AnnualPercentageRate.annualPercentageRate;
import static com.kaching.entities.Money.ZERO_MONEY;
import static com.kaching.entities.Money.money;
import static com.wealthfront.test.Assert.assertBigDecimalEquals;
import static com.wealthfront.test.Assert.assertMarshalling;
import static org.junit.Assert.assertEquals;

import java.math.BigDecimal;

import org.junit.Test;

import com.twolattes.json.Entity;
import com.twolattes.json.EntityMarshaller;
import com.twolattes.json.Json;
import com.twolattes.json.Value;

public class AnnualPercentageRateTest {

  @Test
  public void marshalling() {
    EntityMarshaller<AnnualPercentageRateEntity> marshaller = createEntityMarshaller(AnnualPercentageRateEntity.class);

    Json.Value expected = Json.object(
        "annualPercentageRate", 0.048
    );

    assertMarshalling(marshaller, expected, new AnnualPercentageRateEntity(annualPercentageRate("0.048")));
  }

  @Test
  public void bigDecimalConstructor() {
    assertBigDecimalEquals(.0172, annualPercentageRate(BigDecimal.valueOf(.0172)).toBigDecimal());
    assertBigDecimalEquals(.0185, annualPercentageRate(BigDecimal.valueOf(.0185)).toBigDecimal());
    assertBigDecimalEquals(.02, annualPercentageRate(BigDecimal.valueOf(.02)).toBigDecimal());
    assertBigDecimalEquals(.025, annualPercentageRate(BigDecimal.valueOf(.025)).toBigDecimal());
    assertBigDecimalEquals(1, annualPercentageRate(BigDecimal.valueOf(1)).toBigDecimal());
    assertBigDecimalEquals(1.25, annualPercentageRate(BigDecimal.valueOf(1.25)).toBigDecimal());
  }

  @Test
  public void stringConstructor() {
    assertBigDecimalEquals(.0172, annualPercentageRate(new BigDecimal(".0172")).toBigDecimal());
    assertBigDecimalEquals(.0185, annualPercentageRate(new BigDecimal(".0185")).toBigDecimal());
    assertBigDecimalEquals(.02, annualPercentageRate(new BigDecimal(".02")).toBigDecimal());
    assertBigDecimalEquals(.025, annualPercentageRate(new BigDecimal(".025")).toBigDecimal());
    assertBigDecimalEquals(1, annualPercentageRate(new BigDecimal("1")).toBigDecimal());
    assertBigDecimalEquals(1.25, annualPercentageRate(new BigDecimal("1.25")).toBigDecimal());
  }

  @Test
  public void doubleConstructor() {
    assertBigDecimalEquals(.0172, annualPercentageRate(.0172).toBigDecimal());
    assertBigDecimalEquals(.0185, annualPercentageRate(.0185).toBigDecimal());
    assertBigDecimalEquals(.02, annualPercentageRate(.02).toBigDecimal());
    assertBigDecimalEquals(.025, annualPercentageRate(.025).toBigDecimal());
    assertBigDecimalEquals(1, annualPercentageRate(1).toBigDecimal());
    assertBigDecimalEquals(1.25, annualPercentageRate(1.25).toBigDecimal());
  }

  @Test
  public void formatPercentage() {
    assertEquals("-0.10%", annualPercentageRate(BigDecimal.valueOf(-0.001)).formatPercentage());
    assertEquals("-0.15%", annualPercentageRate(BigDecimal.valueOf(-0.0015)).formatPercentage());
    assertEquals("-1.00%", annualPercentageRate(BigDecimal.valueOf(-0.01)).formatPercentage());
    assertEquals("-1.72%", annualPercentageRate(BigDecimal.valueOf(-0.0172)).formatPercentage());
    assertEquals("-1.72%", annualPercentageRate(BigDecimal.valueOf(-0.01729)).formatPercentage());
    assertEquals("-2.00%", annualPercentageRate(BigDecimal.valueOf(-0.02)).formatPercentage());
    assertEquals("0.00%", annualPercentageRate(BigDecimal.valueOf(0)).formatPercentage());
    assertEquals("0.10%", annualPercentageRate(BigDecimal.valueOf(.001)).formatPercentage());
    assertEquals("0.15%", annualPercentageRate(BigDecimal.valueOf(.0015)).formatPercentage());
    assertEquals("1.00%", annualPercentageRate(BigDecimal.valueOf(.01)).formatPercentage());
    assertEquals("1.72%", annualPercentageRate(BigDecimal.valueOf(.0172)).formatPercentage());
    assertEquals("1.72%", annualPercentageRate(BigDecimal.valueOf(.01729)).formatPercentage());
    assertEquals("2.00%", annualPercentageRate(BigDecimal.valueOf(0.02)).formatPercentage());
  }

  @Test
  public void multiply() {
    assertBigDecimalEquals(0, annualPercentageRate(0).multiply(ZERO_MONEY).toBigDecimal());
    assertBigDecimalEquals(0, annualPercentageRate(0.005).multiply(ZERO_MONEY).toBigDecimal());
    assertBigDecimalEquals(0, annualPercentageRate(0.01).multiply(ZERO_MONEY).toBigDecimal());
    assertBigDecimalEquals(0, annualPercentageRate(0.05).multiply(ZERO_MONEY).toBigDecimal());
    assertBigDecimalEquals(0, annualPercentageRate(0.1).multiply(ZERO_MONEY).toBigDecimal());
    assertBigDecimalEquals(0, annualPercentageRate(1.0).multiply(ZERO_MONEY).toBigDecimal());

    assertBigDecimalEquals(0, ZERO_PERCENT.multiply(money(1)).toBigDecimal());
    assertBigDecimalEquals(.005, annualPercentageRate(0.005).multiply(money(1)).toBigDecimal());
    assertBigDecimalEquals(.05, annualPercentageRate(0.05).multiply(money(1)).toBigDecimal());
    assertBigDecimalEquals(.1, annualPercentageRate(0.1).multiply(money(1)).toBigDecimal());
    assertBigDecimalEquals(2, annualPercentageRate(2).multiply(money(1)).toBigDecimal());
    assertBigDecimalEquals(2.5, annualPercentageRate(2.5).multiply(money(1)).toBigDecimal());

    assertBigDecimalEquals(0.07, annualPercentageRate(0.01).multiply(money(7)).toBigDecimal());
    assertBigDecimalEquals(0.7, annualPercentageRate(0.1).multiply(money(7)).toBigDecimal());
    assertBigDecimalEquals(17.5, annualPercentageRate(2.5).multiply(money(7)).toBigDecimal());
  }

  @Entity
  private static class AnnualPercentageRateEntity {

    @Value
    private AnnualPercentageRate annualPercentageRate;

    AnnualPercentageRateEntity() { /* JSON */ }

    AnnualPercentageRateEntity(AnnualPercentageRate annualPercentageRate) {
      this.annualPercentageRate = annualPercentageRate;
    }

  }

}
