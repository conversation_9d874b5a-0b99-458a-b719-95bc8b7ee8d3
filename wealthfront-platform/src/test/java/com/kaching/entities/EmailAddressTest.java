package com.kaching.entities;

import static com.wealthfront.test.Assert.assertNotEquals;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

public class EmailAddressTest {

  @Test
  public void validEmailAddresses() {
    new EmailAddress("<EMAIL>");
    new EmailAddress("<EMAIL>");
    new EmailAddress("<EMAIL>");
    new EmailAddress("<EMAIL>");
    new EmailAddress("<EMAIL>");
    new EmailAddress("<EMAIL>");
  }

  @Test(expected = MalformedEmailException.class)
  public void spaceInLocalPartIsInvalid() {
    new EmailAddress("pascal <EMAIL>");
  }

  @Test(expected = MalformedEmailException.class)
  public void dotAtEndOfLocalPartIsInvalid() {
    new EmailAddress("<EMAIL>");
  }

  @Test(expected = MalformedEmailException.class)
  public void dotAtStartOfLocalPartIsInvalid() {
    new EmailAddress(".<EMAIL>");
  }

  @Test(expected = MalformedEmailException.class)
  public void twoAtSymbolsIsInvalid() {
    new EmailAddress("jared@<EMAIL>");
  }

  @Test(expected = MalformedEmailException.class)
  public void hyphenAtStartOfDomainPartIsInvalid() {
    new EmailAddress("<EMAIL>");
  }

  @Test(expected = MalformedEmailException.class)
  public void hyphenAtEndOfDomainPartIsInvalid() {
    new EmailAddress("<EMAIL>");
  }

  @Test(expected = MalformedEmailException.class)
  public void digitInTopLevelDomainIsInvalid() {
    new EmailAddress("<EMAIL>.e2u");
  }

  @Test(expected = MalformedEmailException.class)
  public void noSecondLevelDomainIsInvalid() {
    new EmailAddress("jmjacobs@edu");
  }

  @Test(expected = MalformedEmailException.class)
  public void latinCharactersIsInvalid() {
    new EmailAddress("jonañ@gmail.com");
  }

  @Test
  public void validationCanBeBypassed() {
    assertEquals("....@....", new EmailAddress("....@....", false).toString());
  }

  @Test
  public void isWellFormedTest() {
    assertTrue(EmailAddress.isWellFormed("<EMAIL>"));
    assertTrue(EmailAddress.isWellFormed("<EMAIL>"));
    assertFalse(EmailAddress.isWellFormed("matt@test"));
    assertFalse(EmailAddress.isWellFormed("abc@gmaiñ.com"));
    assertFalse(EmailAddress.isWellFormed("abñ@gmail.com"));
    assertFalse(EmailAddress.isWellFormed("************ñ"));
  }

  @Test
  public void toStringLowercasesDomain() {
    assertEquals("<EMAIL>", new EmailAddress("<EMAIL>").toString());
    assertEquals("<EMAIL>", new EmailAddress("<EMAIL>").toString());
    assertEquals("<EMAIL>", new EmailAddress("<EMAIL>").toString());
  }

  @Test
  public void isTest() {
    assertTrue(new EmailAddress("<EMAIL>").isTest());
    assertTrue(new EmailAddress("<EMAIL>").isTest());
    assertFalse(new EmailAddress("<EMAIL>").isTest());
    assertFalse(new EmailAddress("<EMAIL>").isTest());
    assertFalse(new EmailAddress("<EMAIL>").isTest());
  }

  @Test
  public void isTestAndWealthfrontDomain() {
    assertTrue(new EmailAddress("<EMAIL>").isTestAndWealthfrontDomain());
    assertTrue(new EmailAddress("+<EMAIL>").isTestAndWealthfrontDomain());
    assertFalse(new EmailAddress("<EMAIL>").isTestAndWealthfrontDomain());
    assertFalse(new EmailAddress("<EMAIL>").isTestAndWealthfrontDomain());
    assertFalse(new EmailAddress("<EMAIL>").isTestAndWealthfrontDomain());
  }

  @Test
  public void isNoReply() {
    assertTrue(new EmailAddress("<EMAIL>").isNoReply());
    assertTrue(new EmailAddress("<EMAIL>").isNoReply());
    assertFalse(new EmailAddress("<EMAIL>").isNoReply());
    assertFalse(new EmailAddress("<EMAIL>").isNoReply());
  }

  @Test
  public void isCompanyAddress() {
    assertTrue(new EmailAddress("<EMAIL>").isCompanyAddress());
    assertTrue(new EmailAddress("<EMAIL>").isCompanyAddress());
    assertFalse(new EmailAddress("<EMAIL>").isCompanyAddress());
  }

  @Test
  public void localPartIsCaseSensitive() {
    EmailAddress e1 = new EmailAddress("<EMAIL>");
    EmailAddress e2 = new EmailAddress("<EMAIL>");
    EmailAddress e3 = new EmailAddress("<EMAIL>");
    assertEquals(e1, e2);
    assertEquals(e1.hashCode(), e2.hashCode());
    assertFalse(e1.equals(e3));
    assertFalse(e2.equals(e3));
  }

  @Test
  public void equalsIgnoreCase() {
    EmailAddress e1 = new EmailAddress("<EMAIL>");
    EmailAddress e2 = new EmailAddress("<EMAIL>");
    EmailAddress e3 = new EmailAddress("<EMAIL>");
    assertEquals(e1, e2);
    assertNotEquals(e1, e3);
    assertEquals(e1.hashCode(), e2.hashCode());
    assertTrue(e1.equalsIgnoreCase(e3));
    assertTrue(e2.equalsIgnoreCase(e3));
  }

  @Test
  public void domainIsNotCaseSensitive() {
    EmailAddress e1 = new EmailAddress("<EMAIL>");
    EmailAddress e2 = new EmailAddress("<EMAIL>");
    assertEquals(e1, e2);
    assertEquals(e1.hashCode(), e2.hashCode());
  }

}
