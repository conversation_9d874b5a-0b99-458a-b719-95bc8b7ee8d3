package com.kaching.entities;

import static com.kaching.entities.Money.money;
import static com.wealthfront.test.Assert.assertOptionEmpty;
import static com.wealthfront.test.Assert.assertOptionEquals;
import static java.util.Collections.emptyList;

import org.junit.Test;

import com.google.common.collect.ImmutableList;

public class MoniesTest {

  @Test
  public void averageOfEmptyIterable_returnsEmptyOption() {
    assertOptionEmpty(Monies.average(emptyList()));
  }

  @Test
  public void average() {
    assertOptionEquals(money(3), Monies.average(ImmutableList.of(money(1), money(2), money(3), money(4), money(5))));
  }

}