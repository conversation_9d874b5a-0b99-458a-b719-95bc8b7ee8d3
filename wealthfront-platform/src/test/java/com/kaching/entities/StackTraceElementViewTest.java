package com.kaching.entities;

import static com.kaching.DefaultKachingMarshallers.createEntityMarshaller;
import static com.wealthfront.test.Assert.assertMarshalling;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.twolattes.json.Json;

public class StackTraceElementViewTest {

  @Test
  public void marshall() {
    Json.Value expected = Json.object(
        "className", "AClass",
        "methodName", "aMethod",
        "fileName", "AClass.java",
        "lineNumber", 1
    );
    StackTraceElementView element = new StackTraceElementView("AClass", "aMethod", "AClass.java", 1);
    assertMarshalling(createEntityMarshaller(StackTraceElementView.class), expected, element);
  }

  @Test
  public void getters() {
    StackTraceElementView element = new StackTraceElementView("AClass", "aMethod", "AClass.java", 1);
    assertEquals("AClass", element.getClassName());
    assertEquals("aMethod", element.getMethodName());
    assertEquals("AClass.java", element.getFileName());
    assertEquals(1, element.getLineNumber());
  }

}