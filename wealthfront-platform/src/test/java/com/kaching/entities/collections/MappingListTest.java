package com.kaching.entities.collections;

import static com.wealthfront.test.Assert.assertEmpty;
import static com.wealthfront.test.Assert.assertThrows;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.function.Function;

import org.junit.Test;

public class MappingListTest {

  @Test
  public void construct_throwsWithoutRandomAccess() {
    assertThrows(IllegalArgumentException.class, "Delegate list is expected to implement RandomAccess",
        () -> new MappingList<>(new LinkedList<>(), Function.identity(), Function.identity()));
  }

  @Test
  public void construct_okWithRandomAccess() {
    assertEmpty(new MappingList<>(new ArrayList<>(), Function.identity(), Function.identity()));
  }

}