package com.kaching.entities.converters;

import java.math.BigDecimal;

import org.junit.Test;

import com.kaching.DefaultKachingMarshallers;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

public class MapConverterTest {

  @Test(expected = IllegalStateException.class)
  public void castThrowsISE1() {
    new MapConverter<>(
        DefaultKachingMarshallers.createMarshaller(Integer.class),
        DefaultKachingMarshallers.createMarshaller(Foo.class)).fromString("{\"1\":{\"bar\":\"123\"}}");
  }

  @Test(expected = IllegalArgumentException.class)
  public void castThrowsIAE2() {
    new MapConverter<>(
        DefaultKachingMarshallers.createMarshaller(Integer.class),
        DefaultKachingMarshallers.createMarshaller(Foo.class)).fromString("{\"a\":{\"bar\":123}}");
  }

  @Entity
  static class Foo {

    @Value BigDecimal bar;
  }

}
