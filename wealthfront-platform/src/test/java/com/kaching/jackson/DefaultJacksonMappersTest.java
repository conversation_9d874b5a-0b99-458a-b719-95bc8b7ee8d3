package com.kaching.jackson;

import static com.kaching.entities.AllocationPercentage.allocationPercentage;
import static com.kaching.entities.Money.money;
import static com.kaching.entities.TaosAdmin.taos;
import static com.kaching.jackson.AssertJacksonMarshalling.assertJacksonTwolattesMarshalling;
import static com.kaching.jackson.AssertJacksonMarshalling.assertMarshalling;
import static com.kaching.jackson.DefaultJacksonMappersTest.ExampleWithVariousFields.SomeEnum.SOME_ENUM_VALUE;
import static com.wealthfront.test.Assert.assertThrows;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.not;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.io.IOException;
import java.io.Reader;
import java.io.StringReader;
import java.io.StringWriter;
import java.io.UncheckedIOException;
import java.io.Writer;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.junit.Ignore;
import org.junit.Test;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectReader;
import com.fasterxml.jackson.databind.exc.InvalidDefinitionException;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.fasterxml.jackson.databind.exc.MismatchedInputException;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Iterables;
import com.google.inject.TypeLiteral;
import com.kaching.DefaultKachingMarshallers;
import com.kaching.entities.AllocationPercentage;
import com.kaching.entities.Comment;
import com.kaching.entities.EquityPriceOrCleanPrice;
import com.kaching.entities.Money;
import com.kaching.entities.Price;
import com.kaching.entities.Quantity;
import com.kaching.entities.Scalar;
import com.kaching.entities.TaosAdmin;
import com.kaching.entities.types.IdJsonType;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.types.ParameterizedTypeImpl;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.queue.MockEntity;
import com.kaching.platform.oauth2.OAuthAccessToken;
import com.kaching.platform.zk.experiment.ExperimentName;
import com.kaching.util.mail.QueuedPageId;
import com.kaching.util.types.JsonJsonType;
import com.twolattes.json.Entity;
import com.twolattes.json.Json;
import com.twolattes.json.Value;

public class DefaultJacksonMappersTest {

  private enum Good {
    GOOD
  }

  @Test
  public void shouldMarshallNumbersConsistentlyWithTwolattes() {
    assertJacksonTwolattesMarshalling(Integer.class, Json.number(10), 10);
    assertJacksonTwolattesMarshalling(Money.class, Json.number(123.45), money(123.45));
    assertJacksonTwolattesMarshalling(Price.class, Json.number(123.45), Price.price(123.45));
    assertJacksonTwolattesMarshalling(Quantity.class, Json.number(123.45), Quantity.quantity(123.45));
    assertJacksonTwolattesMarshalling(Scalar.class, Json.number(123.45), Scalar.scalar(123.45));
    assertJacksonTwolattesMarshalling(Id.class, Json.number(10), new Id<MockEntity>(10));
  }

  @Test
  public void shouldMarshallStringsConsistentlyWithTwolattes() {
    assertJacksonTwolattesMarshalling(Good.class, Json.string("GOOD"), Good.GOOD);
    assertJacksonTwolattesMarshalling(String.class, Json.string("hello"), "hello");
    assertJacksonTwolattesMarshalling(LocalDate.class, Json.string("20100913"), new LocalDate(2010, 9, 13));
  }

  @Test
  public void shouldMarshallBooleansConsistentlyWithTwolattes() {
    assertJacksonTwolattesMarshalling(Boolean.class, Json.TRUE, Boolean.TRUE);
  }

  @Test
  public void shouldMarshallNullStringsAndBooleansConsistentlyWithTwolattes() {
    assertJacksonTwolattesMarshalling(Good.class, Json.NULL, null);
    assertJacksonTwolattesMarshalling(String.class, Json.NULL, null);
    assertJacksonTwolattesMarshalling(LocalDate.class, Json.NULL, null);
    assertJacksonTwolattesMarshalling(Boolean.class, Json.NULL, null);
  }

  @Test
  public void shouldFailWhenDeserializingNullPrimitive_deprecatedVersion() throws Exception {
    ObjectReader reader = DefaultJacksonMappers.getObjectReader(ExampleWithNonAnnotatedField.class);
    try {
      reader.readValue("{\"number\":null}");
      fail();
    } catch (MismatchedInputException e) {
      assertThat(e.getMessage(), containsString("Cannot map `null` into type"));
    }
  }

  @Test
  public void shouldFailWhenDeserializingNullPrimitive() {
    JacksonMapper<ExampleWithNonAnnotatedField> mapper =
        DefaultJacksonMappers.jacksonMapper(ExampleWithNonAnnotatedField.class);
    try {
      mapper.deserialize("{\"number\":null}");
      fail();
    } catch (UncheckedIOException e) {
      assertThat(e.getMessage(), containsString("Cannot map `null` into type"));
    }
  }

  @Test
  public void shouldFailWhenDeserializingFloatToInt_deprecatedVersion() throws Exception {
    ObjectReader reader = DefaultJacksonMappers.getObjectReader(ExampleWithNonAnnotatedField.class);
    try {
      reader.readValue("{\"number\":123.456}");
      fail();
    } catch (JsonMappingException e) {
      assertThat(e.getMessage(), containsString("123.456"));
      assertThat(e.getMessage(), containsString("Cannot coerce Floating-point value (123.456)"));
    }
  }

  @Test
  public void shouldFailWhenDeserializingFloatToInt() {
    JacksonMapper<ExampleWithNonAnnotatedField> mapper =
        DefaultJacksonMappers.jacksonMapper(ExampleWithNonAnnotatedField.class);
    try {
      mapper.deserialize("{\"number\":123.456}");
      fail();
    } catch (UncheckedIOException e) {
      assertThat(e.getMessage(), containsString("123.456"));
      assertThat(e.getMessage(), containsString(" Cannot coerce Floating-point value (123.456)"));

    }
  }

  @Test
  public void shouldFailWhenDeserializingNumericEnumValue_deprecatedVersion() throws Exception {
    ObjectReader reader = DefaultJacksonMappers.getObjectReader(ExampleWithVariousFields.class);
    try {
      reader.readValue("{\"dateTime\":null,\"map\":null,\"number\":null,\"someEnum\":\"1\",\"strings\":null}");
      fail();
    } catch (InvalidFormatException e) {
      assertThat(e.getMessage(), containsString("\"1\""));
      assertThat(e.getMessage(), containsString("not one of the values accepted for Enum class"));
    }
  }

  @Test
  public void shouldFailWhenDeserializingNumericEnumValue() {
    JacksonMapper<ExampleWithVariousFields> mapper =
        DefaultJacksonMappers.jacksonMapper(ExampleWithVariousFields.class);
    try {
      mapper.deserialize("{\"dateTime\":null,\"map\":null,\"number\":null,\"someEnum\":\"1\",\"strings\":null}");
      fail();
    } catch (UncheckedIOException e) {
      assertThat(e.getMessage(), containsString("\"1\""));
      assertThat(e.getMessage(), containsString("not one of the values accepted for Enum class"));
    }
  }

  @Test
  public void shouldMarshallEntitiesConsistentlyWithTwolattes() {
    assertJacksonTwolattesMarshalling(ExampleWithVariousFields.class,
        Json.object(
            "dateTime", "2010-09-13 04:05:06.007",
            "number", 123,
            "strings", Json.array("hello", "bye"),
            "map", Json.object(
                "20100913", 4
            ),
            "someEnum", "SOME_ENUM_VALUE",
            "price", 12
        ),
        new ExampleWithVariousFields(
            new DateTime(2010, 9, 13, 4, 5, 6, 7, ET),
            123,
            ImmutableList.of("hello", "bye"),
            ImmutableMap.of(new LocalDate(2010, 9, 13), money(4).roundToCents()),
            SOME_ENUM_VALUE,
            Price.price(12))
    );
    assertJacksonTwolattesMarshalling(ExampleWithVariousFields.class,
        Json.object(
            "dateTime", Json.NULL,
            "number", Json.NULL,
            "strings", Json.array("hello", Json.NULL),
            "map", Json.NULL,
            "someEnum", Json.NULL,
            "price", Json.NULL
        ),
        new ExampleWithVariousFields(null, null, Arrays.asList("hello", null), null, null, null)
    );
    assertJacksonTwolattesMarshalling(ExampleWithRenamedField.class,
        Json.object("custom_name", 123),
        new ExampleWithRenamedField(123)
    );
  }

  @Test
  public void shouldMarshallCustomTypesConsistentlyWithTwolattes() {
    assertJacksonTwolattesMarshalling(ExampleWithCustomTypes.class,
        Json.object(
            "customField", "string1",
            "customField2", 456,
            "customList", Json.array("string2a", "string2b"),
            "customKey", Json.object(
                "string3", "string4"
            ),
            "customValue", Json.object(
                "string5", "string6",
                "string7", "string8"
            ),
            "customKeyAndValue", Json.object(
                "1.23", Json.array("string9")
            )
        ),
        new ExampleWithCustomTypes(
            new ExperimentName("string1"),
            Id.of(456),
            ImmutableList.of(taos("string2a"), taos("string2b")),
            ImmutableMap.of(OAuthAccessToken.of("string3"), "string4"),
            ImmutableMap.of("string5", Json.string("string6"), "string7", Json.string("string8")),
            ImmutableMap.of(allocationPercentage(1.23), ImmutableList.of(new Comment("string9")))
        )
    );
  }

  @Test
  public void shouldMarshallEntityFieldsConsistentlyWithTwolattes() {
    assertJacksonTwolattesMarshalling(ExampleWithEntityField.class,
        Json.object(
            "field", Json.array(Json.object(
                "customField", "string1",
                "customField2", 456,
                "customList", Json.array("string2a", "string2b"),
                "customKey", Json.object(
                    "string3", "string4"
                ),
                "customValue", Json.object(
                    "string5", "string6",
                    "string7", "string8"
                ),
                "customKeyAndValue", Json.object(
                    "1.23", Json.array("string9")
                )
            )),
            "entityKey", Json.object(
                Json.object(
                    "type", "subsub2",
                    "number", 123,
                    "number2", 456
                ).toString(), 789
            )
        ),
        new ExampleWithEntityField(
            ImmutableList.of(new ExampleWithCustomTypes(
                new ExperimentName("string1"),
                Id.of(456),
                ImmutableList.of(taos("string2a"), taos("string2b")),
                ImmutableMap.of(OAuthAccessToken.of("string3"), "string4"),
                ImmutableMap.of("string5", Json.string("string6"), "string7", Json.string("string8")),
                ImmutableMap.of(allocationPercentage(1.23), ImmutableList.of(new Comment("string9")))
            )),
            ImmutableMap.of(new ExamplePolymorphicEntity.SubSubclass2(123, new QueuedPageId(456)), 789)
        )
    );
  }

  @Test
  public void shouldMarshallPolymorphicEntitiesConsistentlyWithTwolattes() {
    assertJacksonTwolattesMarshalling(ExamplePolymorphicEntity.class,
        Json.object(
            "type", "sub1",
            "number", 123,
            "number1", 456
        ),
        new ExamplePolymorphicEntity.Subclass1(123, 456)
    );
    assertJacksonTwolattesMarshalling(ExamplePolymorphicEntity.class,
        Json.object(
            "type", "subsub2",
            "number", 123,
            "number2", 456
        ),
        new ExamplePolymorphicEntity.SubSubclass2(123, new QueuedPageId(456))
    );
    assertJacksonTwolattesMarshalling(ExamplePolymorphicEntity.SubSubclass2.class,
        Json.object(
            "number", 123,
            "number2", 456
        ),
        new ExamplePolymorphicEntity.SubSubclass2(123, new QueuedPageId(456))
    );
  }

  @Test
  public void assertMarshalling_shouldMarshallPolymorphicEntitiesConsistentlyWithTwolattes() {
    assertMarshalling(DefaultKachingMarshallers.createEntityMarshaller(ExamplePolymorphicEntity.class),
        Json.object(
            "type", "sub1",
            "number", 123,
            "number1", 456
        ),
        new ExamplePolymorphicEntity.Subclass1(123, 456)
    );
    assertMarshalling(DefaultKachingMarshallers.createEntityMarshaller(ExamplePolymorphicEntity.class),
        Json.object(
            "type", "subsub2",
            "number", 123,
            "number2", 456
        ),
        new ExamplePolymorphicEntity.SubSubclass2(123, new QueuedPageId(456))
    );
  }

  @Test
  public void shouldBeAbleToSerializeAndDeserializeMapDirectly_deprecatedVersion() throws Exception {
    ParameterizedTypeImpl genericMapType = new ParameterizedTypeImpl(
        Map.class,
        new Type[]{
            ExampleWithNonAnnotatedField.class,
            new ParameterizedTypeImpl(List.class, new Type[]{ExamplePolymorphicEntity.SubSubclass2.class})
        }
    );
    String string = DefaultJacksonMappers.getObjectWriter(genericMapType).writeValueAsString(ImmutableMap.of(
        new ExampleWithNonAnnotatedField(null, 123),
        ImmutableList.of(new ExamplePolymorphicEntity.SubSubclass2(456, new QueuedPageId(789)))
    ));

    Map<ExampleWithNonAnnotatedField, ImmutableList<ExamplePolymorphicEntity.SubSubclass2>> map =
        DefaultJacksonMappers.getObjectReader(genericMapType).readValue(string);
    Map.Entry<ExampleWithNonAnnotatedField, ImmutableList<ExamplePolymorphicEntity.SubSubclass2>> onlyMapEntry =
        Iterables.getOnlyElement(map.entrySet());

    assertEquals(123, onlyMapEntry.getKey().number);
    ExamplePolymorphicEntity.SubSubclass2 onlyListElement = Iterables.getOnlyElement(onlyMapEntry.getValue());
    assertEquals(456, onlyListElement.number);
    assertEquals(new QueuedPageId(789), onlyListElement.number2);
  }

  @Test
  public void shouldBeAbleToSerializeAndDeserializeMapDirectly() {
    TypeLiteral<Map<ExampleWithNonAnnotatedField, List<ExamplePolymorphicEntity.SubSubclass2>>> typeLiteral =
        new TypeLiteral<Map<ExampleWithNonAnnotatedField, List<ExamplePolymorphicEntity.SubSubclass2>>>() {};
    String string = DefaultJacksonMappers.jacksonMapper(typeLiteral).serialize(ImmutableMap.of(
        new ExampleWithNonAnnotatedField(null, 123),
        ImmutableList.of(new ExamplePolymorphicEntity.SubSubclass2(456, new QueuedPageId(789)))
    ));

    Map<ExampleWithNonAnnotatedField, List<ExamplePolymorphicEntity.SubSubclass2>> map =
        DefaultJacksonMappers.jacksonMapper(typeLiteral).deserialize(string);
    Map.Entry<ExampleWithNonAnnotatedField, List<ExamplePolymorphicEntity.SubSubclass2>> onlyMapEntry =
        Iterables.getOnlyElement(map.entrySet());

    assertEquals(123, onlyMapEntry.getKey().number);
    ExamplePolymorphicEntity.SubSubclass2 onlyListElement = Iterables.getOnlyElement(onlyMapEntry.getValue());
    assertEquals(456, onlyListElement.number);
    assertEquals(new QueuedPageId(789), onlyListElement.number2);
  }

  @Test
  public void failsToDeserializeMapWithOptionAsValue() {
    TypeLiteral<Map<ExampleWithNonAnnotatedField, Option<ExamplePolymorphicEntity.SubSubclass2>>> typeLiteral =
        new TypeLiteral<Map<ExampleWithNonAnnotatedField, Option<ExamplePolymorphicEntity.SubSubclass2>>>() {};
    String string = DefaultJacksonMappers.jacksonMapper(typeLiteral).serialize(ImmutableMap.of(
        new ExampleWithNonAnnotatedField(null, 123),
        Option.some(new ExamplePolymorphicEntity.SubSubclass2(456, new QueuedPageId(789)))
    ));

    assertThrows(UncheckedIOException.class,
        "com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Cannot construct instance of `com.kaching.platform.common.Option` (no Creators, like default constructor, exist): abstract types either need to be mapped to concrete types, have custom deserializer, or contain additional type information\n" +
            " at [Source: (String)\"{\"{\\\"number\\\":123}\":[{\"number\":456,\"number2\":789}]}\"; line: 1, column: 21] (through reference chain: java.util.LinkedHashMap[\"{\"number\":123}\"])",
        () -> DefaultJacksonMappers.jacksonMapper(typeLiteral).deserialize(string));
  }

  @Test
  public void shouldFailOnNonAnnotatedClass_deprecatedVersion() throws Exception {
    try {
      DefaultJacksonMappers.getObjectWriter(ExampleNonAnnotatedClass.class)
          .writeValueAsString(new ExampleNonAnnotatedClass(123));
      fail();
    } catch (InvalidDefinitionException e) {
      assertThat(e.getMessage(), containsString(
          "No serializer found for class com.kaching.jackson.DefaultJacksonMappersTest$ExampleNonAnnotatedClass"));
    }
  }

  @Test
  public void shouldFailOnNonAnnotatedClass() {
    try {
      DefaultJacksonMappers.jacksonMapper(ExampleNonAnnotatedClass.class)
          .serialize(new ExampleNonAnnotatedClass(123));
      fail();
    } catch (UncheckedIOException e) {
      assertThat(e.getMessage(), containsString(
          "No serializer found for class com.kaching.jackson.DefaultJacksonMappersTest$ExampleNonAnnotatedClass"));
    }
  }

  @Test
  public void shouldOmitNonAnnotatedFieldsWhenWriting() {
    assertJacksonTwolattesMarshalling(ExampleWithNonAnnotatedField.class,
        Json.object("number", 123),
        new ExampleWithNonAnnotatedField(new DateTime(2010, 9, 13, 4, 5, 6, 7, ET), 123)
    );
  }

  @Test
  public void shouldOmitNullOptionalFieldsWhenWriting_deprecatedVersion() throws Exception {
    String jsonString = DefaultJacksonMappers.getObjectWriter(ExampleWithOptionalField.class)
        .writeValueAsString(new ExampleWithOptionalField(null, null, 123));
    assertThat(jsonString, containsString("\"mandatoryNumber\":null"));
    assertThat(jsonString, not(containsString("optionalNumber1")));
    assertThat(jsonString, containsString("\"optionalNumber2\":123"));
  }

  @Test
  public void shouldOmitNullOptionalFieldsWhenWriting() {
    String jsonString = DefaultJacksonMappers.jacksonMapper(ExampleWithOptionalField.class)
        .serialize(new ExampleWithOptionalField(null, null, 123));
    assertThat(jsonString, containsString("\"mandatoryNumber\":null"));
    assertThat(jsonString, not(containsString("optionalNumber1")));
    assertThat(jsonString, containsString("\"optionalNumber2\":123"));
  }

  @Test
  public void shouldIgnoreUnknownOrNonAnnotatedFieldsWhenReading_deprecatedVersion() throws Exception {
    ExampleWithNonAnnotatedField readValue = DefaultJacksonMappers.getObjectReader(ExampleWithNonAnnotatedField.class)
        .readValue("" +
            "{" +
            "  \"dateTime\":\"2010-09-13 04:05:06.007\"," +
            "  \"number\":123," +
            "  \"someRandomProperty\":45634526" +
            "}");
    assertNull(readValue.dateTime);
    assertEquals(123, readValue.number);
  }

  @Test
  public void shouldIgnoreUnknownOrNonAnnotatedFieldsWhenReading() {
    ExampleWithNonAnnotatedField readValue = DefaultJacksonMappers.jacksonMapper(ExampleWithNonAnnotatedField.class)
        .deserialize("" +
            "{" +
            "  \"dateTime\":\"2010-09-13 04:05:06.007\"," +
            "  \"number\":123," +
            "  \"someRandomProperty\":45634526" +
            "}");
    assertNull(readValue.dateTime);
    assertEquals(123, readValue.number);
  }

  @Test
  public void testCanSerialize() {
    assertFalse(DefaultJacksonMappers.canSerialize(ExampleNonAnnotatedClass.class));
    assertTrue(DefaultJacksonMappers.canSerialize(ExamplePolymorphicEntity.class));
    assertTrue(DefaultJacksonMappers.canSerialize(ExampleWithCustomTypes.class));
    assertTrue(DefaultJacksonMappers.canSerialize(ExampleWithEntityField.class));
    assertTrue(DefaultJacksonMappers.canSerialize(ExampleWithNonAnnotatedField.class));
    assertTrue(DefaultJacksonMappers.canSerialize(ExampleWithRenamedField.class));
    assertTrue(DefaultJacksonMappers.canSerialize(ExampleWithVariousFields.class));
  }

  @Test
  public void multipleMappersGetExactSameMapperInstance() {
    JacksonMapper<ExampleWithEntityField> mapper1 = DefaultJacksonMappers.jacksonMapper(ExampleWithEntityField.class);
    JacksonMapper<ExampleWithEntityField> mapper2 = DefaultJacksonMappers.jacksonMapper(ExampleWithEntityField.class);
    assertSame(mapper1, mapper2);
  }

  @Ignore("The cache is static, so the numbers unpredictable when other tests are also being run")
  @Test
  public void logCacheStats() {
    DefaultJacksonMappers.jacksonMapper(Integer.class);
    DefaultJacksonMappers.jacksonMapper(ExampleWithVariousFields.class);
    DefaultJacksonMappers.jacksonMapper(ExampleWithEntityField.class);
    DefaultJacksonMappers.jacksonMapper(ExampleWithEntityField.class);
    DefaultJacksonMappers.jacksonMapper(ExampleWithEntityField.class);
    DefaultJacksonMappers.jacksonMapper(ExampleWithEntityField.class);
    DefaultJacksonMappers.jacksonMapper(ExampleWithEntityField.class);
    DefaultJacksonMappers.jacksonMapper(ExampleWithEntityField.class);

    String message = DefaultJacksonMappers.logCacheStats();

    assertThat(message, containsString("hitCount=5"));
    assertThat(message, containsString("missCount=3"));
    assertThat(message, containsString("Count of Mapper cache: 3"));
  }

  @Test
  public void logCacheStats_doesntThrow() {
    DefaultJacksonMappers.jacksonMapper(Integer.class);
    DefaultJacksonMappers.jacksonMapper(ExampleWithVariousFields.class);
    DefaultJacksonMappers.jacksonMapper(ExampleWithEntityField.class);

    DefaultJacksonMappers.logCacheStats();
  }

  @Test
  public void readFrom() throws IOException {
    JacksonMapper<ExampleWithNonAnnotatedField> mapper =
        DefaultJacksonMappers.jacksonMapper(ExampleWithNonAnnotatedField.class);

    ExampleWithNonAnnotatedField entity;
    try (Reader reader = new StringReader("{ \"number\": 123 }")) {
      entity = mapper.readFrom(reader);
    }
    assertEquals(123, entity.number);
  }

  @Test
  public void writeTo() throws IOException {
    JacksonMapper<ExampleWithNonAnnotatedField> mapper =
        DefaultJacksonMappers.jacksonMapper(ExampleWithNonAnnotatedField.class);

    StringWriter writer = new StringWriter();
    ExampleWithNonAnnotatedField entity = new ExampleWithNonAnnotatedField(null, 123);
    try (Writer ignored = writer) {
      mapper.writeTo(writer, entity);
    }
    assertEquals("{\"number\":123}", writer.toString());
  }

  @com.twolattes.json.Entity
  public static class ExampleWithVariousFields {

    @Value DateTime dateTime;
    @Value Integer number;
    @Value List<String> strings;
    @Value Map<LocalDate, Money> map;
    @Value SomeEnum someEnum;
    @Value EquityPriceOrCleanPrice price;

    @SuppressWarnings("unused")
    ExampleWithVariousFields() { /* JSON */ }

    ExampleWithVariousFields(
        DateTime dateTime, Integer number, List<String> strings, Map<LocalDate, Money> map, SomeEnum someEnum,
        EquityPriceOrCleanPrice price) {
      this.dateTime = dateTime;
      this.number = number;
      this.strings = strings;
      this.map = map;
      this.someEnum = someEnum;
      this.price = price;
    }

    public enum SomeEnum {
      SOME_ENUM_VALUE,
      SOME_OTHER_ENUM_VALUE
    }

  }

  @Entity
  static class ExampleWithCustomTypes {

    @Value(type = ExperimentName.JsonType.class) ExperimentName customField;
    @Value(type = IdJsonType.class) Id<MockEntity> customField2;
    @Value(types = {TaosAdmin.JsonType.class}) List<TaosAdmin> customList;
    @Value(type = OAuthAccessToken.JsonType.class) Map<OAuthAccessToken, String> customKey;
    @Value(type = JsonJsonType.class) Map<String, Json.Value> customValue;
    @Value(types = {AllocationPercentage.JsonType.class, Comment.JsonType.class})
    Map<AllocationPercentage, List<Comment>> customKeyAndValue;

    @SuppressWarnings("unused")
    ExampleWithCustomTypes() { /* JSON */ }

    ExampleWithCustomTypes(ExperimentName customField,
                           Id<MockEntity> customField2,
                           List<TaosAdmin> customList,
                           Map<OAuthAccessToken, String> customKey,
                           Map<String, Json.Value> customValue,
                           Map<AllocationPercentage, List<Comment>> customKeyAndValue) {
      this.customField = customField;
      this.customField2 = customField2;
      this.customList = customList;
      this.customKey = customKey;
      this.customValue = customValue;
      this.customKeyAndValue = customKeyAndValue;
    }

  }

  @Entity
  static class ExampleWithEntityField {

    @Value List<ExampleWithCustomTypes> field;
    @Value Map<ExamplePolymorphicEntity, Integer> entityKey;

    @SuppressWarnings("unused")
    ExampleWithEntityField() { /* JSON */ }

    ExampleWithEntityField(
        List<ExampleWithCustomTypes> field,
        Map<ExamplePolymorphicEntity, Integer> entityKey) {
      this.field = field;
      this.entityKey = entityKey;
    }

  }

  private static class ExampleNonAnnotatedClass {

    int number;

    @SuppressWarnings("unused")
    ExampleNonAnnotatedClass() { /* JSON */ }

    ExampleNonAnnotatedClass(int number) {
      this.number = number;
    }

  }

  @Entity
  private static class ExampleWithOptionalField {

    @Value Integer mandatoryNumber;
    @Value(optional = true) Integer optionalNumber1;
    @Value(optional = true) Integer optionalNumber2;

    @SuppressWarnings("unused")
    ExampleWithOptionalField() { /* JSON */ }

    ExampleWithOptionalField(Integer mandatoryNumber, Integer optionalNumber1, Integer optionalNumber2) {
      this.mandatoryNumber = mandatoryNumber;
      this.optionalNumber1 = optionalNumber1;
      this.optionalNumber2 = optionalNumber2;
    }

  }

  @Entity
  static class ExampleWithNonAnnotatedField {

    DateTime dateTime;
    @Value int number;

    @SuppressWarnings("unused")
    ExampleWithNonAnnotatedField() { /* JSON */ }

    ExampleWithNonAnnotatedField(DateTime dateTime, int number) {
      this.dateTime = dateTime;
      this.number = number;
    }

  }

  @Entity
  private static class ExampleWithRenamedField {

    @Value(name = "custom_name") int number;

    @SuppressWarnings("unused")
    ExampleWithRenamedField() { /* JSON */ }

    ExampleWithRenamedField(int number) {
      this.number = number;
    }

  }

  @Entity(discriminatorName = "type", subclasses = {
      ExamplePolymorphicEntity.Subclass1.class,
      ExamplePolymorphicEntity.SubSubclass2.class
  })
  abstract static class ExamplePolymorphicEntity {

    @Value
    int number;

    @SuppressWarnings("unused")
    ExamplePolymorphicEntity() { /* JSON */ }

    ExamplePolymorphicEntity(int number) {
      this.number = number;
    }

    @Entity(discriminator = "sub1")
    private static class Subclass1 extends ExamplePolymorphicEntity {

      @Value
      int number1;

      @SuppressWarnings("unused")
      Subclass1() { /* JSON */ }

      Subclass1(int number, int number1) {
        super(number);
        this.number1 = number1;
      }

    }

    @Entity(discriminator = "sub2")
    private static class Subclass2 extends ExamplePolymorphicEntity {

      @Value(type = QueuedPageId.JsonType.class) QueuedPageId number2;

      @SuppressWarnings("unused")
      Subclass2() { /* JSON */ }

      Subclass2(int number, QueuedPageId number2) {
        super(number);
        this.number2 = number2;
      }

    }

    @Entity(discriminator = "subsub2")
    private static class SubSubclass2 extends Subclass2 {

      @SuppressWarnings("unused")
      SubSubclass2() { /* JSON */ }

      SubSubclass2(int number, QueuedPageId number2) {
        super(number, number2);
      }

    }

  }

}