package com.kaching.platform;

import static com.kaching.platform.common.Thunk.thunk;

import org.reflections.Reflections;
import org.reflections.util.ConfigurationBuilder;
import org.reflections.util.FilterBuilder;

import com.kaching.platform.common.Thunk;

public class WReflections {

  private static final String KACHING_PACKAGE = "com.kaching";
  private static final String WEALTHFRONT_PACKAGE = "com.wealthfront";
  
  public static final Thunk<Reflections> REFLECTIONS = thunk(() -> new Reflections(new ConfigurationBuilder()
      .forPackages(WEALTHFRONT_PACKAGE, KACHING_PACKAGE)
      .filterInputsBy(new FilterBuilder()
          .includePackage(WEALTHFRONT_PACKAGE)
          .includePackage(KACHING_PACKAGE)
      )));
  
}
