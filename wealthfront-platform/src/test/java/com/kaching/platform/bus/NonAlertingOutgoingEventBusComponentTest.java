package com.kaching.platform.bus;

import static com.google.inject.name.Names.named;
import static com.kaching.platform.guice.TestingApplicationOptions.OPTIONS;

import java.util.Set;

import org.junit.Test;

import com.google.common.collect.ImmutableSet;
import com.google.inject.AbstractModule;
import com.google.inject.TypeLiteral;
import com.kaching.mq.rabbitmq.RabbitMQModule;
import com.kaching.platform.bus.impl.OutgoingEvent;
import com.kaching.platform.bus.impl.ProcessOutgoingEvent;
import com.kaching.platform.bus.impl.ProcessOutgoingEventWithAllowedDestinations;
import com.kaching.platform.components.Component;
import com.kaching.platform.components.KawalaTester;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.zk.ZkAnnounceModule;

public class NonAlertingOutgoingEventBusComponentTest {

  @Test
  public void shouldBeValid() {
    KawalaTester.selfContained().validateComponent(MyService.class);
    KawalaTester.selfContained().validateComponent(MyServiceWithAllowedDestinations.class);
  }

  @Component(
      dependsOn = NonAlertingOutgoingEventBusComponent.class,
      modules = {
          MyService.Module.class
      },
      queries = ProcessOutgoingEventImpl.class
  )
  private static class MyService {

    static class Module extends AbstractModule {

      @Override
      protected void configure() {
        install(ZkAnnounceModule.newZkAnnounceModule(KachingServices.UM.class, OPTIONS));
        install(new BusModuleBuilder()
            .withProcessOutgoingEventClass(ProcessOutgoingEventImpl.class)
            .build());
        install(new RabbitMQModule());
      }
    }
  }

  @Component(
      dependsOn = NonAlertingOutgoingEventBusComponent.class,
      modules = {
          MyServiceWithAllowedDestinations.Module.class
      },
      queries = ProcessOutgoingEventWithAllowedDestinationsImpl.class
  )
  private static class MyServiceWithAllowedDestinations {

    static class Module extends AbstractModule {

      @Override
      protected void configure() {
        install(ZkAnnounceModule.newZkAnnounceModule(KachingServices.UM.class, OPTIONS));
        install(new BusModuleBuilder()
            .withProcessOutgoingEventClass(ProcessOutgoingEventWithAllowedDestinationsImpl.class)
            .build());
        install(new RabbitMQModule());
        bind(new TypeLiteral<Set<Class<? extends ServiceKind>>>() {})
            .annotatedWith(named("event-bus-allowed-destinations"))
            .toInstance(ImmutableSet.of(KachingServices.FBANK.class));
      }
    }
  }

  private static class ProcessOutgoingEventImpl extends ProcessOutgoingEvent {

    private final Id<OutgoingEvent> id;

    ProcessOutgoingEventImpl(Id<OutgoingEvent> id) {
      this.id = id;
    }

    @Override
    public Boolean process() {
      return process(id);
    }
  }

  private static class ProcessOutgoingEventWithAllowedDestinationsImpl extends
      ProcessOutgoingEventWithAllowedDestinations {

    private final Id<OutgoingEvent> id;

    ProcessOutgoingEventWithAllowedDestinationsImpl(Id<OutgoingEvent> id) {
      this.id = id;
    }

    @Override
    public Boolean process() {
      return process(id);
    }
  }

}