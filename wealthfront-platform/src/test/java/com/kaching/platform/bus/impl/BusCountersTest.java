package com.kaching.platform.bus.impl;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.testing.PersistentTestRunner;
import com.kaching.util.tests.PersistentTest;

@RunWith(PersistentTestRunner.class)
@PersistentTest()
public class BusCountersTest {

  private BusCounters counters;

  @Before
  public void before(Transacter transacter) {
    counters = new BusCounters();
    counters.transacter = transacter;
  }

  @Test
  public void getIncomingEvents() {
    assertEquals(0, counters.getIncomingEvents());
  }

  @Test
  public void getMysqlSchema() {
    assertEquals(Option.some("bank"), counters.getMysqlSchema("**************************/bank"));
    assertEquals(Option.some("bank"), counters.getMysqlSchema("**************************:1234/bank"));
    assertEquals(Option.some("bank"), counters.getMysqlSchema("**************************/bank?foo=bar"));
    assertEquals(Option.some("bank"), counters.getMysqlSchema("**************************/bank?foo=bar&foo=bar"));
    assertEquals(Option.some("bank"), counters.getMysqlSchema("**************************/bank?foo=bar/baz"));
    assertEquals(Option.some("bank"), counters.getMysqlSchema("jdbc:mysql://primary,secondary/bank"));
    assertEquals(Option.none(), counters.getMysqlSchema("**************************"));
    assertEquals(Option.none(), counters.getMysqlSchema("jdbc:hsqldb:mem:db_0"));
  }

}