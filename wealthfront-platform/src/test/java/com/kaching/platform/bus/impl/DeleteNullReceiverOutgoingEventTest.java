package com.kaching.platform.bus.impl;

import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertTrue;

import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;

import com.kaching.platform.bus.Event;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.queryengine.exceptions.InvalidArgumentException;
import com.kaching.platform.testing.PersistentTestRunner;
import com.kaching.util.tests.PersistentTest;
import com.twolattes.json.Json;

@RunWith(PersistentTestRunner.class)
@PersistentTest(
    entities = {
        OutgoingEvent.class
    })
public class DeleteNullReceiverOutgoingEventTest {

  @Test
  public void process(Transacter transacter) {
    OutgoingEvent event = createOutgoingEvent(null);
    Id<OutgoingEvent> id = transacter.save(event);

    transacter.executeWithSession(session -> {
      DeleteNullReceiverOutgoingEvent query = new DeleteNullReceiverOutgoingEvent(id);
      query.session = session;

      query.process();
    });

    transacter.executeWithReadOnlySession(session -> assertTrue(session.get(OutgoingEvent.class, id).isEmpty()));
  }

  @Test(expected = InvalidArgumentException.class)
  public void processShouldThrowWhenTheEventIsNotDropped(Transacter transacter) {
    ServiceKind um = KachingServices.singleton(KachingServices.UM.class);
    Id<OutgoingEvent> id = transacter.save(createOutgoingEvent(um));

    transacter.executeWithSession(session -> {
      DeleteNullReceiverOutgoingEvent query = new DeleteNullReceiverOutgoingEvent(id);
      query.session = session;

      query.process();
    });
  }

  private OutgoingEvent createOutgoingEvent(ServiceKind receiver) {
    Message message = new Message(EventId.random(), EventType.of(Event1.class), Json.string("event"));
    return new OutgoingEvent(receiver, message, new DateTime(2015, 10, 1, 2, 0, 0, ET));
  }

  private static class Event1 implements Event {}

}
