package com.kaching.platform.bus.impl;

import static com.wealthfront.test.Assert.assertEquals;
import static com.wealthfront.test.Assert.assertSameInstant;
import static com.wealthfront.test.Assert.fail;
import static com.wealthfront.util.time.DateTimeZones.ET;

import org.hibernate.exception.ConstraintViolationException;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.platform.testing.PersistentTestRunner;
import com.kaching.util.tests.PersistentTest;
import com.twolattes.json.Json;

@RunWith(PersistentTestRunner.class)
@PersistentTest(
    entities = {
        OutgoingEvent.class
    })
public class OutgoingEventTest {

  private ServiceKind um;
  private ServiceKind bi;
  private Message message;

  @Before
  public void before() {
    um = KachingServices.singleton(KachingServices.UM.class);
    bi = KachingServices.singleton(KachingServices.BI.class);
    message = new Message(
        EventId.fromString("1e9777ae-d905-42d1-b8f8-a6220fea37d5"),
        new EventType("type"),
        Json.string("event"));
  }

  @Test
  public void persist(Transacter transacter) {
    final Id<OutgoingEvent> id = transacter.save(new OutgoingEvent(um, message, new DateTime(2015, 10, 1, 2, 0, 0, ET)));

    transacter.execute(new WithSession() {
      @Override
      public void run(DbSession session) {
        OutgoingEvent loaded = session.getOrThrow(OutgoingEvent.class, id);

        assertEquals(id, loaded.getId());
        assertEquals(EventId.fromString("1e9777ae-d905-42d1-b8f8-a6220fea37d5"), loaded.getEventId());
        assertEquals(um, loaded.getReceiver());
        Message message = loaded.getMessage();
        assertEquals(EventId.fromString("1e9777ae-d905-42d1-b8f8-a6220fea37d5"), message.getEventId());
        assertEquals(new EventType("type"), message.getEventType());
        assertEquals(Json.string("event"), message.getEvent());

        assertSameInstant(new DateTime(2015, 10, 1, 2, 0, 0, ET), loaded.getCreatedAt());
        assertEquals("type", loaded.describeEventType());
      }
    });
  }

  @Test
  public void eventIdAndReceiverShouldBeUnique(Transacter transacter) {
    transacter.save(new OutgoingEvent(um, message, new DateTime(2015, 10, 1, 2, 0, 0, ET)));
    transacter.save(new OutgoingEvent(bi, message, new DateTime(2015, 10, 1, 2, 0, 0, ET)));
    try {
      transacter.save(new OutgoingEvent(bi, message, new DateTime(2015, 10, 1, 2, 0, 0, ET)));
      fail();
    } catch (ConstraintViolationException expected) {
    }
  }

}