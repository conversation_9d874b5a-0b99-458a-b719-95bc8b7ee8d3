package com.kaching.platform.components;

import static com.google.inject.Guice.createInjector;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.util.Map;

import org.junit.Test;

import com.google.inject.AbstractModule;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.Provider;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.kaching.platform.common.Errors;
import com.kaching.platform.components.LifecycleModule.LifecycledMatcher;
import com.kaching.platform.converters.Optional;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.PostProcessor;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.queryengine.QueryDriver;

public class LifecycleModuleTest {

  @Test
  public void matcher() {
    LifecycledMatcher matcher = new LifecycledMatcher();
    assertTrue(matcher.matches(TypeLiteral.get(Parking.class)));
    assertFalse(matcher.matches(TypeLiteral.get(Car.class)));
    assertFalse(matcher.matches(TypeLiteral.get(String.class)));
  }

  @Test
  public void lifecycledInjection() {
    assertTrue(
        "should be able to park since the parking ought to be initialized",
        injector().getInstance(Car.class).hasParked);
  }

  private Injector injector() {
    return createInjector(new LifecycleModule(), new AbstractModule() {
      @Override
      protected void configure() {
      }

      @Provides
      @Singleton
      @SuppressWarnings("unused")
      QueryDriver qd(final Injector injector) {
        return new QueryDriver() {
          @Override
          public <T> Object execute(
              Query<T> query, Map<Key<?>, Object> relaxedCache,
              Provider<? extends PostProcessor> postProcessor) {
            injector.injectMembers(query);
            return query.process();
          }
        };
      }
    });
  }

  @Singleton
  @Lifecycled(onStartup = CreateOneSpot.class)
  static class Parking {

    int numSpots = 0;
  }

  static class Car {

    boolean hasParked = false;

    @Inject
    Car(Parking parking) {
      if (parking.numSpots > 0) {
        parking.numSpots--;
        hasParked = true;
      }
    }
  }

  static class CreateOneSpot extends AbstractQuery<Errors> {

    private final int numSpots;

    CreateOneSpot(@Optional("1") int numSpots) {
      this.numSpots = numSpots;
    }

    @Inject Parking parking;

    @Override
    public Errors process() {
      parking.numSpots = numSpots;
      return null;
    }

  }

}
