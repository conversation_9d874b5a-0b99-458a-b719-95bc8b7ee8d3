package com.kaching.platform.guice;

import static com.google.common.collect.Lists.newArrayList;
import static com.google.inject.Guice.createInjector;
import static com.google.inject.name.Names.named;
import static com.kaching.annotations.AuroraImpl.aurora;
import static com.kaching.platform.guice.KcHibernateModule.newKcHibernateModule;
import static com.kaching.platform.guice.TestingApplicationOptions.OPTIONS;
import static com.kaching.platform.queryengine.QueryScope.ScopeRules.RELAXED;
import static com.kaching.security.Aes.generateKey;
import static com.kaching.security.Ciphers.aes;
import static java.util.Collections.singleton;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.security.SecureRandom;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

import org.hibernate.cfg.Configuration;
import org.hibernate.cfg.Environment;
import org.junit.Test;

import com.google.inject.AbstractModule;
import com.google.inject.ConfigurationException;
import com.google.inject.ImplementedBy;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.Module;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;
import com.kaching.annotations.AuroraImpl;
import com.kaching.platform.bus.impl.IncomingEvent;
import com.kaching.platform.bus.impl.OutgoingEvent;
import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.AbstractHibernateEntity;
import com.kaching.platform.hibernate.C3P0ConnectionCustomizer;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.HibernateModule.Hbm2ddl;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.hibernate.TransacterImpl;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.platform.queryengine.HibernateQueryDriver;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.queryengine.QueryEngineModule;
import com.kaching.platform.queryengine.QueryScope;
import com.kaching.platform.queryengine.QueryScope.ScopeRules;
import com.kaching.platform.queryengine.QueryScoped;
import com.kaching.platform.queryengine.authorization.AuthorizationTokenDecryptionResult;
import com.kaching.platform.queryengine.authorization.AuthorizationTokenDecryptionResultProvider;
import com.kaching.platform.zk.ZkModule;
import com.kaching.security.Cipher;
import com.wealthfront.test.EquivalenceTester;

public class KcHibernateModuleTest {

  @Test
  public void c3p0Configuration() {
    Injector injector = createInjector(
        new QueryEngineModule(9000, RELAXED, OPTIONS, HibernateQueryDriver.class),
        new OptionsModule(OPTIONS),
        new KcHibernateModule(new TestingApplicationOptions() {{
          this.jdbcUrl = "jdbc:mysql:whatever";
          this.c3p0MinThreadPoolSize = 1;
          this.c3p0MaxThreadPoolSize = 2;
          this.c3p0IdleThreadTimeoutSeconds = 3;
          this.c3p0ThreadAcquireIncrement = 4;
          this.c3p0IdleThreadTimeoutExcessConnectionsSeconds = 5;
        }}));

    Configuration configuration = injector.getInstance(Configuration.class);
    assertEquals("1", configuration.getProperty(Environment.C3P0_MIN_SIZE));
    assertEquals("2", configuration.getProperty(Environment.C3P0_MAX_SIZE));
    assertEquals("3", configuration.getProperty(Environment.C3P0_TIMEOUT));
    assertEquals("4", configuration.getProperty(Environment.C3P0_ACQUIRE_INCREMENT));
    assertEquals("5", configuration.getProperty("hibernate.c3p0.maxIdleTimeExcessConnections"));
    assertEquals("true", configuration.getProperty(Environment.AUTOCOMMIT));
    assertNull(configuration.getProperty(Environment.ISOLATION));
    assertEquals(
        C3P0ConnectionCustomizer.class.getName(),
        configuration.getProperty("hibernate.c3p0.connectionCustomizerClassName"));
  }

  @ImplementedBy(InSessionInjectedClassImpl.class)
  interface InSessionInjectedClass {

    DbSession getDbSession();

  }

  static class InSessionInjectedClassImpl implements InSessionInjectedClass {

    @Inject DbSession dbSession;

    @Override
    public DbSession getDbSession() {
      return dbSession;
    }

  }

  @Test
  public void bindsHibernateEntities() {
    Module module = newKcHibernateModule(
        "sa", "",
        "jdbc:hsqldb:mem:first",
        Hbm2ddl.CREATE_DROP.hibernateName,
        null,
        5,
        15,
        180,
        1,
        600,
        60,
        AbstractHibernateEntity.class);

    Module fakeZkModule = new ZkModule.FakeZkModule();

    Injector injector = createInjector(module, fakeZkModule,
        new AbstractModule() {
          @Override
          protected void configure() {
            ScopeRules scopeRules = ScopeRules.STRICT;
            QueryScope queryScope = new QueryScope(scopeRules);
            bind(ScopeRules.class).toInstance(scopeRules);
            bind(QueryScope.class).toInstance(queryScope);
            bindScope(QueryScoped.class, queryScope);
            bind(Key.get(new TypeLiteral<Option<Class<? extends Query>>>() {}))
                .toProvider(QueryEngineModule.OptionalQueryClassProvider.class);
            bind(Cipher.class).annotatedWith(named("RequestAuthorizationToken"))
                .toInstance(aes(generateKey(256), new SecureRandom()));
            bind(Key.get(AuthorizationTokenDecryptionResult.class))
                .toProvider(AuthorizationTokenDecryptionResultProvider.class);
          }
        }
    );

    assertEquals(
        singleton(AbstractHibernateEntity.class),
        injector.getInstance(Key.get(new TypeLiteral<Set<Class<?>>>() {}, Names.named("hibernate-entities"))));
  }

  @Test
  public void bindsHibernateEntities_withAnnotations() {
    Module module = newKcHibernateModule(
        "sa", "",
        "jdbc:hsqldb:mem:first",
        Hbm2ddl.CREATE_DROP.hibernateName,
        null,
        5,
        15,
        180,
        1,
        600,
        60,
        AbstractHibernateEntity.class);

    AuroraImpl aurora = aurora();

    Module auroraModule = newKcHibernateModule(
        "sa", "",
        "jdbc:hsqldb:mem:first",
        Hbm2ddl.CREATE_DROP.hibernateName,
        aurora,
        5,
        15,
        180,
        1,
        600,
        60,
        IncomingEvent.class);

    Module annotatedModule = newKcHibernateModule(
        "sa", "",
        "jdbc:hsqldb:mem:first",
        Hbm2ddl.CREATE_DROP.hibernateName,
        named("something"),
        5,
        15,
        180,
        1,
        600,
        60,
        OutgoingEvent.class);

    Module fakeZkModule = new ZkModule.FakeZkModule();

    Injector injector = createInjector(module, auroraModule, annotatedModule, fakeZkModule,
        new AbstractModule() {
          @Override
          protected void configure() {
            ScopeRules scopeRules = ScopeRules.RELAXED;
            QueryScope queryScope = new QueryScope(scopeRules);
            bind(ScopeRules.class).toInstance(scopeRules);
            bind(QueryScope.class).toInstance(queryScope);
            bindScope(QueryScoped.class, queryScope);
            bind(Key.get(new TypeLiteral<Option<Class<? extends Query>>>() {}))
                .toProvider(QueryEngineModule.OptionalQueryClassProvider.class);
            bind(Cipher.class).annotatedWith(named("RequestAuthorizationToken"))
                .toInstance(aes(generateKey(256), new SecureRandom()));
            bind(Key.get(AuthorizationTokenDecryptionResult.class))
                .toProvider(AuthorizationTokenDecryptionResultProvider.class);
          }
        }
    );

    assertEquals(
        singleton(IncomingEvent.class),
        injector.getInstance(
            Key.get(new TypeLiteral<Set<Class<?>>>() {}, Names.named("aurora-hibernate-entities"))));

    assertEquals(
        singleton(AbstractHibernateEntity.class),
        injector.getInstance(
            Key.get(new TypeLiteral<Set<Class<?>>>() {}, Names.named("hibernate-entities"))));
  }

  @Test
  public void verifyTransacterBinding_DbSession() {
    Module module = newKcHibernateModule(
        "sa", "",
        "jdbc:hsqldb:mem:first",
        Hbm2ddl.CREATE_DROP.hibernateName,
        null,
        5,
        15,
        180,
        1,
        600,
        60
    );
    Module fakeZkModule = new ZkModule.FakeZkModule();

    Injector injector = createInjector(module, fakeZkModule,
        new AbstractModule() {
          @Override
          protected void configure() {
            ScopeRules scopeRules = ScopeRules.STRICT;
            QueryScope queryScope = new QueryScope(scopeRules);
            bind(ScopeRules.class).toInstance(scopeRules);
            bind(QueryScope.class).toInstance(queryScope);
            bindScope(QueryScoped.class, queryScope);
            bind(Key.get(new TypeLiteral<Option<Class<? extends Query>>>() {}))
                .toProvider(QueryEngineModule.OptionalQueryClassProvider.class);
            bind(Cipher.class).annotatedWith(named("RequestAuthorizationToken"))
                .toInstance(aes(generateKey(256), new SecureRandom()));
            bind(Key.get(AuthorizationTokenDecryptionResult.class))
                .toProvider(AuthorizationTokenDecryptionResultProvider.class);
          }
        }
    );

    Transacter transacter = injector.getInstance(Key.get(Transacter.class));
    assertTrue(transacter instanceof InjectingTransacter);

    transacter.execute(new WithSession() {
      @Inject InSessionInjectedClass inSessionInjectedClass;

      @Override
      public void run(DbSession session) {
        assertSame(inSessionInjectedClass.getDbSession(), session);
      }
    });
  }

  @Test
  public void verifyTransacterBindings() {
    Module firstModule = newKcHibernateModule(
        "sa", "",
        "jdbc:hsqldb:mem:first",
        Hbm2ddl.CREATE_DROP.hibernateName,
        null,
        5,
        15,
        180,
        1,
        600,
        60);
    Module secondModule = newKcHibernateModule(
        "sa", "",
        "jdbc:hsqldb:mem:second",
        Hbm2ddl.CREATE_DROP.hibernateName,
        named("second"),
        5,
        15,
        180,
        1,
        600,
        60);
    Module thirdModule = newKcHibernateModule(
        "sa", "",
        "jdbc:hsqldb:mem:first",
        Hbm2ddl.CREATE_DROP.hibernateName,
        named("third"),
        5,
        15,
        180,
        1,
        600,
        60);
    Module fakeZkModule = new ZkModule.FakeZkModule();

    Injector injector = createInjector(firstModule, secondModule, thirdModule, fakeZkModule,
        new AbstractModule() {
          @Override
          protected void configure() {
            ScopeRules scopeRules = ScopeRules.RELAXED;
            QueryScope queryScope = new QueryScope(scopeRules);
            bind(ScopeRules.class).toInstance(scopeRules);
            bind(QueryScope.class).toInstance(queryScope);
            bindScope(QueryScoped.class, queryScope);
            bind(Key.get(new TypeLiteral<Option<Class<? extends Query>>>() {}))
                .toProvider(QueryEngineModule.OptionalQueryClassProvider.class);
            bind(Cipher.class).annotatedWith(named("RequestAuthorizationToken"))
                .toInstance(aes(generateKey(256), new SecureRandom()));
            bind(Key.get(AuthorizationTokenDecryptionResult.class))
                .toProvider(AuthorizationTokenDecryptionResultProvider.class);
          }
        });

    Transacter firstTransacter = injector.getInstance(Key.get(Transacter.class));
    Transacter secondTransacter = injector.getInstance(Key.get(Transacter.class, named("second")));
    Transacter thirdTransacter = injector.getInstance(Key.get(Transacter.class, named("third")));

    // all different
    EquivalenceTester.check(
        newArrayList(firstTransacter),
        newArrayList(secondTransacter),
        newArrayList(thirdTransacter));

    // singletoness
    assertEquals("should be singleton", firstTransacter, injector.getInstance(Transacter.class));

    // should be InjectingTransacter
    assertTrue(firstTransacter instanceof InjectingTransacter);
    assertTrue(((InjectingTransacter) firstTransacter).getDelegate() instanceof TransacterImpl);
    assertTrue(secondTransacter instanceof InjectingTransacter);
    assertTrue(((InjectingTransacter) secondTransacter).getDelegate() instanceof TransacterImpl);
    assertTrue(thirdTransacter instanceof InjectingTransacter);
    assertTrue(((InjectingTransacter) thirdTransacter).getDelegate() instanceof TransacterImpl);

    try {
      injector.getInstance(Key.get(Transacter.class, named("fourth")));
      fail();
    } catch (ConfigurationException e) {
      // expected
    }

    try {
      injector.getInstance(Key.get(Transacter.class, named("fifth")));
      fail();
    } catch (ConfigurationException e) {
      // expected
    }
  }

  @Test
  public void verifyRetryingTransacterBindings() {
    Module firstModule = newKcHibernateModule(
        "sa", "",
        "jdbc:hsqldb:mem:first",
        Hbm2ddl.CREATE_DROP.hibernateName,
        null,
        5,
        15,
        180,
        1,
        600,
        60);
    Module secondModule = newKcHibernateModule(
        "sa", "",
        "jdbc:hsqldb:mem:second",
        Hbm2ddl.CREATE_DROP.hibernateName,
        named("second"),
        5,
        15,
        180,
        1,
        600,
        60);
    Module thirdModule = newKcHibernateModule(
        "sa", "",
        "jdbc:hsqldb:mem:first",
        Hbm2ddl.CREATE_DROP.hibernateName,
        named("third"),
        5,
        15,
        180,
        1,
        600,
        60);
    Module fakeZkModule = new ZkModule.FakeZkModule();

    Injector injector = createInjector(firstModule, secondModule, thirdModule, fakeZkModule,
        new AbstractModule() {
          @Override
          protected void configure() {
            ScopeRules scopeRules = ScopeRules.RELAXED;
            QueryScope queryScope = new QueryScope(scopeRules);
            bind(ScopeRules.class).toInstance(scopeRules);
            bind(QueryScope.class).toInstance(queryScope);
            bindScope(QueryScoped.class, queryScope);
            bind(Key.get(new TypeLiteral<Option<Class<? extends Query>>>() {}))
                .toProvider(QueryEngineModule.OptionalQueryClassProvider.class);
            bind(Cipher.class).annotatedWith(named("RequestAuthorizationToken"))
                .toInstance(aes(generateKey(256), new SecureRandom()));
            bind(Key.get(AuthorizationTokenDecryptionResult.class))
                .toProvider(AuthorizationTokenDecryptionResultProvider.class);
          }
        });

    RetryingTransacter firstTransacter = injector.getInstance(Key.get(RetryingTransacter.class));
    RetryingTransacter secondTransacter = injector.getInstance(Key.get(RetryingTransacter.class, named("second")));
    RetryingTransacter thirdTransacter = injector.getInstance(Key.get(RetryingTransacter.class, named("third")));

    // all different
    EquivalenceTester.check(
        newArrayList(firstTransacter),
        newArrayList(secondTransacter),
        newArrayList(thirdTransacter));

    // singletoness
    assertEquals("should be singleton", firstTransacter, injector.getInstance(RetryingTransacter.class));

    try {
      injector.getInstance(Key.get(RetryingTransacter.class, named("fourth")));
      fail();
    } catch (ConfigurationException e) {
      // expected
    }

    try {
      injector.getInstance(Key.get(RetryingTransacter.class, named("fifth")));
      fail();
    } catch (ConfigurationException e) {
      // expected
    }
  }

  @Test
  public void randomize() {
    AtomicInteger count = new AtomicInteger(0);
    assertEquals("jdbc:mysql://che03/bi", KcHibernateModule.randomizeJdbcUrl("jdbc:mysql://che03/bi", count));
    assertEquals(0, count.get());

    assertEquals("**************************", KcHibernateModule.randomizeJdbcUrl("jdbc:hsqldb:mem:stg", count));
    assertEquals(1, count.get());
    assertEquals("**************************", KcHibernateModule.randomizeJdbcUrl("jdbc:hsqldb:mem:stg", count));
    assertEquals(2, count.get());
  }

}
