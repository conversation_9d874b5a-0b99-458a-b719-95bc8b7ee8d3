package com.kaching.platform.hibernate;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

public class DatabaseKindsTest {

  @Test
  public void getDriverMariadb() {
    assertEquals(org.mariadb.jdbc.Driver.class,
        DatabaseKinds.fromJdbcUrl("********************************").getOrThrow().getDriver());
    assertEquals(org.mariadb.jdbc.Driver.class,
        DatabaseKinds.fromJdbcUrl("*************************************").getOrThrow().getDriver());
    assertEquals(org.mariadb.jdbc.Driver.class,
        DatabaseKinds.fromJdbcUrl("********************************&whatever").getOrThrow().getDriver());

    assertEquals(org.mariadb.jdbc.Driver.class,
        DatabaseKinds.fromJdbcUrl("********************************,host2/kaching").getOrThrow().getDriver());
    assertEquals(org.mariadb.jdbc.Driver.class, DatabaseKinds
        .fromJdbcUrl("********************************,host2:5678,host3/kaching&hello=world").getOrThrow().getDriver());
  }

  @Test
  public void getDriverMysql() {
    assertEquals(com.mysql.cj.jdbc.Driver.class,
        DatabaseKinds.fromJdbcUrl("******************************").getOrThrow().getDriver());
    assertEquals(com.mysql.cj.jdbc.Driver.class,
        DatabaseKinds.fromJdbcUrl("***********************************").getOrThrow().getDriver());
    assertEquals(com.mysql.cj.jdbc.Driver.class,
        DatabaseKinds.fromJdbcUrl("******************************&whatever").getOrThrow().getDriver());

    assertEquals(com.mysql.cj.jdbc.Driver.class,
        DatabaseKinds.fromJdbcUrl("******************************,host2/kaching").getOrThrow().getDriver());
    assertEquals(com.mysql.cj.jdbc.Driver.class, DatabaseKinds
        .fromJdbcUrl("******************************,host2:5678,host3/kaching&hello=world").getOrThrow().getDriver());
  }

}