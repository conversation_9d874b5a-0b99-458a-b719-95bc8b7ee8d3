package com.kaching.platform.hibernate;

import static com.kaching.platform.hibernate.IdTransactionBatcher.toCollection;
import static com.kaching.platform.hibernate.IdTransactionBatcherTest.TestBatcherEntity.getBatchingQuery;
import static org.hibernate.criterion.Restrictions.eq;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.dbunit.DatabaseUnitException;
import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.AbstractIterator;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.kaching.platform.hibernate.IdTransactionBatcher.AbstractBatchingQuery;
import com.kaching.util.tests.PersistentTestBase;

public class IdTransactionBatcherTest extends PersistentTestBase {

  @BeforeClass
  public static void beforeClass() throws DatabaseUnitException, SQLException {
    configure(TestBatcherEntity.class);
  }

  @Test
  public void testBatch() throws Exception {
    final List<Id<TestBatcherEntity>> ids = transacter
        .execute((WithSessionExpression<List<Id<TestBatcherEntity>>>) session -> {
      List<Id<TestBatcherEntity>> list = new ArrayList<>();
      list.add(session.save(new TestBatcherEntity(true)));
      session.save(new TestBatcherEntity(false));
      list.add(session.save(new TestBatcherEntity(true)));
      session.save(new TestBatcherEntity(false));
      list.add(session.save(new TestBatcherEntity(true)));
      return list;
    });

    final List<Id<TestBatcherEntity>> resultIds = new ArrayList<>();

    IdTransactionBatcher batcher = new IdTransactionBatcher(transacter);
    batcher.batch(2,
        getBatchingQuery().withCriteria(criteria -> criteria.add(eq("prop", true))),
        (session, items) -> {
          assertTrue(items.size() <= 2);
          resultIds.addAll(Lists.transform(items, Id.toId()));
        });

    assertEquals(resultIds, ids);
    assertTrue(resultIds.size() == 3);
  }

  @Test
  public void testToCollection() throws Exception {
    Iterable<String> collection1 = ImmutableList.of();
    Iterable<String> collection2 = () -> (new AbstractIterator<String>() {
      @Override
      protected String computeNext() {
        return endOfData();
      }
    });
    assertTrue(toCollection(collection1) == collection1);
    assertTrue(toCollection(collection2) != collection2);
  }

  public static class TestBatcherEntity extends AbstractHibernateEntity {

    private Id<TestBatcherEntity> id;
    private boolean prop;

    public TestBatcherEntity() {
      this(false);
    }

    public TestBatcherEntity(boolean prop) {
      this.prop = prop;
    }

    @Override
    public Id<TestBatcherEntity> getId() {
      return id;
    }

    public static AbstractBatchingQuery<TestBatcherEntity> getBatchingQuery() {
      return new AbstractBatchingQuery<TestBatcherEntity>() {};
    }

  }

}