package com.kaching.platform.hibernate;

import java.sql.Connection;

import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.TypeHelper;

import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.interceptor.EntityLoadEnforcingInterceptor;

public class StubDbSession implements DbSession {

  @Override
  public Kind getKind() {
    throw new UnsupportedOperationException();
  }

  @Override
  public EntityLoadEnforcingInterceptor.Stats getEntityLoadStats() {
    throw new UnsupportedOperationException();
  }

  @Override
  public void flush() {
    throw new UnsupportedOperationException();
  }

  @Override
  public Connection connection() {
    throw new UnsupportedOperationException();
  }

  @Override
  public <E extends HibernateEntity> Id<E> save(E object) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void save(HibernateEntity object1, HibernateEntity object2, HibernateEntity... others) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void saveOrUpdate(HibernateEntity object) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void update(HibernateEntity object) {
    throw new UnsupportedOperationException();
  }

  @Override
  public <E extends HibernateEntity> void persist(E object) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void delete(HibernateEntity object) {
    throw new UnsupportedOperationException();
  }

  @Override
  public <E extends HibernateEntity> void refresh(E object) {
    throw new UnsupportedOperationException();
  }

  @Override
  public <E extends HibernateEntity> PersistentCriteria<E> createCriteria(Class<E> persistentClass) {
    throw new UnsupportedOperationException();
  }

  @Override
  public <E extends HibernateEntity> PersistentCriteria<E> createCriteria(Class<E> persistentClass, String alias) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Query createQuery(String queryString) {
    throw new UnsupportedOperationException();
  }

  @Override
  public <E extends HibernateEntity> PersistentQuery<E> createQuery(Class<E> clazz, String queryString) {
    throw new UnsupportedOperationException();
  }

  @Override
  public SQLQuery createSQLQuery(String queryString) {
    throw new UnsupportedOperationException();
  }

  @Override
  public <E extends HibernateEntity> PersistentSQLQuery<E> createSQLQuery(Class<E> clazz, String queryString) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void clear() {
    throw new UnsupportedOperationException();
  }

  @Override
  public <E extends HibernateEntity> Option<E> get(Class<E> clazz, Id<? super E> id) {
    throw new UnsupportedOperationException();
  }

  @Override
  public <E extends HibernateEntity> E getOrThrow(Class<E> clazz, Id<? super E> id) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Session getSession() {
    throw new UnsupportedOperationException();
  }

  @Override
  public TypeHelper getTypeHelper() {
    throw new UnsupportedOperationException();
  }

  @Override
  public void onCommit(Runnable runnable) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void onRollback(Runnable runnable) {
    throw new UnsupportedOperationException();
  }

  @Override
  public <E extends HibernateEntity> void evict(E object) {
    throw new UnsupportedOperationException();
  }

}
