package com.kaching.platform.hibernate;

import static java.util.concurrent.TimeUnit.MILLISECONDS;
import static org.junit.Assert.assertEquals;

import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;

import com.google.inject.util.Providers;

public class TransacterCountersTest {

  private TransacterCounters counters;

  @Before
  public void before() {
    counters = new TransacterCounters(Providers.of(new DateTime()));
  }

  @Test
  public void incrementReadWriteSessions() {
    assertEquals(0, counters.getReadWriteSessions());
    counters.incrementReadWriteSessions();
    assertEquals(1, counters.getReadWriteSessions());
  }

  @Test
  public void incrementReadOnlySessions() {
    assertEquals(0, counters.getReadOnlySessions());
    counters.incrementReadOnlySessions();
    assertEquals(1, counters.getReadOnlySessions());
  }

  @Test
  public void incrementStatelessSessions() {
    assertEquals(0, counters.getStatelessSessions());
    counters.incrementStatelessSessions();
    assertEquals(1, counters.getStatelessSessions());
  }

  @Test
  public void recordSessionOpenedNanos() {
    assertEquals(0, counters.getSessionOpenedTimeRollingMaximum());
    counters.recordSessionOpenedNanos(MILLISECONDS.toNanos(10));
    assertEquals(10, counters.getSessionOpenedTimeRollingMaximum());
    counters.recordSessionOpenedNanos(MILLISECONDS.toNanos(15));
    assertEquals(15, counters.getSessionOpenedTimeRollingMaximum());
    counters.recordSessionOpenedNanos(MILLISECONDS.toNanos(5));
    assertEquals(15, counters.getSessionOpenedTimeRollingMaximum());
  }

  @Test
  public void recordBorrowedConnectionProxyCreationTimeInNanos() {
    assertEquals(0, counters.getBorrowedConnectionProxyTimeRollingMaximum());
    counters.recordBorrowedConnectionProxyCreationTimeInNanos(MILLISECONDS.toNanos(10));
    assertEquals(10, counters.getBorrowedConnectionProxyTimeRollingMaximum());
    counters.recordBorrowedConnectionProxyCreationTimeInNanos(MILLISECONDS.toNanos(15));
    assertEquals(15, counters.getBorrowedConnectionProxyTimeRollingMaximum());
    counters.recordBorrowedConnectionProxyCreationTimeInNanos(MILLISECONDS.toNanos(5));
    assertEquals(15, counters.getBorrowedConnectionProxyTimeRollingMaximum());
  }

  @Test
  public void recordSetReadOnlyTimeInNanos() {
    assertEquals(0, counters.getSetReadOnlyTimeRollingMaximum());
    counters.recordSetReadOnlyTimeInNanos(MILLISECONDS.toNanos(10));
    assertEquals(10, counters.getSetReadOnlyTimeRollingMaximum());
    counters.recordSetReadOnlyTimeInNanos(MILLISECONDS.toNanos(15));
    assertEquals(15, counters.getSetReadOnlyTimeRollingMaximum());
    counters.recordSetReadOnlyTimeInNanos(MILLISECONDS.toNanos(5));
    assertEquals(15, counters.getSetReadOnlyTimeRollingMaximum());
  }

}