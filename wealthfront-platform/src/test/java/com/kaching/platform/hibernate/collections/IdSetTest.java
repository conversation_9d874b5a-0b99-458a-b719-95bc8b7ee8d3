package com.kaching.platform.hibernate.collections;

import static com.kaching.platform.common.Option.some;
import static com.kaching.platform.hibernate.collections.IdSet.entitiesToIdSet;
import static com.kaching.platform.hibernate.collections.IdSet.entityIdSet;
import static com.kaching.platform.hibernate.collections.IdSet.idSet;
import static com.kaching.platform.hibernate.collections.IdSet.longsToIdSet;
import static com.kaching.platform.hibernate.collections.IdSet.toIdSet;
import static com.wealthfront.test.Assert.assertEmpty;
import static com.wealthfront.test.Assert.assertThrows;
import static java.util.stream.Collectors.toSet;
import static org.hibernate.criterion.Projections.id;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.Collections;
import java.util.Iterator;
import java.util.Set;
import java.util.stream.LongStream;
import java.util.stream.Stream;

import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.google.common.collect.Streams;
import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.TestEntity2;
import com.kaching.platform.testing.FakeBatcher;
import com.kaching.util.id.TestEntity;
import com.kaching.util.tests.PersistentTestBase;

public class IdSetTest extends PersistentTestBase {

  @BeforeClass
  public static void beforeClass() {
    configure(TestEntity2.class);
  }

  @Test
  public void test_equalsImmutableSet() {
    Set<Id<TestEntity>> setOfIds = ImmutableSet.of(Id.of(1), Id.of(2));
    Set<Id<TestEntity>> idSet = idSet(setOfIds);
    Set<Id<HibernateEntity>> idSetFromEntities = entityIdSet(ImmutableSet.of(
        () -> Id.of(1),
        () -> Id.of(2)
    ));

    assertFalse(idSet.isEmpty());
    assertEquals(setOfIds, idSet);
    assertEquals(setOfIds, idSetFromEntities);
  }

  @Test
  public void largeSet() {
    int itemsPerSet = 5_000_000;

    Set<Id<TestEntity>> idSet = idSet(LongStream.range(0, itemsPerSet).mapToObj(Id::of));

    assertEquals(itemsPerSet, idSet.size());
    LongStream.range(0, itemsPerSet)
        .forEach(longIdentifier -> assertTrue(idSet.contains(Id.of(longIdentifier))));
    assertFalse(idSet.contains(Id.of(itemsPerSet)));
    assertFalse(idSet.contains(Id.of(itemsPerSet + 1)));
    assertThrows(ClassCastException.class, null, () -> assertFalse(idSet.contains(some(Id.of(0)))));
  }

  @Test
  public void batching() {
    Set<Id<TestEntity>> allIds = idSet(LongStream.range(0, 3).mapToObj(Id::of));

    Iterator<ImmutableList<Id<TestEntity>>> batches = new FakeBatcher().splitList(allIds).iterator();

    assertEquals(ImmutableList.of(Id.of(0), Id.of(1)), batches.next());
    assertEquals(ImmutableList.of(Id.of(2)), batches.next());
    assertFalse(batches.hasNext());
  }

  @Test
  public void setsDifference() {
    Set<Id<TestEntity>> set1 = idSet(ImmutableSet.of(Id.of(1), Id.of(2)));
    Set<Id<TestEntity>> set2 = idSet(ImmutableSet.of(Id.of(2), Id.of(3)));
    Set<Id<TestEntity>> set3 = ImmutableSet.of(Id.of(2), Id.of(3));

    assertEquals(ImmutableSet.of(Id.of(1)),
        Sets.difference(set1, set2));
    assertEquals(ImmutableSet.of(Id.of(1)),
        Sets.difference(set1, set3));
    assertEmpty(Sets.difference(set2, set3));
  }

  @Test
  public void returnedFromRepoMethod() {
    Id<TestEntity2> id1 = transacter.save(new TestEntity2("a"));
    Id<TestEntity2> id2 = transacter.save(new TestEntity2("b"));

    Set<Id<TestEntity2>> idSet = transacter.executeWithReadOnlySessionExpression(
        session -> session.createCriteria(TestEntity2.class)
            .<Id<TestEntity2>>setProjection(id())
            .idSet());

    assertEquals(ImmutableSet.of(id1, id2), idSet);
  }

  @Test
  public void abstractSetOperations() {
    Set<Id<TestEntity2>> set = ImmutableSet.of(Id.of(1));
    Set<Id<TestEntity2>> idSet = idSet(set);

    assertNotNull(idSet.toArray());
    assertNotNull(idSet.toArray(new Object[0]));
    assertTrue(idSet.containsAll(set));
    assertTrue(idSet.containsAll(Collections.<Id<TestEntity2>>emptyList()));
    assertFalse(idSet.containsAll(ImmutableList.<Id<TestEntity2>>of(Id.of(0))));
  }

  @Test
  public void mutatingOperationsSupported() {
    Set<Id<HibernateEntity>> set = ImmutableSet.of(Id.of(1));
    Set<Id<HibernateEntity>> idSet = idSet(set);

    assertTrue(idSet.remove(Id.of(1)));
    assertFalse(idSet.remove(Id.of(2)));
    assertThrows(ClassCastException.class,
        "class com.kaching.platform.common.Option$Some cannot be cast to class com.kaching.platform.hibernate.Id (com.kaching.platform.common.Option$Some and com.kaching.platform.hibernate.Id are in unnamed module of loader 'app')",
        () -> idSet.remove(some(Id.of(1))));

    assertTrue(idSet.addAll(ImmutableSet.of(Id.of(1))));
    assertTrue(idSet.addAll(ImmutableSet.of(Id.of(2))));
  }

  @Test
  public void batchMutatingOperationsUnsupported_throws() {
    Set<Id<HibernateEntity>> set = ImmutableSet.of(Id.of(1));
    Set<Id<HibernateEntity>> idSet = idSet(set);

    assertThrows(UnsupportedOperationException.class, "remove",
        () -> assertTrue(idSet.retainAll(Collections.<Id<HibernateEntity>>emptySet())));
    assertThrows(UnsupportedOperationException.class, "remove",
        () -> assertTrue(idSet.removeAll(ImmutableSet.of(Id.of(1)))));
    assertThrows(UnsupportedOperationException.class, "remove", idSet::clear);
  }

  @Test
  public void union_supported() {
    Set<Id<HibernateEntity>> set1 = idSet(LongStream.range(1, 3));
    Set<Id<HibernateEntity>> set2 = idSet(LongStream.range(2, 4));

    Set<Id<HibernateEntity>> expectedUnion = ImmutableSet.of(Id.of(1), Id.of(2), Id.of(3));

    assertEquals(expectedUnion, Sets.union(set1, set2));
  }

  @Test
  public void intersection_supported() {
    Set<Id<HibernateEntity>> set1 = idSet(LongStream.range(1, 3));
    Set<Id<HibernateEntity>> set2 = idSet(LongStream.range(2, 4));

    Set<Id<HibernateEntity>> expectedIntersection = ImmutableSet.of(Id.of(2));

    assertEquals(expectedIntersection, Sets.intersection(set1, set2));
  }

  @Test
  public void collectors() {
    Set<Id<HibernateEntity>> expected = LongStream.range(0, 3)
        .mapToObj(Id::of)
        .collect(toSet());

    Set<Id<HibernateEntity>> fromEntities = Stream.<HibernateEntity>of(
            () -> Id.of(0),
            () -> Id.of(1),
            () -> Id.of(2)
        )
        .collect(entitiesToIdSet());

    Set<Id<HibernateEntity>> fromIds = LongStream.range(0, 3)
        .mapToObj(Id::of)
        .collect(toIdSet());

    Set<Id<HibernateEntity>> fromLongs = LongStream.range(0, 3)
        .boxed()
        .collect(longsToIdSet());

    assertEquals(expected, fromEntities);
    assertEquals(expected, fromIds);
    assertEquals(expected, fromLongs);
  }

  @Test
  public void collectors_concatenated() {
    Set<Id<HibernateEntity>> expected = LongStream.range(0, 6)
        .mapToObj(Id::of)
        .collect(toSet());

    Set<Id<HibernateEntity>> fromEntities = Streams.<HibernateEntity>concat(
            Stream.of(
                () -> Id.of(0),
                () -> Id.of(1),
                () -> Id.of(2)
            ),
            Stream.of(
                () -> Id.of(2),
                () -> Id.of(3)
            ),
            Stream.of(
                () -> Id.of(3),
                () -> Id.of(4),
                () -> Id.of(5)
            )
        )
        .collect(entitiesToIdSet());

    Set<Id<HibernateEntity>> fromIds = LongStream.concat(
            LongStream.concat(
                LongStream.range(0, 3),
                LongStream.range(2, 4)
            ),
            LongStream.range(3, 6)
        )
        .mapToObj(Id::of)
        .collect(toIdSet());

    Set<Id<HibernateEntity>> fromLongs = Streams.concat(
            LongStream.range(0, 3).boxed(),
            LongStream.range(2, 4).boxed(),
            LongStream.range(3, 6).boxed()
        )
        .collect(longsToIdSet());

    assertEquals(expected, fromEntities);
    assertEquals(expected, fromIds);
    assertEquals(expected, fromLongs);
  }

}