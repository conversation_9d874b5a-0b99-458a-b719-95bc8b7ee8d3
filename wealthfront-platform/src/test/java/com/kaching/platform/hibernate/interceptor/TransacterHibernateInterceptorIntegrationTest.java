package com.kaching.platform.hibernate.interceptor;

import static com.kaching.platform.hibernate.StatefulSessionFactoryProvider.cleanSessionFactoriesFromInjector;
import static com.kaching.platform.hibernate.interceptor.InterceptorIntegrationTestUtils.getSpyInterceptorInjector;
import static com.kaching.platform.hibernate.interceptor.InterceptorIntegrationTestUtils.validateTransacter;

import org.junit.Test;

import com.google.inject.Injector;
import com.kaching.platform.common.Option;

public class TransacterHibernateInterceptorIntegrationTest {

  private static final String COMMENT = "/*k:com.kaching.platform.guice.KachingServices$UM*/";

  @Test
  public void withDisabledStatusAndWithService_executesCorrectlyWithoutAppending() {
    Injector injector = getSpyInterceptorInjector(Option.none(), true);
    HibernateInterceptorStatusSwitch statusSwitch = injector.getInstance(HibernateInterceptorStatusSwitch.class);
    statusSwitch.setStatus(HibernateInterceptorStatusSwitch.Status.INTERCEPTOR_DISABLED);
    validate<PERSON>ran<PERSON>cter(injector);
    cleanSessionFactoriesFromInjector(injector);
  }

  @Test
  public void withDisabledStatusAndWithoutService_executesCorrectlyWithoutAppending() {
    Injector injector = getSpyInterceptorInjector(Option.none(), false);
    HibernateInterceptorStatusSwitch statusSwitch = injector.getInstance(HibernateInterceptorStatusSwitch.class);
    statusSwitch.setStatus(HibernateInterceptorStatusSwitch.Status.INTERCEPTOR_DISABLED);
    validateTransacter(injector);
    cleanSessionFactoriesFromInjector(injector);
  }

  @Test
  public void withEnabledStatusAndWithService_executesCorrectlyAndAppends() {
    Injector injector = getSpyInterceptorInjector(Option.some(COMMENT), true);
    HibernateInterceptorStatusSwitch statusSwitch = injector.getInstance(HibernateInterceptorStatusSwitch.class);
    statusSwitch.setStatus(HibernateInterceptorStatusSwitch.Status.INTERCEPTOR_ENABLED);
    validateTransacter(injector);
    cleanSessionFactoriesFromInjector(injector);
  }

  @Test
  public void withEnabledStatusAndWithoutService_executesCorrectlyWithoutAppending() {
    Injector injector = getSpyInterceptorInjector(Option.none(), false);
    HibernateInterceptorStatusSwitch statusSwitch = injector.getInstance(HibernateInterceptorStatusSwitch.class);
    statusSwitch.setStatus(HibernateInterceptorStatusSwitch.Status.INTERCEPTOR_ENABLED);
    validateTransacter(injector);
    cleanSessionFactoriesFromInjector(injector);
  }

}
