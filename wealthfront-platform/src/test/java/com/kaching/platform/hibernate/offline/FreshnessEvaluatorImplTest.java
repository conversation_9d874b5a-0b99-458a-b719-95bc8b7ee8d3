package com.kaching.platform.hibernate.offline;

import static com.kaching.util.functional.Either.left;
import static com.kaching.util.functional.Either.right;
import static com.kaching.util.functional.Pointer.pointer;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.hibernate.criterion.Projections.max;
import static org.hibernate.criterion.Restrictions.eq;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.dbunit.DatabaseUnitException;
import org.joda.time.DateTime;
import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.offline.FreshnessEvaluator.Freshness;
import com.kaching.platform.hibernate.offline.HasFreshness.EvaluationCriterion;
import com.kaching.platform.hibernate.offline.HasFreshness.ImmutableEvaluationCriterion;
import com.kaching.platform.hibernate.offline.HasFreshness.MutableEvaluationCriterion;
import com.kaching.util.functional.Either;
import com.kaching.util.functional.Pointer;
import com.kaching.util.tests.PersistentTestBase;

public class FreshnessEvaluatorImplTest extends PersistentTestBase {
  
  final DateTime now = new DateTime(2017, 1, 1, 0, 0, 0, 0, ET);
  
  @BeforeClass
  public static void beforeClass()
      throws DatabaseUnitException, SQLException {
    configure(CustomEntity.class, MutableEntity.class, ImmutableEntity.class);
  }
  
  @Test
  public void noOfflineMax_returnsStale() {
    transacter.save(new MutableEntity("john", now));
    transacter.save(new ImmutableEntity("john"));
    HasFreshness<String> noOfflineMax = hasFreshness("john", Collections.emptyMap(),
        new MutableEvaluationCriterion("mutables", "user_name"),
        new ImmutableEvaluationCriterion("immutables", "user_name"));

    Freshness freshness = getEvaluator().getFreshness(noOfflineMax);
    assertEquals(ImmutableSet.of("mutables", "immutables"), freshness.getStaleTables());
  }
  
  @Test
  public void noOnlineMax_returnsStale() {
    Id<MutableEntity> mutableEntityId = transacter.save(new MutableEntity("not john", now));
    Id<ImmutableEntity> immutableEntityId = transacter.save(new ImmutableEntity("not john"));
    HasFreshness<String> noOnlineMax = hasFreshness("john",
        ImmutableMap.of("mutables", now.plusSeconds(1), "immutables", immutableEntityId.getId() + 1),
        new MutableEvaluationCriterion("mutables", "user_name"),
        new ImmutableEvaluationCriterion("immutables", "user_name"));

    Freshness freshness = getEvaluator().getFreshness(noOnlineMax);
    assertEquals(ImmutableSet.of("mutables", "immutables"), freshness.getStaleTables());
  }
  
  @Test
  public void maxesOfDifferentType_returnsStale() {
    Id<MutableEntity> mutableEntityId = transacter.save(new MutableEntity("john", now));
    Id<ImmutableEntity> immutableEntityId = transacter.save(new ImmutableEntity("john"));
    HasFreshness<String> hasMaxesOfWrongType = hasFreshness("john",
        ImmutableMap.of("immutables", now.plusSeconds(1), "mutables", immutableEntityId.getId() + 1),
        new MutableEvaluationCriterion("mutables", "user_name"),
        new ImmutableEvaluationCriterion("immutables", "user_name"));

    Freshness freshness = getEvaluator().getFreshness(hasMaxesOfWrongType);
    assertEquals(ImmutableSet.of("mutables", "immutables"), freshness.getStaleTables());
  }
  
  @Test
  public void mutables_offlineUpdatedAtLessThanMaxOnline_returnsStale() {
    transacter.save(new MutableEntity("not john", now.minusSeconds(2)));
    transacter.save(new MutableEntity("john", now.minusSeconds(1)));
    transacter.save(new MutableEntity("john", now));
    HasFreshness<String> hasStaleMutable = hasFreshness("john",
        ImmutableMap.of("mutables", now.minusSeconds(1)), new MutableEvaluationCriterion("mutables", "user_name"));
    
    Freshness freshness = getEvaluator().getFreshness(hasStaleMutable);
    assertEquals(ImmutableSet.of("mutables"), freshness.getStaleTables());
  }
  
  @Test
  public void immutables_offlineIdLessThanMaxOnline_returnsStale() {
    transacter.save(new ImmutableEntity("not john"));
    Id<ImmutableEntity> first = transacter.save(new ImmutableEntity("john"));
    Id<ImmutableEntity> second = transacter.save(new ImmutableEntity("john"));
    HasFreshness<String> hasStaleImmutable = hasFreshness("john",
        ImmutableMap.of("immutables", first.getId()), new ImmutableEvaluationCriterion("immutables", "user_name"));
    
    Freshness freshness = getEvaluator().getFreshness(hasStaleImmutable);
    assertEquals(ImmutableSet.of("immutables"), freshness.getStaleTables());
  }
  
  @Test
  public void customs_delegatesToEvaluationCriterionForOnlineMax() {
    Pointer<Id<CustomEntity>> maxId = pointer();
    transacter.executeWithSession(
        (DbSession session) -> {
          ImmutableEntity notJohn = new ImmutableEntity("not john");
          ImmutableEntity john = new ImmutableEntity("john");
          
          session.save(new CustomEntity(notJohn));
          session.save(new CustomEntity(john));
          maxId.set(session.save(new CustomEntity(john)));
        });
    HasFreshness<String> hasStaleCustoms = hasFreshness("john",
        ImmutableMap.of("customs", maxId.get().getId() - 1),
        new HasFreshness.CustomEvaluationCriterion<String>("customs") {
          @Override
          Option<Either<Long, DateTime>> getOnlineMax(DbSession session, String key) {
            return Option.some(Either.left((Long) ((Id)
                session.createCriteria(CustomEntity.class)
                    .createAlias("associatedEntity", "associatedEntity")
                    .add(eq("associatedEntity.userName", key))
                    .setProjection(max("id"))
                    .uniqueResult()).getId()));
          }
        });
    
    Freshness freshness = getEvaluator().getFreshness(hasStaleCustoms);
    assertEquals(ImmutableSet.of("customs"), freshness.getStaleTables());
  }
  
  @Test
  public void notStale() {
    transacter.save(new MutableEntity("john", now.minusSeconds(1)));
    transacter.save(new MutableEntity("john", now));
    Id<ImmutableEntity> first = transacter.save(new ImmutableEntity("john"));
    Id<ImmutableEntity> second = transacter.save(new ImmutableEntity("john"));
    HasFreshness<String> notStale = hasFreshness("john",
        ImmutableMap.of("mutables", now, "immutables", second.getId()),
        new MutableEvaluationCriterion("mutables", "user_name"),
        new ImmutableEvaluationCriterion("immutables", "user_name"));
    
    Freshness freshness = getEvaluator().getFreshness(notStale);
    assertTrue(freshness.isFresh());
  }
  
  @SafeVarargs
  private final HasFreshness<String> hasFreshness(
      String key, Map<String, Object> offlineMaxes, EvaluationCriterion<String>... evaluationCriteria) {
    return new HasFreshness<String>() {
      @Override
      public String getFreshnessKey() {
        return key;
      }

      @Override
      public Option<Either<Long, DateTime>> getOfflineMaxInput(String tableName) {
        if (offlineMaxes.containsKey(tableName)) {
          Object offlineMax = offlineMaxes.get(tableName);
          if (offlineMax instanceof Long) {
            return Option.some(left((Long) offlineMax));
          }
          if (offlineMax instanceof DateTime) {
            return Option.some(right((DateTime) offlineMax));
          }
          throw new IllegalStateException(
              Strings.format("Unexpected offlineMax class: %s", offlineMax.getClass()));
        }
        return Option.none();
      }

      @Override
      public List<EvaluationCriterion<String>> getFreshnessEvaluationCriteria() {
        return ImmutableList.copyOf(evaluationCriteria);
      }
    };
  }
  
  private FreshnessEvaluator getEvaluator() {
    FreshnessEvaluatorImpl freshnessEvaluator = new FreshnessEvaluatorImpl();
    freshnessEvaluator.transacter = transacter;
    return freshnessEvaluator;
  }
  
  public static class CustomEntity implements HibernateEntity {
    
    Id<CustomEntity> id;
    ImmutableEntity associatedEntity;

    CustomEntity() {}
    
    CustomEntity(ImmutableEntity associatedEntity) {
      this.associatedEntity = associatedEntity;
    }
    
    @Override
    public Id<CustomEntity> getId() {
      return id;
    }
    
  }
  
  public static class MutableEntity implements HibernateEntity {

    Id<MutableEntity> id;
    String userName;
    DateTime updatedAt;

    MutableEntity() {}
    
    MutableEntity(String userName, DateTime updatedAt) {
      this.userName = userName;
      this.updatedAt = updatedAt;
    }

    @Override
    public Id<MutableEntity> getId() {
      return id;
    }
    
  }
  
  public static class ImmutableEntity implements HibernateEntity {

    Id<ImmutableEntity> id;
    String userName;
    
    ImmutableEntity() {}

    ImmutableEntity(String userName) {
      this.userName = userName;
    }

    @Override
    public Id<ImmutableEntity> getId() {
      return id;
    }
    
  }
  
}
