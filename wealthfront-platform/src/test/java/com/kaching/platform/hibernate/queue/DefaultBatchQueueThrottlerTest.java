package com.kaching.platform.hibernate.queue;

import static com.twolattes.json.Json.object;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertEquals;

import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.kaching.platform.common.Option;
import com.twolattes.json.Json;

public class DefaultBatchQueueThrottlerTest {
  
  private DateTime now = new DateTime(2012, 2, 2, 0, 0, 0, ET);
  private final Json.Object category1 = object("category", "1");
  private final Json.Object category2 = object("category", "2");
  
  @Test
  public void observeFailedBatchAndGetRetryDelay_increasesDelayWithRetryCount() {
    DefaultBatchQueueThrottler throttler = getThrottler();
    assertEquals(Duration.standardMinutes(1), throttler.observeFailedBatchAndGetRetryDelay(category1, 1, Option.some(new RuntimeException("continue"))));
    
    assertEquals(Duration.standardMinutes(10), throttler.observeFailedBatchAndGetRetryDelay(category1, 2, Option.some(new RuntimeException("continue"))));
    
    throttler.observeSuccessfulBatch(category1);
    assertEquals(Duration.standardMinutes(10), throttler.observeFailedBatchAndGetRetryDelay(category1, 2, Option.some(new RuntimeException("continue"))));
    assertEquals(Duration.standardMinutes(20), throttler.observeFailedBatchAndGetRetryDelay(category1, 3, Option.some(new RuntimeException("continue"))));
    assertEquals(Duration.standardMinutes(20), throttler.observeFailedBatchAndGetRetryDelay(category1, 4, Option.some(new RuntimeException("continue"))));
    
    assertEquals(0, throttler.categoryToUnpauseTime.size());
    assertEquals(Duration.ZERO, throttler.getPreProcessDelay(category1));
    assertEquals(Duration.ZERO, throttler.getPreProcessDelay(category2));
  }

  @Test
  public void getDebugState() {
    DefaultBatchQueueThrottler throttler = getThrottler();
    assertEquals(Duration.standardMinutes(1), throttler.observeFailedBatchAndGetRetryDelay(category1, 1, Option.some(new RuntimeException("PAUSE"))));
    assertEquals(Duration.standardMinutes(1), throttler.getPreProcessDelay(category1));
    
    assertEquals(Json.object(
        "categoryToQueueBackoffIndex", Json.object("{\"category\":\"1\"}", 0),
        "categoryToUnpauseTime", Json.object("{\"category\":\"1\"}", "2012-02-02 00:01:00.000")
    ), throttler.debugState());
  }
  
  @Test
  public void observePausingException_pausesFutureBatchesWithSameCategory_ignoredUpdatesUntilPauseIsOver() {
    DefaultBatchQueueThrottler throttler = getThrottler();
    assertEquals(Duration.standardMinutes(1), throttler.observeFailedBatchAndGetRetryDelay(category1, 1, Option.some(new RuntimeException("PAUSE"))));
    assertEquals(Duration.standardMinutes(1), throttler.getPreProcessDelay(category1));
    
    assertEquals(ImmutableMap.of(category1, 0), throttler.categoryToQueueBackoffIndex);
    
    now = now.plusSeconds(20);
    assertEquals(Duration.standardSeconds(40), throttler.getPreProcessDelay(category1));
    
    assertEquals(Duration.standardSeconds(40), throttler.getPreProcessDelay(category1));
    assertEquals(Duration.standardSeconds(60), throttler.observeFailedBatchAndGetRetryDelay(category1, 1, Option.some(new RuntimeException("PAUSE"))));
    assertEquals(ImmutableSet.of(category1), throttler.categoryToUnpauseTime.keySet());
    
    assertEquals(Duration.ZERO, throttler.getPreProcessDelay(category2));
    
    now = now.plusSeconds(50);
    assertEquals(Duration.ZERO, throttler.getPreProcessDelay(category1));
    assertEquals(Duration.standardMinutes(2), throttler.observeFailedBatchAndGetRetryDelay(category1, 1, Option.some(new RuntimeException("PAUSE"))));
    assertEquals(ImmutableMap.of(category1, 1), throttler.categoryToQueueBackoffIndex);
    assertEquals(Duration.standardMinutes(2), throttler.observeFailedBatchAndGetRetryDelay(category1, 1, Option.some(new RuntimeException("PAUSE"))));
    assertEquals(Duration.standardMinutes(2), throttler.observeFailedBatchAndGetRetryDelay(category1, 1, Option.some(new RuntimeException("PAUSE"))));
    assertEquals(Duration.standardMinutes(2), throttler.getPreProcessDelay(category1));
    
    now = now.plusMinutes(3);
    assertEquals(Duration.ZERO, throttler.getPreProcessDelay(category1));
    assertEquals(Duration.standardMinutes(3), throttler.observeFailedBatchAndGetRetryDelay(category1, 1, Option.some(new RuntimeException("PAUSE"))));
    
    now = now.plusMinutes(4);
    assertEquals(Duration.ZERO, throttler.getPreProcessDelay(category1));
    assertEquals(Duration.standardMinutes(3), throttler.observeFailedBatchAndGetRetryDelay(category1, 1, Option.some(new RuntimeException("PAUSE"))));
    
    now = now.plusMinutes(4);
    throttler.observeSuccessfulBatch(category1);
    assertEquals(Duration.ZERO, throttler.getPreProcessDelay(category1));
    assertEquals(0, throttler.categoryToUnpauseTime.size());
  }

  @Test
  public void observePausingException_thenSuccessfulBatchBeforePauseOver_ignores() {
    DefaultBatchQueueThrottler throttler = getThrottler();
    assertEquals(Duration.standardMinutes(1), throttler.observeFailedBatchAndGetRetryDelay(category1, 1, Option.some(new RuntimeException("PAUSE"))));
    assertEquals(Duration.standardMinutes(1), throttler.getPreProcessDelay(category1));

    assertEquals(ImmutableMap.of(category1, 0), throttler.categoryToQueueBackoffIndex);

    now = now.plusSeconds(20);
    throttler.observeSuccessfulBatch(category1);
    assertEquals(Duration.standardSeconds(40), throttler.getPreProcessDelay(category1));
  }
  
  private DefaultBatchQueueThrottler getThrottler() {
    DefaultBatchQueueThrottler throttler = new DefaultBatchQueueThrottler(
        ImmutableList.of(Duration.standardMinutes(1), Duration.standardMinutes(2), Duration.standardMinutes(3)),
        ImmutableList.of(Duration.standardMinutes(1), Duration.standardMinutes(10), Duration.standardMinutes(20)),
        e -> e.getMessage().contains("PAUSE")
    );
    throttler.clock = () -> now;
    return throttler;
  }

}