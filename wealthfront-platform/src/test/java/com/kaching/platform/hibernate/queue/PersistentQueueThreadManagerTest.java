package com.kaching.platform.hibernate.queue;

import static java.lang.Thread.State.BLOCKED;
import static java.lang.Thread.State.RUNNABLE;
import static java.lang.Thread.State.TERMINATED;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.google.common.collect.Lists;
import com.google.inject.Provider;
import com.google.inject.util.Providers;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.WExpectations;

public class PersistentQueueThreadManagerTest {

  private static final int WORKER_THREADS = 2;
  private Mockeries.WFMockery mockery;
  private Provider<PersistentQueue> queueProvider;

  @Before
  public void before() {
    mockery = Mockeries.mockery(true);
  }

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process_isLeaderAndNoExistingThreads_shouldStartWorkers() {
    Thread mockThread1 = mockery.mock(Thread.class, "thread1");
    Thread mockThread2 = mockery.mock(Thread.class, "thread2");
    PersistentQueueThreadManager manager = getManager(true);
    assertEquals(0, manager.startedQueueThreads.size());
    mockery.checking(new WExpectations() {{
      exactly(WORKER_THREADS).of(manager.threadManager)
          .startWorkerAndGetThread(with(any(String.class)), with(queueProvider.get()));
      will(onConsecutiveCalls(returnValue(mockThread1), returnValue(mockThread2)));
    }});
    manager.process(queueProvider, WORKER_THREADS, "queue");
    assertEquals(WORKER_THREADS, manager.startedQueueThreads.size());
  }

  @Test
  public void process_notLeaderAndHasExistingThreads_shouldKillWorkers() {
    List<Thread> existingThreads = Lists.newArrayList(
        mockery.mock(Thread.class, "thread1"), mockery.mock(Thread.class, "thread2"));
    PersistentQueueThreadManager manager = getManager(false);
    assertEquals(0, manager.startedQueueThreads.size());
    manager.startedQueueThreads.addAll(existingThreads);
    mockery.checking(new WExpectations() {{
      existingThreads.forEach(t -> oneOf(t).interrupt());
    }});
    manager.process(queueProvider, WORKER_THREADS, "queue");
    assertEquals(0, manager.startedQueueThreads.size());
  }

  @Test
  public void process_isLeaderAndExistingThreads_doNothing() {
    Thread mockThread1 = mockery.mock(Thread.class, "thread1");
    Thread mockThread2 = mockery.mock(Thread.class, "thread2");
    List<Thread> existingThreads = Lists.newArrayList(mockThread1, mockThread2);
    PersistentQueueThreadManager manager = getManager(true);
    manager.startedQueueThreads.addAll(existingThreads);
    mockery.checking(new WExpectations() {{
      oneOf(mockThread1).getState();
      will(returnValue(RUNNABLE));
      oneOf(mockThread2).getState();
      will(returnValue(BLOCKED));
      never(manager.threadManager)
          .startWorkerAndGetThread(with(any(String.class)), with(queueProvider.get()));
    }});
    manager.process(queueProvider, WORKER_THREADS, "queue");
    assertFalse(manager.startedQueueThreads.isEmpty());
  }

  @Test
  public void process_notLeaderAndNoExistingThreads_doNothing() {
    PersistentQueueThreadManager manager = getManager(false);
    mockery.checking(new WExpectations() {{
      never(manager.threadManager)
          .startWorkerAndGetThread(with(any(String.class)), with(queueProvider.get()));
    }});
    manager.process(queueProvider, WORKER_THREADS, "queue");
    assertTrue(manager.startedQueueThreads.isEmpty());
  }

  @Test
  public void process_isLeaderAndDeadThreads_killInactiveThreadAndStartNew() {
    Thread mockThread1 = mockery.mock(Thread.class, "thread1");
    Thread mockThread2 = mockery.mock(Thread.class, "thread2");
    Thread mockThread3 = mockery.mock(Thread.class, "thread3");
    List<Thread> existingThreads = Lists.newArrayList(mockThread1, mockThread2);
    PersistentQueueThreadManager manager = getManager(true);
    assertEquals(0, manager.startedQueueThreads.size());
    manager.startedQueueThreads.addAll(existingThreads);
    mockery.checking(new WExpectations() {{
      oneOf(mockThread1).getState();
      will(returnValue(TERMINATED));
      oneOf(mockThread1).interrupt();
      oneOf(mockThread2).getState();
      will(returnValue(RUNNABLE));
      exactly(WORKER_THREADS - 1).of(manager.threadManager)
          .startWorkerAndGetThread(with(any(String.class)), with(queueProvider.get()));
      will(returnValue(mockThread3));
    }});
    manager.process(queueProvider, WORKER_THREADS, "queue");
    assertEquals(WORKER_THREADS, manager.startedQueueThreads.size());
  }

  private PersistentQueueThreadManager getManager(boolean isLeader) {
    PersistentQueueThreadManager manager = new PersistentQueueThreadManager();
    queueProvider = Providers.of(mockery.mock(PersistentQueue.class));
    manager.isLeader = Providers.of(isLeader);
    manager.threadManager = mockery.mock(PersistentQueueThreadManager.ThreadManager.class);
    return manager;
  }

}