package com.kaching.platform.hibernate.queue;

import static com.wealthfront.test.Assert.assertThrows;
import static java.util.Collections.emptySet;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNotSame;
import static org.junit.Assert.assertSame;

import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

import javax.inject.Named;

import org.junit.Test;

import com.google.common.collect.ImmutableMap;
import com.google.inject.AbstractModule;
import com.google.inject.Guice;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.Scopes;
import com.google.inject.TypeLiteral;
import com.kaching.platform.guice.MutableSingleton;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.queue.AbstractHybridQueueExampleTest.TestHybridQueue;
import com.kaching.platform.queryengine.exceptions.QueryClientException;

public class QueueBinderTest {

  private final TestHybridQueue testQueue1 = new TestHybridQueue(1, null);
  private final TestHybridQueue testQueue2 = new TestHybridQueue(1, null);

  @Test
  public void getQueue_isPresent() {
    QueueBinder queueBinder = new QueueBinder();
    queueBinder.hybridQueueMap = ImmutableMap.of("queue-name-1", () -> testQueue1, "queue-name-2", () -> testQueue2);
    assertSame(testQueue1, queueBinder.getQueue("queue-name-1"));
  }

  @Test
  public void getQueue_isAbsent() {
    QueueBinder queueBinder = new QueueBinder();
    queueBinder.hybridQueueMap = ImmutableMap.of("queue-name-1", () -> testQueue1, "queue-name-2", () -> testQueue2);
    assertThrows(QueryClientException.class,
        "No such queue 'WRONG-NAME' on this service. Available queues: queue-name-1, queue-name-2",
        () -> queueBinder.getQueue("WRONG-NAME"));
  }

  @Test
  public void unscopedQueuesAreBoundAsSingletons() {
    Injector injector = Guice.createInjector(new AbstractModule() {
      @Override
      protected void configure() {
        QueueBinder.bindHybridQueueTo(binder(), NonScopedHybridQueue.class);
      }
    });
    Map<String, HybridQueue<?>> map1 =
        injector.getInstance(Key.get(new TypeLiteral<Map<String, HybridQueue<?>>>() {}));
    Map<String, HybridQueue<?>> map2 =
        injector.getInstance(Key.get(new TypeLiteral<Map<String, HybridQueue<?>>>() {}));
    assertNotSame(map1, map2);

    HybridQueue<?> map1Queue = map1.get("NonScopedHybridQueue");
    HybridQueue<?> map2Queue = map2.get("NonScopedHybridQueue");
    assertNotNull(map1Queue);
    assertSame(map1Queue, map2Queue);

    NonScopedHybridQueue instance1 = injector.getInstance(NonScopedHybridQueue.class);
    NonScopedHybridQueue instance2 = injector.getInstance(NonScopedHybridQueue.class);
    assertSame(instance1, instance2);
  }

  @Test
  public void scopedQueuesKeepTheirScope() {
    Injector injector = Guice.createInjector(new AbstractModule() {
      @Override
      protected void configure() {
        QueueBinder.bindHybridQueueTo(binder(), ScopedHybridQueue.class);
        bindScope(MutableSingleton.class, Scopes.NO_SCOPE);
      }
    });
    Map<String, HybridQueue<?>> map1 =
        injector.getInstance(Key.get(new TypeLiteral<Map<String, HybridQueue<?>>>() {}));
    Map<String, HybridQueue<?>> map2 =
        injector.getInstance(Key.get(new TypeLiteral<Map<String, HybridQueue<?>>>() {}));
    assertNotSame(map1, map2);

    HybridQueue<?> map1Queue = map1.get("ScopedHybridQueue");
    HybridQueue<?> map2Queue = map2.get("ScopedHybridQueue");
    assertNotNull(map1Queue);
    assertNotSame(map1Queue, map2Queue);

    ScopedHybridQueue instance1 = injector.getInstance(ScopedHybridQueue.class);
    ScopedHybridQueue instance2 = injector.getInstance(ScopedHybridQueue.class);
    assertNotSame(instance1, instance2);
  }

  @Named("this @Named annotation shouldn't prevent binding as @Singleton since it's not a ScopeAnnotation")
  public static class NonScopedHybridQueue implements HybridQueue<MockEntity> {

    @Override
    public Id<MockEntity> persistAndEnqueue(MockEntity entity, DbSession session) {
      return null;
    }

    @Override
    public boolean signalEnqueue(Id<MockEntity> entityId) {
      return false;
    }

    @Override
    public void notifyEnqueue() {
    }

    @Override
    public void shutdownGracefully(boolean blockUntilFinished) {
    }

    @Override
    public int getWorkerCount() {
      return 0;
    }

    @Override
    public void setWorkerCount(int count) {
    }

    @Override
    public boolean tryLockAndExecute(Id<MockEntity> id, DbSession session, Consumer<MockEntity> execute) {
      return false;
    }

    @Override
    public void suspend() {
    }

    @Override
    public void unsuspend() {
    }

    @Override
    public boolean isSuspended() {
      return false;
    }

    @Override
    public void run() {
    }

    @Override
    public void resetProcessedIgnoredIds() {
    }

    @Override
    public Set<Id<MockEntity>> getProcessedIgnoredIds() {
      return emptySet();
    }

    @Override
    public long getProcessedIgnoredCount() {
      return 0;
    }

  }

  @MutableSingleton
  public static class ScopedHybridQueue implements HybridQueue<MockEntity> {

    @Override
    public Id<MockEntity> persistAndEnqueue(MockEntity entity, DbSession session) {
      return null;
    }

    @Override
    public boolean signalEnqueue(Id<MockEntity> entityId) {
      return false;
    }

    @Override
    public void notifyEnqueue() {
    }

    @Override
    public void shutdownGracefully(boolean blockUntilFinished) {
    }

    @Override
    public int getWorkerCount() {
      return 0;
    }

    @Override
    public void setWorkerCount(int count) {
    }

    @Override
    public boolean tryLockAndExecute(Id<MockEntity> id, DbSession session, Consumer<MockEntity> execute) {
      return false;
    }

    @Override
    public void suspend() {
    }

    @Override
    public void unsuspend() {
    }

    @Override
    public boolean isSuspended() {
      return false;
    }

    @Override
    public void run() {
    }

    @Override
    public void resetProcessedIgnoredIds() {
    }

    @Override
    public Set<Id<MockEntity>> getProcessedIgnoredIds() {
      return emptySet();
    }

    @Override
    public long getProcessedIgnoredCount() {
      return 0;
    }

  }

}
