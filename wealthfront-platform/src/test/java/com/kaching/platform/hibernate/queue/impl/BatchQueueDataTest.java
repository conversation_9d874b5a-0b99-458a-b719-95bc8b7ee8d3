package com.kaching.platform.hibernate.queue.impl;

import static org.junit.Assert.assertEquals;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.joda.time.LocalDate;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.queue.impl.BatchQueueBindingsTestBase.SomeBatchConfig;
import com.kaching.platform.hibernate.queue.impl.BatchQueueData.DeflatedBatch;
import com.kaching.platform.hibernate.queue.impl.BatchQueueData.InflatedBatch;
import com.kaching.user.UserId;
import com.twolattes.json.Json;

public class BatchQueueDataTest {
  
  @Test
  public void test_deflatedBatchOrdering_retriesThenFirstItemId_thenThingsWeDontCareAboutButMustIncludeForTreeSetContract() {
    List<DeflatedBatch> expectedOrdering = ImmutableList.of(
        new DeflatedBatch(Id.of(3L), Id.of(13), Id.of(16), 2, Json.object("ef", "2012-01-02"), 2, Option.none()),
        new DeflatedBatch(Id.of(2L), Id.of(11), Id.of(15), 2, Json.object("ef", "2012-01-02"), 1, Option.none()),
        new DeflatedBatch(Id.of(2L), Id.of(11), Id.of(16), 2, Json.object("ef", "2012-01-02"), 1, Option.none()),
        new DeflatedBatch(Id.of(1L), Id.of(12), Id.of(14), 2, Json.object("ef", "2012-01-02"), 1, Option.none())
    );
    List<DeflatedBatch> randomized = new ArrayList<>(expectedOrdering);
    for (int i = 0; i < 20; i++) {
      Collections.shuffle(randomized);
      assertEquals(expectedOrdering, ImmutableList.sortedCopyOf(randomized));
    }
  }

  @Test
  public void test_deflatedBatchCompareTo_instanceEquality() {
    DeflatedBatch batch = new DeflatedBatch(Id.of(3L), Id.of(13), Id.of(16), 2, Json.object("ef", "2012-01-02"), 2, Option.none());
    assertEquals(0, batch.compareTo(batch));
  }
  
  @Test
  public void test_inflatedBatch_deflate() {
    InflatedBatch<SomeBatchConfig, UserId> inflatedBatch = new InflatedBatch<>(Id.of(1L), new SomeBatchConfig(new LocalDate(2012, 1, 2)),
        Json.object("effectiveDate", "2012-01-02"),
        ImmutableList.of(
        new BatchQueueData.BatchItem<>(Id.of(33), new UserId(333)),
        new BatchQueueData.BatchItem<>(Id.of(11), new UserId(111)),
        new BatchQueueData.BatchItem<>(Id.of(22), new UserId(222))
    ), 2);
    DeflatedBatch deflated = inflatedBatch.deflate();
    assertEquals(Id.of(1L), deflated.getBatchId());
    assertEquals(Id.of(11), deflated.getFirstItemId());
    assertEquals(Id.of(33), deflated.getLastItemId());
    assertEquals(3, deflated.getSize());
  }

}