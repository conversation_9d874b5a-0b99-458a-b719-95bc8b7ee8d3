package com.kaching.platform.hibernate.queue.impl;

import static com.wealthfront.test.Assert.assertOptionEmpty;
import static com.wealthfront.test.Assert.assertOptionEquals;
import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.exceptions.InvalidArgumentException;
import com.twolattes.json.Json;

public class BatchQueueMarkAsErroredTest extends BatchQueueTestBase {
  
  @Test
  public void process_markAsErrored_success() {
    Id<BatchQueueBatch> batch1 = BatchQueueEntityFactory.createBatch()
        .withQueueId(getUserIdQueueId())
        .withItem(Json.number(11))
        .withItem(Json.number(22))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueBatch> batch2 = BatchQueueEntityFactory.createBatch()
        .withQueueId(getUserIdQueueId())
        .withItem(Json.number(33))
        .withItem(Json.number(44))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueBatch> batch3_alreadyErrored = BatchQueueEntityFactory.createBatch()
        .withQueueId(getUserIdQueueId())
        .withItem(Json.number(55))
        .withErroredAt(now.get().minusDays(1), "errored")
        .buildAndPersist(transacter())
        .getId();
    BatchQueueLockAndExecuteResult result =
        prepareQuery(new BatchQueueMarkAsErrored("UserIdQueue", true, Option.some("my msg"), ImmutableList.of(batch1, batch2, batch3_alreadyErrored))).process();
    assertEquals(String.format("Changed: [%s, %s],\n Unchanged: [%s]", batch1, batch2, batch3_alreadyErrored), result.getMessage().getOrThrow());
    assertEquals(ImmutableSet.of(batch1, batch2, batch3_alreadyErrored), result.getProcessedIds());
    
    transacter().executeWithReadOnlySession(session -> {
      BatchQueueBatch read1 = session.getOrThrow(BatchQueueBatch.class, batch1);
      assertOptionEquals(now.get(), read1.getErroredAt());
      assertOptionEquals("my msg", read1.getMessage());
      
      BatchQueueBatch read2 = session.getOrThrow(BatchQueueBatch.class, batch2);
      assertOptionEquals(now.get(), read2.getErroredAt());
      
      BatchQueueBatch read3 = session.getOrThrow(BatchQueueBatch.class, batch3_alreadyErrored);
      assertOptionEquals(now.get().minusDays(1), read3.getErroredAt());
    });
  }
  
  @Test
  public void process_unmarkAsErrored_success() {
    Id<BatchQueueBatch> batch1 = BatchQueueEntityFactory.createBatch()
        .withQueueId(getUserIdQueueId())
        .withItem(Json.number(11))
        .withItem(Json.number(22))
        .withErroredAt(now.get(), "errored")
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueBatch> batch2 = BatchQueueEntityFactory.createBatch()
        .withQueueId(getUserIdQueueId())
        .withItem(Json.number(33))
        .withItem(Json.number(44))
        .withErroredAt(now.get(), "errored")
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueBatch> batch3_notErrored = BatchQueueEntityFactory.createBatch()
        .withQueueId(getUserIdQueueId())
        .withItem(Json.number(55))
        .buildAndPersist(transacter())
        .getId();
    BatchQueueLockAndExecuteResult result =
        prepareQuery(new BatchQueueMarkAsErrored("UserIdQueue", false, Option.none(), ImmutableList.of(batch1, batch2, batch3_notErrored))).process();
    assertEquals(String.format("Changed: [%s, %s],\n Unchanged: [%s]", batch1, batch2, batch3_notErrored), result.getMessage().getOrThrow());
    assertEquals(ImmutableSet.of(batch1, batch2, batch3_notErrored), result.getProcessedIds());

    transacter().executeWithReadOnlySession(session -> {
      BatchQueueBatch read1 = session.getOrThrow(BatchQueueBatch.class, batch1);
      assertOptionEmpty(read1.getErroredAt());

      BatchQueueBatch read2 = session.getOrThrow(BatchQueueBatch.class, batch2);
      assertOptionEmpty(read2.getErroredAt());

      BatchQueueBatch read3 = session.getOrThrow(BatchQueueBatch.class, batch3_notErrored);
      assertOptionEmpty(read3.getErroredAt());
    });
  }
  
  @Test
  public void process_messageGivenWhenNotErrored_throws() {
    Id<BatchQueueBatch> batch1 = BatchQueueEntityFactory.createBatch()
        .withQueueId(getUserIdQueueId())
        .withItem(Json.number(11))
        .withItem(Json.number(22))
        .withSentAt(now.get())
        .buildAndPersist(transacter())
        .getId();
    assertThrows(InvalidArgumentException.class, () -> prepareQuery(new BatchQueueMarkAsErrored("UserIdQueue", false, Option.some("message"),
        ImmutableList.of(batch1))).process());
  }
  
  private BatchQueueMarkAsErrored prepareQuery(BatchQueueMarkAsErrored query) {
    injector().injectMembers(query);
    return query;
  }

}