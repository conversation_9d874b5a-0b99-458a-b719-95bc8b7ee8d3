package com.kaching.platform.hibernate.queue.impl;

import static com.kaching.platform.hibernate.queue.impl.BatchQueueEntityFactory.createName;
import static com.wealthfront.test.Assert.assertOptionEmpty;
import static com.wealthfront.test.Assert.assertOptionEquals;
import static com.wealthfront.test.Assert.assertThrows;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import java.util.List;

import org.joda.time.DateTime;
import org.junit.Test;

import com.google.common.collect.ImmutableSet;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.exceptions.InvalidArgumentException;
import com.twolattes.json.Json;

public class BatchQueueSplitBatchTest extends BatchQueueTestBase {
  
  @Test
  public void process_badBatchSize_throws() {
    Id<BatchQueueBatch> originalId = BatchQueueEntityFactory.createBatch()
        .withIgnoredAt(new DateTime(2012, 2, 2, 0, 0, 0, ET), "ignored")
        .withQueueId(getUserIdQueueId())
        .withItem(Json.number(11))
        .withItem(Json.number(22))
        .withItem(Json.number(33))
        .withItem(Json.number(44))
        .withItem(Json.number(55))
        .buildAndPersist(transacter())
        .getId();
    assertThrows(
        InvalidArgumentException.class, "Batch size must be positive", () -> prepareQuery(new BatchQueueSplitBatch("UserIdQueue", originalId, 0))
        .process());
  }

  @Test
  public void process_wrongQueue_returnsNotFound() {
    Id<BatchQueueName> otherQueueId = createName().withQueueName("OtherQueue").buildAndPersist(transacter()).getId();
    Id<BatchQueueBatch> originalId = BatchQueueEntityFactory.createBatch()
        .withIgnoredAt(new DateTime(2012, 2, 2, 0, 0, 0, ET), "ignored")
        .withQueueId(otherQueueId)
        .withItem(Json.number(11))
        .withItem(Json.number(22))
        .withItem(Json.number(33))
        .withItem(Json.number(44))
        .withItem(Json.number(55))
        .buildAndPersist(transacter())
        .getId();
    BatchQueueLockAndExecuteResult result = prepareQuery(new BatchQueueSplitBatch("UserIdQueue", originalId, 1)).process();
    assertEquals(ImmutableSet.of(originalId), result.getNotFoundIds());
    assertOptionEmpty(result.getMessage());
  }
  
  @Test
  public void process_success() {
    Id<BatchQueueBatch> originalId = BatchQueueEntityFactory.createBatch()
        .withIgnoredAt(new DateTime(2012, 2, 2, 0, 0, 0, ET), "ignored")
        .withQueueId(getUserIdQueueId())
        .withItem(Json.number(11))
        .withItem(Json.number(22))
        .withItem(Json.number(33))
        .withItem(Json.number(44))
        .withItem(Json.number(55))
        .buildAndPersist(transacter())
        .getId();
    List<Id<BatchQueueItem>> itemIds = getAllItemIds();
    BatchQueueLockAndExecuteResult result = prepareQuery(new BatchQueueSplitBatch("UserIdQueue", originalId, 2))
        .process();
    transacter().executeWithReadOnlySession(session -> {
      List<BatchQueueBatch> all = session.createCriteria(BatchQueueBatch.class).list();
      assertEquals(3, all.size());
      {
        BatchQueueBatch batch = all.get(0);
        assertEquals(originalId, batch.getId());
        assertEquals(itemIds.get(0), batch.getItemIdFrom());
        assertEquals(itemIds.get(1), batch.getItemIdTo());
      }
      {
        BatchQueueBatch batch = all.get(1);
        assertEquals(itemIds.get(2), batch.getItemIdFrom());
        assertEquals(itemIds.get(3), batch.getItemIdTo());
        assertNotNull(batch.getIgnoredAt().getOrNull());
      }
      {
        BatchQueueBatch batch = all.get(2);
        assertEquals(itemIds.get(4), batch.getItemIdFrom());
        assertEquals(itemIds.get(4), batch.getItemIdTo());
        assertNotNull(batch.getIgnoredAt().getOrNull());
      }
      assertOptionEquals(String.format("[%s, %s, %s]", all.get(0).getId(), all.get(1).getId(), all.get(2).getId()), result.getMessage());
    });
  }
  
  private BatchQueueSplitBatch prepareQuery(BatchQueueSplitBatch query) {
    injector().injectMembers(query);
    return query;
  }

}