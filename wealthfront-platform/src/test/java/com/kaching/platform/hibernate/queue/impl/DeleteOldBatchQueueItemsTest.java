package com.kaching.platform.hibernate.queue.impl;

import static com.kaching.platform.hibernate.queue.impl.BatchQueueEntityFactory.createBatch;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static java.util.stream.Collectors.toList;
import static org.junit.Assert.assertEquals;

import java.util.List;
import java.util.Map;

import org.jmock.Sequence;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.platform.hibernate.queue.BatchQueueBinder;
import com.kaching.platform.hibernate.queue.BatchQueueDeletionPolicy;
import com.kaching.platform.hibernate.queue.impl.DeleteOldBatchQueueItems.ToDelete;
import com.kaching.platform.testing.WExpectations;
import com.kaching.util.time.NanoTimeProvider;
import com.twolattes.json.Json;

public class DeleteOldBatchQueueItemsTest extends BatchQueueTestBase {
  
  private static final long NANOS_IN_SECOND = 1_000_000_000L;
  
  private final DateTime now = new DateTime(2012, 2, 2, 0, 0, 0, ET);
  private final NanoTimeProvider nanoTimeProvider = mockery.mock(NanoTimeProvider.class);

  @Test
  public void process_success() {
    BatchQueueBatch errored = createBatch()
        .withQueueId(getUserIdQueueId())
        .withCreatedAt(now.minusDays(8))
        .withErroredAt(now, "err")
        .withItem(Json.number(11))
        .withItem(Json.number(22))
        .buildAndPersist(transacter());
    BatchQueueBatch sentOldEnough1 = createBatch()
        .withQueueId(getUserIdQueueId())
        .withCreatedAt(now.minusDays(8))
        .withSentAt(now.minusDays(10))
        .withItem(Json.number(33))
        .withItem(Json.number(44))
        .withItem(Json.number(55))
        .buildAndPersist(transacter());
    BatchQueueBatch sentOldEnough2 = createBatch()
        .withQueueId(getUserIdQueueId())
        .withCreatedAt(now.minusDays(8))
        .withSentAt(now.minusDays(11))
        .withItem(Json.number(66))
        .buildAndPersist(transacter());
    BatchQueueBatch notSent = createBatch()
        .withQueueId(getUserIdQueueId())
        .withSentAt(null)
        .withCreatedAt(now.minusDays(8))
        .withItem(Json.number(77))
        .buildAndPersist(transacter());
    BatchQueueBatch ignoredOldEnough = createBatch()
        .withQueueId(getUserIdQueueId())
        .withSentAt(null)
        .withCreatedAt(now.minusDays(8))
        .withIgnoredAt(now.minusDays(4), "ignored")
        .withItem(Json.number(88))
        .buildAndPersist(transacter());
    BatchQueueBatch ignoredNotOldEnough = createBatch()
        .withQueueId(getUserIdQueueId())
        .withSentAt(null)
        .withCreatedAt(now.minusDays(8))
        .withIgnoredAt(now.minusDays(2), "ignored")
        .withItem(Json.number(99))
        .buildAndPersist(transacter());
    BatchQueueBatch sentNotOldEnough = createBatch()
        .withQueueId(getUserIdQueueId())
        .withSentAt(now.minusDays(1))
        .withCreatedAt(now.minusDays(8))
        .withItem(Json.number(100))
        .buildAndPersist(transacter());
    Sequence sequence = mockery.sequence("sequence");
    mockery.checking(new WExpectations() {{
      oneOf(nanoTimeProvider).get();
      will(returnValue(1 * NANOS_IN_SECOND));
      inSequence(sequence);

      oneOf(nanoTimeProvider).get();
      will(returnValue(2 * NANOS_IN_SECOND));
      inSequence(sequence);
      
      oneOf(sleeper).sleep(with(Duration.millis(2_000)));
      inSequence(sequence);
      
      oneOf(nanoTimeProvider).get();
      will(returnValue(3 * NANOS_IN_SECOND));
      inSequence(sequence);
      
      oneOf(nanoTimeProvider).get();
      will(returnValue(5 * NANOS_IN_SECOND));
      inSequence(sequence);
      
      oneOf(sleeper).sleep(with(Duration.millis(4_000)));
      inSequence(sequence);

      oneOf(nanoTimeProvider).get();
      will(returnValue(1 * NANOS_IN_SECOND));
      inSequence(sequence);

      oneOf(nanoTimeProvider).get();
      will(returnValue(2 * NANOS_IN_SECOND));
      inSequence(sequence);

      oneOf(sleeper).sleep(with(Duration.millis(2_000)));
      inSequence(sequence);
    }});
    prepareQuery(new DeleteOldBatchQueueItems() {
      @Override
      Map<Id<BatchQueueName>, BatchQueueDeletionPolicy> getQueueIdsToPurge() {
        return ImmutableMap.of(getUserIdQueueId(), BatchQueueDeletionPolicy.create()
            .withDeleteSentAfter(Duration.standardDays(7))
            .withDeleteIgnoredAfter(Duration.standardDays(3))
            .build());
      }

      @Override
      int getSelectSize() {
        return 2;
      }

      @Override
      int getMaxItemsInSingleTransaction() {
        return 2;
      }
    }).process();
    transacter().executeWithReadOnlySession(session -> {
      assertEquals(ImmutableList.of(errored, notSent, ignoredNotOldEnough, sentNotOldEnough), 
          session.createCriteria(BatchQueueBatch.class).list());
      List<Json.Value> allItems = session.createCriteria(BatchQueueItem.class).list()
          .stream()
          .map(BatchQueueItem::getPayload)
          .collect(toList());
      assertEquals(ImmutableList.of(
          Json.number(11),
          Json.number(22),
          Json.number(77),
          Json.number(99),
          Json.number(100)
      ), allItems);
    });
  }

  @Test
  public void process_nothingToDo_noOptimize() {
    BatchQueueBatch errored = createBatch()
        .withQueueId(getUserIdQueueId())
        .withCreatedAt(now.minusDays(8))
        .withErroredAt(now, "err")
        .withItem(Json.number(11))
        .withItem(Json.number(22))
        .buildAndPersist(transacter());
    prepareQuery(new DeleteOldBatchQueueItems() {
      @Override
      Map<Id<BatchQueueName>, BatchQueueDeletionPolicy> getQueueIdsToPurge() {
        return ImmutableMap.of(getUserIdQueueId(), BatchQueueDeletionPolicy.create()
            .withDeleteSentAfter(Duration.standardDays(7))
            .withDeleteIgnoredAfter(Duration.standardDays(3))
            .build());
      }

      @Override
      int getSelectSize() {
        return 2;
      }

      @Override
      int getMaxItemsInSingleTransaction() {
        return 2;
      }
    }).process();
    transacter().executeWithReadOnlySession(session -> {
      assertEquals(ImmutableList.of(errored), session.createCriteria(BatchQueueBatch.class).list());
    });
  }

  @Test
  public void process_batchMarkedAsNotSentBeforeDeleting_rollsBackAndSkips() {
    BatchQueueBatch sentOldEnough1 = createBatch()
        .withQueueId(getUserIdQueueId())
        .withCreatedAt(now.minusDays(8))
        .withSentAt(now.minusDays(10))
        .withItem(Json.number(33))
        .withItem(Json.number(44))
        .withItem(Json.number(55))
        .buildAndPersist(transacter());
    BatchQueueBatch sentOldEnough2 = createBatch()
        .withQueueId(getUserIdQueueId())
        .withCreatedAt(now.minusDays(8))
        .withSentAt(now.minusDays(11))
        .withItem(Json.number(66))
        .buildAndPersist(transacter());
    
    Sequence sequence = mockery.sequence("sequence");
    mockery.checking(new WExpectations() {{
      allowing(nanoTimeProvider).get();
      will(returnValue(0L));
      
      allowing(sleeper).sleep(with(any(Duration.class)));
    }});
    prepareQuery(new DeleteOldBatchQueueItems() {
      @Override
      Map<Id<BatchQueueName>, BatchQueueDeletionPolicy> getQueueIdsToPurge() {
        return ImmutableMap.of(getUserIdQueueId(), BatchQueueDeletionPolicy.create()
            .withDeleteSentAfter(Duration.standardDays(7))
            .build());
      }

      @Override
      void deleteBatch(Id<BatchQueueName> batchId, List<ToDelete> toDelete, DeleteOldBatchQueueItems.State state) {
        transacter().execute((WithSession) session -> {
          BatchQueueBatch changed = session.getOrThrow(BatchQueueBatch.class, sentOldEnough1.getId());
          if (changed.getSentAt().isDefined()) {
            changed.markAsNotSent(now);
          }
        });
        super.deleteBatch(batchId, toDelete, state);
      }
      
      @Override
      int getSelectSize() {
        return 2;
      }
      
      @Override
      int getMaxItemsInSingleTransaction() {
        return 2;
      }
    }).process();
    transacter().executeWithReadOnlySession(session -> {
      assertEquals(ImmutableList.of(sentOldEnough1), session.createCriteria(BatchQueueBatch.class).list());
      List<Json.Value> allItems = session.createCriteria(BatchQueueItem.class).list()
          .stream()
          .map(BatchQueueItem::getPayload)
          .collect(toList());
      assertEquals(ImmutableList.of(
          Json.number(33),
          Json.number(44),
          Json.number(55)
      ), allItems);
    });
  }
  
  @Test
  public void getQueueIdsToPurge() {
    DeleteOldBatchQueueItems query = prepareQuery(new DeleteOldBatchQueueItems() {
      @Override
      int getSelectSize() {
        return 2;
      }
    
      @Override
      int getMaxItemsInSingleTransaction() {
        return 2;
      }
    });
    query.binder = mockery.mock(BatchQueueBinder.class);
    BatchQueueRunner<?, ?> runner1 = mockery.mock(BatchQueueRunner.class, "BatchQueueRunner1");
    BatchQueueRunner<?, ?> runner2 = mockery.mock(BatchQueueRunner.class, "BatchQueueRunner2");
    UserIdQueue queue1 = new UserIdQueue() {
      @Override
      public BatchQueueDeletionPolicy getDeletionPolicy() {
        return BatchQueueDeletionPolicy.create()
            .withDeleteSentAfter(Duration.standardDays(7))
            .withDeleteIgnoredAfter(Duration.standardDays(3))
            .build();
      }
    };
    EntityQueue queue2 = new EntityQueue() {
      @Override
      public BatchQueueDeletionPolicy getDeletionPolicy() {
        return BatchQueueDeletionPolicy.create()
            .withDeleteSentAfter(Duration.standardDays(1))
            .build();
      }
    };
    
    mockery.checking(new WExpectations() {{
      oneOf(query.binder).getAllQueueNames();
      will(returnValue(ImmutableSet.of("SomeQueue1", "SomeQueue2")));
      
      oneOf(query.binder).getQueue("SomeQueue1");
      will(returnValue(queue1));

      oneOf(query.binder).getRunner("SomeQueue1");
      will(returnValue(runner1));

      oneOf(runner1).getQueueId();
      will(returnValue(Id.of(11)));

      oneOf(query.binder).getQueue("SomeQueue2");
      will(returnValue(queue2));

      oneOf(query.binder).getRunner("SomeQueue2");
      will(returnValue(runner2));

      oneOf(runner2).getQueueId();
      will(returnValue(Id.of(22)));
    }});
    assertEquals(ImmutableMap.of(
        Id.of(11), queue1.getDeletionPolicy(),
        Id.of(22), queue2.getDeletionPolicy()
    ), query.getQueueIdsToPurge());
  }
  
  @Test
  public void partition() {
    ToDelete one = new ToDelete(null, null, null, 1);
    ToDelete two = new ToDelete(null, null, null, 2);
    ToDelete two_2 = new ToDelete(null, null, null, 2);
    ToDelete three = new ToDelete(null, null, null, 3);
    ToDelete four = new ToDelete(null, null, null, 4);
    ToDelete five = new ToDelete(null, null, null, 5);
    
    assertEquals(ImmutableList.of(), DeleteOldBatchQueueItems.partition(ImmutableList.of(), 5));
    assertEquals(ImmutableList.of(
        ImmutableList.of(one),
        ImmutableList.of(three),
        ImmutableList.of(four)
    ), DeleteOldBatchQueueItems.partition(ImmutableList.of(one, three, four), 2));
    assertEquals(ImmutableList.of(
        ImmutableList.of(one, three),
        ImmutableList.of(four)
    ), DeleteOldBatchQueueItems.partition(ImmutableList.of(one, three, four), 4));
    assertEquals(ImmutableList.of(
        ImmutableList.of(one, two, two_2),
        ImmutableList.of(three, four),
        ImmutableList.of(five)
    ), DeleteOldBatchQueueItems.partition(ImmutableList.of(one, two, two_2, three, four, five), 7));
  }
  
  private DeleteOldBatchQueueItems prepareQuery(DeleteOldBatchQueueItems query) {
    query.sleeper = sleeper;
    query.binder = injector().getInstance(BatchQueueBinder.class);
    query.nanoTimeProvider = nanoTimeProvider;
    query.transacter = RetryingTransacter.retrying(transacter());
    query.now = now;
    return query;
  }

}