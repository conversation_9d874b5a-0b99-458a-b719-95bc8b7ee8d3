package com.kaching.platform.monitoring;

import static org.junit.Assert.assertEquals;

import java.util.concurrent.ThreadPoolExecutor;

import org.junit.After;
import org.junit.Test;

import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.WExpectations;

public class ThreadPoolStatsTest {

  private final Mockeries.WFMockery mockery = Mockeries.mockery(true);
  private final ThreadPoolExecutor threadPoolExecutor = mockery.mock(ThreadPoolExecutor.class);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void getActiveThreadCount() {
    mockery.checking(new WExpectations() {{
      oneOf(threadPoolExecutor).getActiveCount();
      will(returnValue(2));
    }});

    assertEquals(2, getStats().getActiveThreadCount());
  }

  @Test
  public void getIdleThreadCount() {
    mockery.checking(new WExpectations() {{
      oneOf(threadPoolExecutor).getPoolSize();
      will(returnValue(5));

      oneOf(threadPoolExecutor).getActiveCount();
      will(returnValue(2));
    }});

    assertEquals(3, getStats().getIdleThreadCount());

    mockery.checking(new WExpectations() {{
      oneOf(threadPoolExecutor).getPoolSize();
      will(returnValue(1));

      oneOf(threadPoolExecutor).getActiveCount();
      will(returnValue(2));
    }});

    assertEquals(0, getStats().getIdleThreadCount());
  }

  @Test
  public void getPoolSize() {
    mockery.checking(new WExpectations() {{
      oneOf(threadPoolExecutor).getPoolSize();
      will(returnValue(5));
    }});

    assertEquals(5, getStats().getPoolSize());
  }

  @Test
  public void getMaximumPoolSize() {
    mockery.checking(new WExpectations() {{
      oneOf(threadPoolExecutor).getMaximumPoolSize();
      will(returnValue(4));
    }});

    assertEquals(4, getStats().getMaximumPoolSize());
  }

  private ThreadPoolStats getStats() {
    return new ThreadPoolStats() {
      @Override
      public ThreadPoolExecutor getThreadPool() {
        return threadPoolExecutor;
      }
    };
  }

}
