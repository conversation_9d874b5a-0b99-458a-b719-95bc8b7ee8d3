package com.kaching.platform.monitoring.icinga;

import static org.junit.Assert.assertEquals;

import java.lang.management.ThreadMXBean;

import org.jmock.Mockery;
import org.junit.After;
import org.junit.Test;

import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.monitoring.RunningQueriesSummarizer;
import com.kaching.platform.monitoring.ThreadDumpFileGenerator;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.WExpectations;

public class CheckActiveJvmThreadCountTest {

  private final Mockery mockery = Mockeries.mockery(true);
  private final RunningQueriesSummarizer runningQueriesSummarizerMock = mockery.mock(RunningQueriesSummarizer.class);
  private final ThreadMXBean threadMXBeanMock = mockery.mock(ThreadMXBean.class);
  private final ThreadDumpFileGenerator threadDumpGenerator = mockery.mock(ThreadDumpFileGenerator.class);

  @After
  public void tearDown() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process_returnsOKAY() {
    CheckActiveJvmThreadCount query = createQuery();
    mockery.checking(new WExpectations() {{
      never(runningQueriesSummarizerMock).getSummaryText(with(any(RunningQueriesSummarizer.SortType.class)));
      oneOf(threadMXBeanMock).getThreadCount();
      will(returnValue(900));
      oneOf(threadMXBeanMock).getDaemonThreadCount();
      will(returnValue(0));
    }});
    IcingaOutput icingaOutput = query.getIcingaOutput();
    assertEquals(IcingaOutput.ExitCode.OKAY, icingaOutput.getExitCode());
    assertEquals(
        "There are currently 0 daemon threads running and 900 total threads running. Threshold count for total threads is 2000.",
        icingaOutput.getMessage());
  }

  @Test
  public void process_returnsOKAYAndCallsRunningQueriesSummarizer() {
    CheckActiveJvmThreadCount query = createQuery();
    mockery.checking(new WExpectations() {{
      oneOf(runningQueriesSummarizerMock).getSummaryText(RunningQueriesSummarizer.SortType.time);
      will(returnValue(""));
      oneOf(threadMXBeanMock).getThreadCount();
      will(returnValue(2000));
      oneOf(threadMXBeanMock).getDaemonThreadCount();
      will(returnValue(0));
    }});
    IcingaOutput icingaOutput = query.getIcingaOutput();
    assertEquals(IcingaOutput.ExitCode.OKAY, icingaOutput.getExitCode());
    assertEquals(
        "There are currently 0 daemon threads running and 2000 total threads running. Threshold count for total threads is 2000.",
        icingaOutput.getMessage());
  }

  @Test
  public void process_returnsCRITICAL() {
    CheckActiveJvmThreadCount query = createQuery();
    mockery.checking(new WExpectations() {{
      oneOf(runningQueriesSummarizerMock).getSummaryText(RunningQueriesSummarizer.SortType.time);
      will(returnValue(""));
      oneOf(threadMXBeanMock).getThreadCount();
      will(returnValue(2001));
      oneOf(threadMXBeanMock).getDaemonThreadCount();
      will(returnValue(500));
      oneOf(threadDumpGenerator).generate();
    }});
    IcingaOutput icingaOutput = query.getIcingaOutput();
    assertEquals(IcingaOutput.ExitCode.CRITICAL, icingaOutput.getExitCode());
    assertEquals(
        "There are currently 500 daemon threads running and 2001 total threads running. Threshold count for total threads is 2000." +
            "\n" + "Please check the logs for running queries as PagerDuty will truncate output.",
        icingaOutput.getMessage());
  }

  private CheckActiveJvmThreadCount createQuery() {
    CheckActiveJvmThreadCount query = new CheckActiveJvmThreadCount();
    query.threadMXBean = threadMXBeanMock;
    query.serviceKind = KachingServices.singleton(KachingServices.UM.class);
    query.runningQueriesSummarizer = runningQueriesSummarizerMock;
    query.threadDumpFileGenerator = threadDumpGenerator;
    return query;
  }

}