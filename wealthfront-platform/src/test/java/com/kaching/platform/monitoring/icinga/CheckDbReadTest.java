package com.kaching.platform.monitoring.icinga;

import static com.kaching.platform.monitoring.icinga.IcingaOutput.ExitCode.CRITICAL;
import static com.kaching.util.mail.Pager.Device.PAGER_INVESTMENT_SERVICES;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;

import org.jmock.Mockery;
import org.junit.Test;

import com.kaching.platform.guice.KachingServices.BI;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.testing.Mockeries;

public class CheckDbReadTest {

  Mockery mockery = Mockeries.mockery();
  Transacter transacter = mockery.mock(Transacter.class);

  CheckDbRead checkDbRead = new CheckDbRead(new BI(), transacter);

  @Test
  public void looksUpPagerFromKind() {
    IcingaMetadata metadata = checkDbRead.getIcingaMetadata();
    assertThat(metadata.getIcingaGroup(), is(PAGER_INVESTMENT_SERVICES));
  }

  @Test
  public void timeout() {
    checkDbRead.setCheckTimeoutSeconds(0);
    IcingaOutput output = checkDbRead.getIcingaOutput();
    assertThat(output.getExitCode(), is(CRITICAL));
    assertThat(output.getMessage(), is("Transacter execution timed out after 0 seconds"));
  }

}