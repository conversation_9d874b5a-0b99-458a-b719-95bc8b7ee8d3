package com.kaching.platform.monitoring.icinga;

import static org.junit.Assert.assertEquals;

import org.jmock.Expectations;
import org.jmock.Mockery;
import org.junit.After;
import org.junit.Test;

import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.monitoring.nscaspooler.NscaspoolerClient;
import com.kaching.platform.monitoring.nscaspooler.NscaspoolerServiceRunData;
import com.kaching.platform.monitoring.nscaspooler.NscaspoolerServiceRunResponse;
import com.kaching.platform.testing.Mockeries;
import com.kaching.util.functional.Result;
import com.kaching.util.mail.Pager;

public class RunIcingaCheckTest {

  Mockery mockery = Mockeries.mockery(true);
  BoundIcingaCheckFactory boundIcingaCheckFactory = mockery.mock(BoundIcingaCheckFactory.class);
  NscaspoolerClient nscaspoolerClient = mockery.mock(NscaspoolerClient.class);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process_returnsSuccessOutput() {
    RunIcingaCheck query = new RunIcingaCheck("SomeCheck");
    query.boundIcingaCheckFactory = boundIcingaCheckFactory;
    query.nscaspoolerClient = nscaspoolerClient;
    query.packaging = "rpm";
    query.serviceId = "nop0";
    query.serviceKind = new KachingServices.NOP();

    NscaspoolerServiceRunResponse response = new NscaspoolerServiceRunResponse.Builder()
        .withError(false)
        .withMessage("")
        .withServiceRunData(
            new NscaspoolerServiceRunData.Builder()
                .withMessage("ok")
                .withExitCode(0)
                .build()
        )
        .build();
    mockery.checking(new Expectations() {{
      oneOf(boundIcingaCheckFactory).getBoundIcingaCheck("SomeCheck");
      will(returnValue(new SomeCheck()));
      oneOf(nscaspoolerClient).runService("nop_SomeCheck");
      will(returnValue(Result.of(response)));
    }});

    IcingaOutput actual = query.process();
    assertEquals("ok", actual.getMessage());
    assertEquals(IcingaOutput.ExitCode.OKAY, actual.getExitCode());
  }

  @Test
  public void process_returnsFailureOutput() {
    RunIcingaCheck query = new RunIcingaCheck("SomeCheck");
    query.boundIcingaCheckFactory = boundIcingaCheckFactory;
    query.nscaspoolerClient = nscaspoolerClient;
    query.packaging = "rpm";
    query.serviceId = "nop0";
    query.serviceKind = new KachingServices.NOP();

    NscaspoolerServiceRunResponse response = new NscaspoolerServiceRunResponse.Builder()
        .withError(false)
        .withMessage("")
        .withServiceRunData(
            new NscaspoolerServiceRunData.Builder()
                .withMessage("oops")
                .withExitCode(2)
                .build()
        )
        .build();
    mockery.checking(new Expectations() {{
      oneOf(boundIcingaCheckFactory).getBoundIcingaCheck("SomeCheck");
      will(returnValue(new SomeCheck()));
      oneOf(nscaspoolerClient).runService("nop_SomeCheck");
      will(returnValue(Result.of(response)));
    }});

    IcingaOutput actual = query.process();
    assertEquals("oops", actual.getMessage());
    assertEquals(IcingaOutput.ExitCode.CRITICAL, actual.getExitCode());
  }

  @Test
  public void process_returnsErrorAsUnknown() {
    RunIcingaCheck query = new RunIcingaCheck("SomeCheck");
    query.boundIcingaCheckFactory = boundIcingaCheckFactory;
    query.nscaspoolerClient = nscaspoolerClient;
    query.packaging = "rpm";
    query.serviceId = "nop0";
    query.serviceKind = new KachingServices.NOP();

    NscaspoolerServiceRunResponse response = new NscaspoolerServiceRunResponse.Builder()
        .withError(true)
        .withMessage("oops")
        .build();
    mockery.checking(new Expectations() {{
      oneOf(boundIcingaCheckFactory).getBoundIcingaCheck("SomeCheck");
      will(returnValue(new SomeCheck()));
      oneOf(nscaspoolerClient).runService("nop_SomeCheck");
      will(returnValue(Result.of(response)));
    }});

    IcingaOutput actual = query.process();
    assertEquals("oops", actual.getMessage());
    assertEquals(IcingaOutput.ExitCode.UNKNOWN, actual.getExitCode());
  }

  @Test
  public void process_returnsFailureAsUnknown() {
    RunIcingaCheck query = new RunIcingaCheck("SomeCheck");
    query.boundIcingaCheckFactory = boundIcingaCheckFactory;
    query.nscaspoolerClient = nscaspoolerClient;
    query.packaging = "rpm";
    query.serviceId = "nop0";
    query.serviceKind = new KachingServices.NOP();

    mockery.checking(new Expectations() {{
      oneOf(boundIcingaCheckFactory).getBoundIcingaCheck("SomeCheck");
      will(returnValue(new SomeCheck()));
      oneOf(nscaspoolerClient).runService("nop_SomeCheck");
      will(returnValue(Result.fail("server error")));
    }});

    IcingaOutput actual = query.process();
    assertEquals("server error", actual.getMessage());
    assertEquals(IcingaOutput.ExitCode.UNKNOWN, actual.getExitCode());
  }

  @Test
  public void process_withContainerPackaging_returnsOutput() {
    RunIcingaCheck query = new RunIcingaCheck("SomeCheck");
    query.boundIcingaCheckFactory = boundIcingaCheckFactory;
    query.nscaspoolerClient = nscaspoolerClient;
    query.packaging = "container";
    query.serviceId = "nop0";
    query.serviceKind = new KachingServices.NOP();

    NscaspoolerServiceRunResponse response = new NscaspoolerServiceRunResponse.Builder()
        .withError(false)
        .withMessage("")
        .withServiceRunData(
            new NscaspoolerServiceRunData.Builder()
                .withMessage("ok")
                .withExitCode(0)
                .build()
        )
        .build();
    mockery.checking(new Expectations() {{
      oneOf(boundIcingaCheckFactory).getBoundIcingaCheck("SomeCheck");
      will(returnValue(new SomeCheck()));
      oneOf(nscaspoolerClient).runService("nop0_SomeCheck");
      will(returnValue(Result.of(response)));
    }});

    IcingaOutput actual = query.process();
    assertEquals("ok", actual.getMessage());
    assertEquals(IcingaOutput.ExitCode.OKAY, actual.getExitCode());
  }

  @Test
  public void process_withUnboundServiceKind_returnsUnknown() {
    RunIcingaCheck query = new RunIcingaCheck("SomeCheck");
    query.boundIcingaCheckFactory = boundIcingaCheckFactory;
    query.nscaspoolerClient = nscaspoolerClient;
    query.packaging = "rpm";
    query.serviceId = "nop0";
    query.serviceKind = null;

    IcingaOutput actual = query.process();
    assertEquals("unknown service", actual.getMessage());
    assertEquals(IcingaOutput.ExitCode.UNKNOWN, actual.getExitCode());
  }

  private static class SomeCheck implements IcingaCheck {

    @Override
    public IcingaMetadata getIcingaMetadata() {
      return new IcingaMetadata.IcingaMetadataBuilder(SomeCheck.class)
          .withIcingaGroup(Pager.Device.PAGER_SYSTEMS)
          .build();
    }

    @Override
    public IcingaOutput getIcingaOutput() {
      return new IcingaOutput.IcingaOutputBuilder().build();
    }

  }

}
