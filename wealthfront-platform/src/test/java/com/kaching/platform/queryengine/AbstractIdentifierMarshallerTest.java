package com.kaching.platform.queryengine;

import static com.google.common.collect.Lists.newArrayList;
import static com.kaching.platform.queryengine.AbstractIdentifierMarshaller.createAbstractIdentifierMarshaller;
import static com.kaching.platform.queryengine.AbstractIdentifierMarshaller.getWrappedIdentifierClass;
import static com.wealthfront.test.Assert.assertMarshalling;
import static com.wealthfront.test.Assert.assertOptionEmpty;
import static com.wealthfront.test.Assert.assertOptionEquals;
import static com.wealthfront.test.Assert.assertSameJson;
import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import java.io.IOException;
import java.io.Reader;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Test;

import com.google.inject.util.Types;
import com.kaching.entities.PhoneNumber;
import com.kaching.platform.common.AbstractIdentifier;
import com.kaching.platform.common.AbstractIntIdentifier;
import com.kaching.platform.common.AbstractLongIdentifier;
import com.kaching.platform.common.Identifier;
import com.kaching.platform.sentry.SentryDsn;
import com.kaching.user.UserId;
import com.kaching.util.mail.QueuedPageId;
import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;

public class AbstractIdentifierMarshallerTest {

  static class SomeGenericAbstractIdentifier extends AbstractIdentifier<Long> {

    SomeGenericAbstractIdentifier(long id) {
      super(id);
    }

  }

  @Test
  public void marshallGeneric() {

    Marshaller<SomeGenericAbstractIdentifier> marshaller = createAbstractIdentifierMarshaller(SomeGenericAbstractIdentifier.class);
    assertMarshalling(marshaller, Json.number(123), new SomeGenericAbstractIdentifier(123L));
  }

  static class SomeAbstractLongIdentifier extends AbstractLongIdentifier {

    SomeAbstractLongIdentifier(long id) {
      super(id);
    }

  }

  @Test
  public void marshallLong() {
    Marshaller<SomeAbstractLongIdentifier> marshaller = createAbstractIdentifierMarshaller(SomeAbstractLongIdentifier.class);
    assertMarshalling(marshaller, Json.number(123), new SomeAbstractLongIdentifier(123L));
  }

  static class SomeAbstractIntIdentifier extends AbstractIntIdentifier {

    SomeAbstractIntIdentifier(int id) {
      super(id);
    }

  }

  @Test
  public void marshallInt() {
    Marshaller<SomeAbstractIntIdentifier> marshaller = createAbstractIdentifierMarshaller(SomeAbstractIntIdentifier.class);
    assertMarshalling(marshaller, Json.number(123), new SomeAbstractIntIdentifier(123));
  }

  @Test
  public void marshall_null() {
    Marshaller<UserId> marshaller = createAbstractIdentifierMarshaller(UserId.class);
    assertMarshalling(marshaller, Json.NULL, null);
  }

  @Test
  public void marshallList() {
    Marshaller<PhoneNumber> marshaller = createAbstractIdentifierMarshaller(PhoneNumber.class);
    Collection<PhoneNumber> objectCollection =
        newArrayList(new PhoneNumber("911"), new PhoneNumber("411"), null, new PhoneNumber("1800"));
    Json.Array jsonArray = Json.array("911", "411", null, "1800");
    assertSameJson(jsonArray, marshaller.marshallList(objectCollection));
    assertSameJson(jsonArray, marshaller.marshallList(marshaller.unmarshallList(jsonArray)));
  }

  @Test
  public void marshallMap() {
    Marshaller<QueuedPageId> marshaller = createAbstractIdentifierMarshaller(QueuedPageId.class);
    Map<String, QueuedPageId> objectMap = new HashMap<>();
    objectMap.put("A", new QueuedPageId(1L));
    objectMap.put("B", new QueuedPageId(2L));
    objectMap.put("C", null);
    Json.Object json = Json.object(Json.string("A"), Json.number(1), Json.string("B"), Json.number(2), Json.string("C"), Json.NULL);
    assertSameJson(json, marshaller.marshallMap(objectMap));
    assertSameJson(json, marshaller.marshallMap(marshaller.unmarshallMap(json)));
  }

  @Test
  public void marshallMap_stringKeyMarshaller() {
    Marshaller<QueuedPageId> marshaller = createAbstractIdentifierMarshaller(QueuedPageId.class);
    Marshaller<SentryDsn> keyMarshaller = createAbstractIdentifierMarshaller(SentryDsn.class);
    Map<SentryDsn, QueuedPageId> objectMap = new HashMap<>();
    objectMap.put(new SentryDsn("A"), new QueuedPageId(1L));
    objectMap.put(new SentryDsn("B"), new QueuedPageId(2L));
    objectMap.put(new SentryDsn("C"), null);
    Json.Object json = Json.object(Json.string("\"A\""), Json.number(1), Json.string("\"B\""), Json.number(2), Json.string("\"C\""), Json.NULL);
    assertSameJson(json, marshaller.marshallMap(objectMap, keyMarshaller));
    assertSameJson(json, marshaller.marshallMap(marshaller.unmarshallMap(json, keyMarshaller), keyMarshaller));
  }

  @Test
  public void marshallMap_longKeyMarshaller() {
    Marshaller<SentryDsn> marshaller = createAbstractIdentifierMarshaller(SentryDsn.class);
    Marshaller<QueuedPageId> keyMarshaller = createAbstractIdentifierMarshaller(QueuedPageId.class);
    Map<QueuedPageId, SentryDsn> objectMap = new HashMap<>();
    objectMap.put(new QueuedPageId(1L), new SentryDsn("A"));
    objectMap.put(new QueuedPageId(2L), new SentryDsn("B"));
    objectMap.put(new QueuedPageId(3L), null);
    Json.Object json = Json.object(Json.string("1"), Json.string("A"), Json.string("2"), Json.string("B"), Json.string("3"), Json.NULL);
    assertSameJson(json, marshaller.marshallMap(objectMap, keyMarshaller));
    assertSameJson(json, marshaller.marshallMap(marshaller.unmarshallMap(json, keyMarshaller), keyMarshaller));
  }

  @Test
  public void unmarshallStream_string() throws IOException {
    Marshaller<PhoneNumber> marshaller = createAbstractIdentifierMarshaller(PhoneNumber.class);
    Json.Array jsonArray = Json.array("911", "411", null, "1800");
    Reader reader = new StringReader(jsonArray.toString());
    List<PhoneNumber> receivedAbstractIds = new ArrayList<>();
    Marshaller.Generator<PhoneNumber> generator = receivedAbstractIds::add;
    marshaller.unmarshallStream(reader, generator);
    assertEquals(newArrayList(new PhoneNumber("911"), new PhoneNumber("411"), null, new PhoneNumber("1800")),
        receivedAbstractIds);
  }

  @Test
  public void unmarshallStream_long() throws IOException {
    Marshaller<QueuedPageId> marshaller = createAbstractIdentifierMarshaller(QueuedPageId.class);
    Reader reader = new StringReader(Json.array(1, 2, null, 4).toString());
    List<QueuedPageId> receivedAbstractIds = new ArrayList<>();
    Marshaller.Generator<QueuedPageId> generator = receivedAbstractIds::add;
    marshaller.unmarshallStream(reader, generator);
    assertEquals(newArrayList(new QueuedPageId(1), new QueuedPageId(2), null, new QueuedPageId(4)),
        receivedAbstractIds);
  }

  @Test
  public void nullSafeGetId() {
    AbstractIdentifierMarshaller<Long, QueuedPageId> marshaller = createAbstractIdentifierMarshaller(QueuedPageId.class);
    assertEquals(Long.valueOf(123L), marshaller.nullSafeGetId(new QueuedPageId(123L)));
    assertNull(marshaller.nullSafeGetId(null));
  }

  @Test
  public void nullSafeConstructor() {
    AbstractIdentifierMarshaller<Long, QueuedPageId> marshaller = createAbstractIdentifierMarshaller(QueuedPageId.class);
    assertEquals(new QueuedPageId(123L), marshaller.nullSafeConstructor(123L));
    assertNull(marshaller.nullSafeConstructor(null));
  }

  @Test
  public void noSuperClass_throws() {
    assertThrows(IllegalArgumentException.class, "class Object must extend a parameterized Identifier",
        () -> createAbstractIdentifierMarshaller(Object.class));
  }

  @Test
  public void notAnAbstractIdentifier_throws() {
    assertThrows(IllegalArgumentException.class, "class Long must extend a parameterized Identifier",
        () -> createAbstractIdentifierMarshaller(Long.class));
  }

  private static class UnparameterizedAbstractIdentifier extends AbstractIdentifier {
    private UnparameterizedAbstractIdentifier(Comparable id) {
      super(id);
    }
  }

  @Test
  public void unparameterizedAbstractIdentifier_throws() {
    assertThrows(IllegalArgumentException.class,
        "class UnparameterizedAbstractIdentifier must extend a parameterized Identifier",
        () -> createAbstractIdentifierMarshaller(UnparameterizedAbstractIdentifier.class));
  }

  private static class Wrapper<K> implements Comparable<Wrapper<K>> {

    @Override
    public int compareTo(Wrapper<K> o) {
      return 0;
    }

  }

  private static class AbstractIdentifierWithParameterizedParameter<K> extends AbstractIdentifier<Wrapper<Integer>> {
    AbstractIdentifierWithParameterizedParameter(Wrapper<Integer> id) {
      super(id);
    }
  }

  @Test
  public void abstractIdentifierWithParameterizedParameter_throws() {
    assertThrows(IllegalArgumentException.class,
        "Identifier must have a non-parameterized parameter",
        () -> createAbstractIdentifierMarshaller(AbstractIdentifierWithParameterizedParameter.class));
  }

  private static class AbstractIdentifierWithIncompatibleConstructor extends AbstractIdentifier<Integer> {
    private AbstractIdentifierWithIncompatibleConstructor(int id, boolean unused) {
      super(id);
    }
  }

  private static class AbstractIntIdentifierWithIncompatibleConstructor extends AbstractIntIdentifier {
    private AbstractIntIdentifierWithIncompatibleConstructor(int id, boolean unused) {
      super(id);
    }
  }

  @Test
  public void abstractIdentifierWithIncompatibleConstructor_throws() {
    assertThrows(IllegalArgumentException.class,
        "class AbstractIdentifierWithIncompatibleConstructor must have a one-arg constructor accepting class Integer",
        () -> createAbstractIdentifierMarshaller(AbstractIdentifierWithIncompatibleConstructor.class));
    assertThrows(IllegalArgumentException.class,
        "class AbstractIntIdentifierWithIncompatibleConstructor must have a one-arg constructor accepting class Integer",
        () -> createAbstractIdentifierMarshaller(AbstractIntIdentifierWithIncompatibleConstructor.class));
  }

  private static class Foo implements Comparable<Foo> {
    @Override
    public int compareTo(Foo o) {
      return 0;
    }
  }

  private static class AbstractIdentifierWithUnmarshallableParameter extends AbstractIdentifier<Foo> {
    private AbstractIdentifierWithUnmarshallableParameter(Foo id) {
      super(id);
    }
  }

  @Test
  public void abstractIdentifierWithUnmarshallableParameter() {
    assertThrows(IllegalArgumentException.class,
        "Cannot find marshaller for class Foo (the Identifier's parameter)",
        () -> createAbstractIdentifierMarshaller(AbstractIdentifierWithUnmarshallableParameter.class));
  }

  @Test
  public void getWrappedIdentifierClass_handlesAllValidVariations() {
    class WrapsAnInteger extends AbstractIdentifier<Integer> {

      WrapsAnInteger(Integer id) {
        super(id);
      }

    }
    class WrapsAnIntegerSubclass extends WrapsAnInteger {

      WrapsAnIntegerSubclass(Integer id) {
        super(id);
      }

    }
    class WrapsAnInt extends AbstractIntIdentifier {

      WrapsAnInt(int id) {
        super(id);
      }

    }
    class WrapsAnIntSubclass extends WrapsAnInt {

      WrapsAnIntSubclass(int id) {
        super(id);
      }

    }
    class WrapsNothing extends AbstractIdentifier {

      WrapsNothing(Integer id) {
        super(id);
      }

    }
    class WrapsGeneric extends AbstractIdentifier<AbstractIdentifier<String>> {

      WrapsGeneric(AbstractIdentifier<String> id) {
        super(id);
      }

    }
    class ImplementsInterfaceDirectly implements Identifier<Integer> {

      @Override
      public Integer getId() {
        return 1;
      }

    }

    assertOptionEquals(Integer.class, getWrappedIdentifierClass(WrapsAnInteger.class));
    assertOptionEquals(Integer.class, getWrappedIdentifierClass(WrapsAnIntegerSubclass.class));
    assertOptionEquals(Integer.class, getWrappedIdentifierClass(WrapsAnInt.class));
    assertOptionEquals(Integer.class, getWrappedIdentifierClass(WrapsAnIntSubclass.class));
    assertOptionEmpty(getWrappedIdentifierClass(WrapsNothing.class));
    assertOptionEquals(Types.newParameterizedType(AbstractIdentifier.class, String.class),
        getWrappedIdentifierClass(WrapsGeneric.class));
    assertOptionEquals(Integer.class, getWrappedIdentifierClass(ImplementsInterfaceDirectly.class));
    assertOptionEmpty(getWrappedIdentifierClass(String.class));
  }

  @Test
  public void getMarshalledClass() {
    Marshaller<UserId> marshallerUser = createAbstractIdentifierMarshaller(UserId.class);
    assertEquals(Json.Number.class, marshallerUser.getMarshalledClass());
    Marshaller<SentryDsn> keyMarshaller = createAbstractIdentifierMarshaller(SentryDsn.class);
    assertEquals(Json.String.class, keyMarshaller.getMarshalledClass());
  }

}