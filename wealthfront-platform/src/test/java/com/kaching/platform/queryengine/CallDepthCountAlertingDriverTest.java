package com.kaching.platform.queryengine;

import static com.kaching.platform.queryengine.QueryEngineModule.CALL_DEPTH_COUNT_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.TRACE_KEY;
import static com.wealthfront.test.Assert.assertThrows;

import org.jmock.Expectations;
import org.jmock.Mockery;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.google.inject.Injector;
import com.google.inject.Provider;
import com.google.inject.util.Providers;
import com.kaching.platform.testing.Mockeries;

public class CallDepthCountAlertingDriverTest {

  private Mockery mockery;
  private ScopedQueryDriver delegate;
  private Injector injector;
  private CallDepthCountAlertingDriver driver;
  private CallDepthCountAlertingDriver.Alerting alerting;
  private Query<?> query;

  @Before
  public void before() {
    mockery = Mockeries.mockery(true);
    delegate = mockery.mock(ScopedQueryDriver.class);
    injector = mockery.mock(Injector.class);
    driver = new CallDepthCountAlertingDriver(delegate, injector);
    alerting = new CallDepthCountAlertingDriver.Alerting();
    alerting.queryScope = mockery.mock(QueryScope.class);
    query = new QueryImpl();
  }

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void execute() {
    final Provider<? extends PostProcessor> provider = Providers.of(null);
    final CallDepthCountAlertingDriver.Alerting alerting = mockery.mock(CallDepthCountAlertingDriver.Alerting.class);
    mockery.checking(new Expectations() {{
      oneOf(injector).getInstance(CallDepthCountAlertingDriver.Alerting.class);
      will(returnValue(alerting));

      oneOf(alerting).alertAndTerminateIfCallDepthCountExceedsThreshold(query);

      oneOf(delegate).execute(query, provider);
    }});
    driver.execute(query, provider);
  }

  @Test
  public void alertWhenCallDepthCountExceedsThreshold() {
    mockery.checking(new Expectations() {{
      oneOf(alerting.queryScope).get(CALL_DEPTH_COUNT_KEY);
      will(returnValue(new CallDepthCount(100L)));

      Trace trace = Trace.newTrace();
      oneOf(alerting.queryScope).get(TRACE_KEY);
      will(returnValue(trace));
    }});

    alerting.alertAndTerminateIfCallDepthCountExceedsThreshold(query);
  }

  @Test
  public void whenCallDepthCountExceedsThresholdAndNotInMultipleOfHundred_doesNotAlert() {
    mockery.checking(new Expectations() {{
      oneOf(alerting.queryScope).get(CALL_DEPTH_COUNT_KEY);
      will(returnValue(new CallDepthCount(101L)));
    }});

    alerting.alertAndTerminateIfCallDepthCountExceedsThreshold(query);
  }

  @Test
  public void alertAndTerminateWhenCallCountCountExceedsThreshold() {
    Trace trace = Trace.newTrace();
    mockery.checking(new Expectations() {{
      oneOf(alerting.queryScope).get(CALL_DEPTH_COUNT_KEY);
      will(returnValue(new CallDepthCount(200L)));

      exactly(2).of(alerting.queryScope).get(TRACE_KEY);
      will(returnValue(trace));
    }});

    assertThrows(RuntimeException.class, () -> alerting.alertAndTerminateIfCallDepthCountExceedsThreshold(query));
  }

  private static class QueryImpl extends AbstractQuery<Object> {

    @Override
    public Object process() {
      return null;
    }

  }

}
