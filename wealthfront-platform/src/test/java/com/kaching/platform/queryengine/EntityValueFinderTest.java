package com.kaching.platform.queryengine;

import static com.kaching.platform.queryengine.EntityValueFinder.visit;
import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.junit.Test;

import com.google.inject.TypeLiteral;
import com.kaching.platform.common.Option;
import com.kaching.platform.queryengine.EntityValueFinder.DefaultVisitor;

public class EntityValueFinderTest {

  @Test
  public void visisList() {
    DefaultVisitor<Boolean> visitor = new DefaultVisitor<Boolean>(false) {
      @Override
      public Boolean visitList(TypeLiteral<?> type) {
        assertEquals(TypeLiteral.get(String.class), type);
        return true;
      }
    };
    assertTrue(visit(new TypeLiteral<List<String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Map<String, String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Set<String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Option<String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<String>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Collection<String>>() {}, visitor));
  }

  @Test
  public void visitMap() {
    DefaultVisitor<Boolean> visitor = new DefaultVisitor<Boolean>(false) {
      @Override
      public Boolean visitMap(TypeLiteral<?> keyType, TypeLiteral<?> valueType) {
        assertEquals(TypeLiteral.get(String.class), keyType);
        assertEquals(TypeLiteral.get(Integer.class), valueType);
        return true;
      }
    };
    assertFalse(visit(new TypeLiteral<List<String>>() {}, visitor));
    assertTrue(visit(new TypeLiteral<Map<String, Integer>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Set<String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Option<String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<String>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Collection<String>>() {}, visitor));
  }

  @Test
  public void visitOption() {
    DefaultVisitor<Boolean> visitor = new DefaultVisitor<Boolean>(false) {
      @Override
      public Boolean visitOption(TypeLiteral<?> type) {
        assertEquals(TypeLiteral.get(String.class), type);
        return true;
      }
    };
    assertFalse(visit(new TypeLiteral<List<String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Map<String, String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Set<String>>() {}, visitor));
    assertTrue(visit(new TypeLiteral<Option<String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<String>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Collection<String>>() {}, visitor));
  }

  @Test
  public void visitSet() {
    DefaultVisitor<Boolean> visitor = new DefaultVisitor<Boolean>(false) {
      @Override
      public Boolean visitSet(TypeLiteral<?> type) {
        assertEquals(TypeLiteral.get(String.class), type);
        return true;
      }
    };
    assertFalse(visit(new TypeLiteral<List<String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Map<String, String>>() {}, visitor));
    assertTrue(visit(new TypeLiteral<Set<String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Option<String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<String>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Collection<String>>() {}, visitor));
  }

  @Test
  public void visitValue() {
    DefaultVisitor<Boolean> visitor = new DefaultVisitor<Boolean>(false) {
      @Override
      public Boolean visitValue(TypeLiteral<?> type) {
        assertEquals(TypeLiteral.get(String.class), type);
        return true;
      }
    };
    assertFalse(visit(new TypeLiteral<List<String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Map<String, String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Set<String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Option<String>>() {}, visitor));
    assertTrue(visit(new TypeLiteral<String>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Collection<String>>() {}, visitor));
  }

  @Test
  public void visitCollection() {
    DefaultVisitor<Boolean> visitor = new DefaultVisitor<Boolean>(false) {
      @Override
      public Boolean visitUnsupported(TypeLiteral<?> type) {
        assertEquals(new TypeLiteral<Collection<String>>() {}, type);
        return true;
      }
    };
    assertFalse(visit(new TypeLiteral<List<String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Map<String, String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Set<String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<Option<String>>() {}, visitor));
    assertFalse(visit(new TypeLiteral<String>() {}, visitor));
    assertTrue(visit(new TypeLiteral<Collection<String>>() {}, visitor));
  }

  @Test
  public void testDefaultValueVisitor() {
    DefaultVisitor<Boolean> visitor = new DefaultVisitor<Boolean>(true) {};
    assertTrue(visit(new TypeLiteral<List<String>>() {}, visitor));
    assertTrue(visit(new TypeLiteral<Map<String, String>>() {}, visitor));
    assertTrue(visit(new TypeLiteral<Set<String>>() {}, visitor));
    assertTrue(visit(new TypeLiteral<Option<String>>() {}, visitor));
    assertTrue(visit(new TypeLiteral<String>() {}, visitor));
    assertTrue(visit(new TypeLiteral<Collection<String>>() {}, visitor));

    SerializerProvider serializer = new AbstractSerializerProvider() {};
    assertFalse(serializer.getSerializer(new TypeLiteral<Collection<String>>() {}).isDefined());
  }

  @Test
  public void testUnsupportedOperation() {
    DefaultVisitor<Boolean> visitor = new DefaultVisitor<Boolean>() {};
    assertThrows(UnsupportedOperationException.class, () -> visit(new TypeLiteral<List<String>>() {}, visitor));
    assertThrows(UnsupportedOperationException.class, () -> visit(new TypeLiteral<Map<String, String>>() {}, visitor));
    assertThrows(UnsupportedOperationException.class, () -> visit(new TypeLiteral<Set<String>>() {}, visitor));
    assertThrows(UnsupportedOperationException.class, () -> visit(new TypeLiteral<Option<String>>() {}, visitor));
    assertThrows(UnsupportedOperationException.class, () -> visit(new TypeLiteral<String>() {}, visitor));
    assertThrows(UnsupportedOperationException.class, () -> visit(new TypeLiteral<List<String>>() {}, visitor));
  }

}