package com.kaching.platform.queryengine;

import static com.kaching.platform.testing.QueryMatcher.query;
import static com.twolattes.json.Json.array;
import static com.twolattes.json.Json.object;
import static com.wealthfront.test.Assert.assertEmpty;
import static com.wealthfront.test.Assert.assertSameJson;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.jmock.Expectations;
import org.jmock.Mockery;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.kaching.DefaultKachingMarshallers;
import com.kaching.platform.components.Component;
import com.kaching.platform.testing.Mockeries;
import com.twolattes.json.EntityMarshaller;

public class GetClientInfoTest {

  private static final EntityMarshaller<GetClientInfo.ClientJarInfo> MARSHALLER =
      DefaultKachingMarshallers.createEntityMarshaller(
          GetClientInfo.ClientJarInfo.class);

  private static final String CLASSPATH_JAR_NAMES = """
          SomeJarWithTestInTheName-1.234.jar
          library.jar
          some-client-1.234-SNAPSHOT.jar
          another-client-9.876.jar
          jar-with-test-in-the-name.jar""";

  private Mockery mockery;
  private QueryExecutorService queryExecutorService;

  @Before
  public void setUp() {
    mockery = Mockeries.mockery();
    queryExecutorService = mockery.mock(QueryExecutorService.class);
  }

  @After
  public void tearDown() {
    mockery.assertIsSatisfied();
  }

  @Component
  static class DefaultComponent {}

  @Component(clientName = "some-client", dependsOn = DefaultComponent.class)
  static class ClientComponent {}

  @Component(clientName = "another-client", dependsOn = ClientComponent.class)
  static class AnotherClientComponent {}

  @Test
  public void process_null() {
    GetClientInfo query = getQuery(null);
    assertEmpty(query.process());
  }

  @Test
  public void process_noClients() {
    GetClientInfo query = getQuery(ImmutableList.of(DefaultComponent.class.getAnnotation(Component.class)));

    mockery.checking(new Expectations() {{
      oneOf(queryExecutorService).submitAndGetResult(with(query(new GetClasspathJarNames(""))));
      will(returnValue(CLASSPATH_JAR_NAMES));
    }});

    assertEmpty(query.process());
  }

  @Test
  public void process_oneClient() {
    GetClientInfo query =
        getQuery(ImmutableList.of(DefaultComponent.class, ClientComponent.class).stream()
            .map(c -> c.getAnnotation(Component.class))
            .collect(Collectors.toList()));

    mockery.checking(new Expectations() {{
      oneOf(queryExecutorService).submitAndGetResult(with(query(new GetClasspathJarNames(""))));
      will(returnValue(CLASSPATH_JAR_NAMES));
    }});

    assertSameJson(array(
        object(
            "artifact", "some-client",
            "jarName", "some-client-1.234-SNAPSHOT.jar",
            "version", "1.234-SNAPSHOT",
            "revision", "abcd"
        )
    ), MARSHALLER.marshallList(query.process()));
  }

  @Test
  public void process_multipleClients() {
    GetClientInfo query =
        getQuery(ImmutableList.of(DefaultComponent.class, ClientComponent.class, AnotherClientComponent.class).stream()
            .map(c -> c.getAnnotation(Component.class))
            .collect(Collectors.toList()));

    mockery.checking(new Expectations() {{
      oneOf(queryExecutorService).submitAndGetResult(with(query(new GetClasspathJarNames(""))));
      will(returnValue(CLASSPATH_JAR_NAMES));
    }});

    assertSameJson(array(
        object(
            "artifact", "some-client",
            "jarName", "some-client-1.234-SNAPSHOT.jar",
            "version", "1.234-SNAPSHOT",
            "revision", "abcd"
        ),
        object(
            "artifact", "another-client",
            "jarName", "another-client-9.876.jar",
            "version", "9.876",
            "revision", "abcd"
        )
    ), MARSHALLER.marshallList(query.process()));
  }

  @Test
  public void patternForClientJarName() {
    Pattern pattern = GetClientInfo.patternForClientArtifact("nop-client");

    Matcher matcher = pattern.matcher("nop-client-1.2.jar");
    assertTrue(matcher.matches());
    assertEquals("nop-client-1.2.jar", matcher.group(0));
    assertEquals("nop-client", matcher.group(1));
    assertEquals("1.2", matcher.group(2));

    matcher = pattern.matcher("nop-client-1.2-SNAPSHOT.jar");
    assertTrue(matcher.matches());
    assertEquals("nop-client-1.2-SNAPSHOT.jar", matcher.group(0));
    assertEquals("nop-client", matcher.group(1));
    assertEquals("1.2-SNAPSHOT", matcher.group(2));

    assertFalse(pattern.matcher("nop-client-1.2").matches());
    assertFalse(pattern.matcher("nop-client-1.2").matches());
    assertFalse(pattern.matcher("anop-client-1.2.jar").matches());
    assertFalse(pattern.matcher("nop-client-dev-1.2.jar").matches());
    assertFalse(pattern.matcher("nop-client-1.2-SNAPSHOT-a.jar").matches());
  }

  private GetClientInfo getQuery(List<Component> componentList) {
    GetClientInfo query = new GetClientInfo();
    query.queryExecutorService = queryExecutorService;
    query.components = componentList;
    query.version = "rabcd";
    return query;
  }

}