package com.kaching.platform.queryengine;

import static com.google.inject.Guice.createInjector;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.io.IOException;

import org.hamcrest.Matchers;
import org.jmock.Expectations;
import org.jmock.Mockery;
import org.joda.time.DateTime;
import org.junit.Test;
import org.quartz.JobExecutionContext;

import com.google.inject.AbstractModule;
import com.google.inject.ConfigurationException;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.kaching.platform.queryengine.InjectingJobDetail.InjectingJob;
import com.kaching.platform.testing.Mockeries;

public class InjectingJobDetailTest {

  private final Mockery mockery = Mockeries.mockery(true);
  private final StackTraceMonitor stackTraceMonitor = mockery.mock(StackTraceMonitor.class);

  public static class TestJob {

    private Appendable logger;

    @Inject
    public TestJob(DateTime now) {
    }

    public void setLogger(Appendable logger) {
      this.logger = logger;
    }

    public void run() throws IOException {
      logger.append("success");
    }
  }

  abstract class UnimplementedClass {

  }

  static class UninjectablePredicate implements Predicate {

    @Inject UnimplementedClass unimplementedClass;

    @Override
    public boolean satisfied() {
      return true;
    }

  }

  @Test
  public void exampleUsage() throws Exception {
    Injector injector = createInjector();
    InjectingJobDetail jobDetail = new InjectingJobDetail("job", "group", injector,
        TestJob.class, "run", new AlwaysTruePredicate());
    StringBuilder logger = new StringBuilder();
    jobDetail.setProperty("logger", logger);

    InjectingJob job = (InjectingJob) jobDetail.getJobClass().newInstance();
    job.execute(jobDetail.getJobDataMap());

    assertThat("success", is(equalTo(logger.toString())));
  }

  @Test
  public void executeRecordsInjectionFailure() throws Exception {
    Injector injector = createInjector(new AbstractModule() {

      @Override
      protected void configure() {
        bind(StackTraceMonitor.class).toInstance(stackTraceMonitor);
      }

    });

    final JobExecutionContext jobExecutionContext = mockery.mock(JobExecutionContext.class);
    final InjectingJobDetail jobDetail = new InjectingJobDetail("job", "group", injector,
        TestJob.class, "run", new UninjectablePredicate());
    mockery.checking(new Expectations() {{
      oneOf(jobExecutionContext).getJobDetail();
      will(returnValue(jobDetail));
      oneOf(stackTraceMonitor).add(with(Matchers.<Throwable>instanceOf(ConfigurationException.class)));
    }});

    InjectingJob job = (InjectingJob) jobDetail.getJobClass().newInstance();
    try {
      job.execute(jobDetail.getJobDataMap());
      fail("Expected exception");
    } catch (ConfigurationException ex) {
      assertTrue(ex.getMessage().contains("No implementation"));
    }
  }

}
