package com.kaching.platform.queryengine;

import static com.google.inject.Guice.createInjector;
import static com.kaching.platform.guice.TestingApplicationOptions.OPTIONS;
import static com.kaching.platform.queryengine.QueryEngineModule.API_REQUEST_METADATA_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.BUILT_IN_QUERIES;
import static com.kaching.platform.queryengine.QueryEngineModule.IKQ_REQUESTER_USERNAME;
import static com.kaching.platform.queryengine.QueryEngineModule.QUERY_CLASS_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.QUERY_OUTPUT_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.RETURN_STUB_FAKE_RESULT;
import static com.kaching.platform.queryengine.QueryEngineModule.SFE_END2END_TEST_USER;
import static com.kaching.platform.queryengine.QueryScope.ScopeRules.RELAXED;
import static com.kaching.platform.queryengine.QueryScope.ScopeRules.STRICT;
import static com.wealthfront.test.Assert.assertOptionEmpty;
import static com.wealthfront.test.Assert.assertOptionEquals;
import static java.util.Collections.singleton;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;

import java.util.Set;

import org.I0Itec.zkclient.ZkClient;
import org.eclipse.jetty.jmx.MBeanContainer;
import org.eclipse.jetty.server.Connector;
import org.eclipse.jetty.server.Server;
import org.eclipse.jetty.server.ServerConnector;
import org.joda.time.DateTimeZone;
import org.junit.Test;
import org.quartz.Scheduler;

import com.google.common.collect.ImmutableSet;
import com.google.inject.AbstractModule;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.Provider;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;
import com.google.inject.util.Types;
import com.kaching.DefaultJsonMarshallerFactory;
import com.kaching.KachingInstantiators;
import com.kaching.entities.ApiRequestMetadata;
import com.kaching.entities.IpAddress;
import com.kaching.platform.common.Errors;
import com.kaching.platform.common.Option;
import com.kaching.platform.guice.OptionsModule;
import com.kaching.platform.guice.TypeLiterals;
import com.kaching.platform.monitoring.icinga.IcingaCheck;
import com.kaching.platform.monitoring.icinga.NopIcingaCheck;
import com.kaching.platform.queryengine.authorization.Impersonator;
import com.kaching.platform.queryengine.postprocessors.IkqRequesterUsername;
import com.kaching.user.UserId;
import com.kaching.user.UserPlatform;

public class QueryEngineModuleTest {

  private final Injector injector = createInjector(
      new QueryEngineModule(
          12345,
          RELAXED,
          OPTIONS,
          SessionlessQueryDriver.class),
      new OptionsModule(OPTIONS));

  private final Injector strictInjector = createInjector(
      new QueryEngineModule(
          12345,
          STRICT,
          OPTIONS,
          SessionlessQueryDriver.class),
      new OptionsModule(OPTIONS));

  @Test
  public void canCreateQueryEngine() {
    assertNotNull(injector.getInstance(QueryEngine.class));
  }

  @Test
  public void queryOutputWriterIsBound() {
    assertNotNull(injector.getProvider(QUERY_OUTPUT_KEY));
  }

  @Test
  public void ikqOptionalRequesterUsernameIsBound() {
    injector.getInstance(QueryScope.class).cache(IKQ_REQUESTER_USERNAME, Option.some("julien"));
    Key<Option<String>> key = Key.get(new TypeLiteral<Option<String>>() {}, IkqRequesterUsername.class);
    Provider<Option<String>> provider = injector.getProvider(key);
    assertNotNull(provider);
    assertOptionEquals("julien", provider.get());
  }

  @Test
  public void ikqOptionalRequesterUsernameIsNotBound() {
    Key<Option<String>> key = Key.get(new TypeLiteral<Option<String>>() {}, IkqRequesterUsername.class);
    Provider<Option<String>> provider = injector.getProvider(key);
    assertNotNull(provider);
    assertOptionEmpty(provider.get());
  }

  @Test
  public void queryClassOptionalIsBound() {
    injector.getInstance(QueryScope.class).cache(QUERY_CLASS_KEY, HelloWorld.class);
    Key<Option<Class<? extends Query>>> key = Key.get(new TypeLiteral<Option<Class<? extends Query>>>() {});
    Provider<Option<Class<? extends Query>>> provider = injector.getProvider(key);
    assertNotNull(provider);
    assertOptionEquals(HelloWorld.class, provider.get());
  }

  @Test
  public void queryClassOptionalIsNotBound() {
    Key<Option<Class<? extends Query>>> key = Key.get(new TypeLiteral<Option<Class<? extends Query>>>() {});
    Provider<Option<Class<? extends Query>>> provider = injector.getProvider(key);
    assertNotNull(provider);
    assertOptionEmpty(provider.get());
  }

  @Test
  public void apiRequestMetadataOptionalIsBound() {
    ApiRequestMetadata apiRequestMetadata = ApiRequestMetadata.with()
        .userPlatform(UserPlatform.IOS)
        .appVersion("3.4.5")
        .userTimeZone(DateTimeZone.UTC)
        .ipAddress(IpAddress.of("***********"))
        .sessionKey("session-key")
        .build();

    injector.getInstance(QueryScope.class).cache(API_REQUEST_METADATA_KEY, Option.some(apiRequestMetadata));
    Key<Option<ApiRequestMetadata>> key = Key.get(new TypeLiteral<Option<ApiRequestMetadata>>() {});
    Provider<Option<ApiRequestMetadata>> provider = injector.getProvider(key);
    assertNotNull(provider);
    assertOptionEquals(apiRequestMetadata, provider.get());
  }

  @Test
  public void apiRequestMetadataOptionalIsNotBound() {
    Key<Option<ApiRequestMetadata>> key = Key.get(new TypeLiteral<Option<ApiRequestMetadata>>() {});
    Provider<Option<ApiRequestMetadata>> provider = injector.getProvider(key);
    assertNotNull(provider);
    assertOptionEmpty(provider.get());
  }

  @Test
  public void testIsSfeE2ETestUserProvider() {
    Provider<Boolean> provider = injector.getProvider(SFE_END2END_TEST_USER);
    assertFalse(provider.get());

    injector.getInstance(QueryScope.class).cache(SFE_END2END_TEST_USER, true);
    assertTrue(provider.get());
  }

  @Test
  public void testShouldReturnStubFakeResult() {
    Provider<Boolean> provider = injector.getProvider(RETURN_STUB_FAKE_RESULT);
    assertFalse(provider.get());

    injector.getInstance(QueryScope.class).cache(RETURN_STUB_FAKE_RESULT, true);
    assertTrue(provider.get());
  }

  @Test
  public void createJettyServerWithCorrectPort() {
    Server jettyServer = injector.getInstance(Server.class);
    assertNotNull(jettyServer.getBean(MBeanContainer.class));

    Connector[] connectors = jettyServer.getConnectors();
    assertEquals(1, connectors.length);
    assertEquals(12345, ((ServerConnector) connectors[0]).getPort());
    assertSame("Jetty Server must be a singleton", jettyServer, injector.getInstance(Server.class));
  }

  @Test
  public void withInstantiators() {
    Injector injector = createInjector(
        new QueryEngineModule(9000, RELAXED, OPTIONS, SessionlessQueryDriver.class),
        new OptionsModule(OPTIONS));
    assertEquals(InstantiatorBasedQueryJobImpl.class,
        injector.getInstance(
            Key.get(new TypeLiteral<Class<? extends QueryJob>>() {})));
    assertEquals(QP0P1Interpreter.class,
        injector.getInstance(RequestInterpreter.class).getClass());
  }

  @Test
  public void canMoveToInstantiators() {
    for (Class<?> query : BUILT_IN_QUERIES) {
      KachingInstantiators.createInstantiator(query, new DefaultJsonMarshallerFactory());
    }
  }

  @Test
  public void quartzSchedulerThreadCount() throws Exception {
    Scheduler scheduler = injector.getInstance(Scheduler.class);
    assertEquals(QueryEngineModule.QUARTZ_SCHEDULER_NUM_THREADS, scheduler.getMetaData().getThreadPoolSize());
  }

  @Test
  public void zkModuleInstalled() {
    assertNotNull(injector.getInstance(ZkClient.class));
  }

  @Test
  public void bindIcingaChecks() {
    Injector injector = createInjector(new AbstractModule() {
      @Override
      protected void configure() {
        QueryEngineModule.bindIcingaChecks(binder(), ImmutableSet.of(NopIcingaCheck.class));
      }
    });
    injector.getInstance(
        Key.get(TypeLiterals.get(Set.class, TypeLiterals.get(Class.class, Types.subtypeOf(IcingaCheck.class)))));
    injector.getInstance(Key.get(TypeLiterals.get(Set.class, IcingaCheck.class)));
  }

  @Test
  public void bindMonitoredSelfTests() {
    Injector injector = createInjector(new AbstractModule() {
      @Override
      protected void configure() {
        QueryEngineModule.bindMonitoredSelfTests(binder(), singleton(ExampleSelfTest.class));
      }
    });
    assertEquals(
        singleton(ExampleSelfTest.class),
        injector.getInstance(
            Key.get(new TypeLiteral<Set<Class<? extends Query<Errors>>>>() {}, Names.named("monitored"))));
  }

  @Test
  public void canInjectImpersonatorOutsideQueryScope() {
    strictInjector.getInstance(Impersonator.class).doAs(new UserId(123), getClass(), () -> {});
  }

  private static class ExampleSelfTest extends AbstractQuery<Errors> {

    @Override
    public Errors process() {
      return new Errors();
    }

  }

  static class HelloWorld extends AbstractQuery<String> {

    @Override
    public String process() {
      throw new UnsupportedOperationException();
    }
  }

}
