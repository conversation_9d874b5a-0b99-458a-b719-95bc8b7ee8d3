package com.kaching.platform.queryengine;

import static com.google.inject.Guice.createInjector;
import static com.kaching.platform.guice.TestingApplicationOptions.OPTIONS;
import static com.kaching.platform.queryengine.QueryScope.ScopeRules.STRICT;

import org.junit.Test;

import com.google.inject.Inject;
import com.google.inject.Injector;
import com.kaching.platform.guice.CommonModule;
import com.kaching.platform.guice.OptionsModule;
import com.kaching.platform.guice.TestingApplicationOptions;
import com.kaching.platform.queryengine.QueryClassOrInstance.QueryClass;
import com.wealthfront.test.AllowDNSResolution;

@AllowDNSResolution
public class SchedulingTest {

  @Test
  @SuppressWarnings("unchecked")
  public void queriesShouldGetCreatedUsingNewInstance() throws Exception {
    Injector injector = createInjector(
        new QueryEngineModule(9000, STRICT, OPTIONS, SimpleQueryDriver.class),
        new OptionsModule(OPTIONS),
        new CommonModule(new TestingApplicationOptions()));

    InstantiatorBasedQueryJobImpl queryJob = injector.getInstance(InstantiatorBasedQueryJobImpl.class);
    queryJob.setQueryClassOrInstance(new QueryClass(SomeRandomTransactionQuery.class));
    queryJob.execute();
  }

  public static class SomeRandomTransactionQuery extends AbstractQuery<String> {

    public SomeRandomTransactionQuery() {
    }

    @Inject
    public void set(QueryScopedObject client) {
    }

    public String process() {
      return "foo";
    }

  }

  @QueryScoped
  static class QueryScopedObject {
  }

}
