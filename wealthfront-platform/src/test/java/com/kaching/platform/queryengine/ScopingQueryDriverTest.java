package com.kaching.platform.queryengine;

import static com.kaching.platform.queryengine.QueryScope.EMPTY_CACHE;
import static com.kaching.platform.queryengine.QueryScope.ScopeRules.STRICT;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.sameInstance;
import static org.junit.Assert.fail;

import java.util.Map;

import org.jmock.Expectations;
import org.jmock.Mockery;
import org.junit.Before;
import org.junit.Test;

import com.google.inject.Key;
import com.google.inject.Provider;
import com.google.inject.util.Providers;

public class ScopingQueryDriverTest {

  private Mockery mocks;
  private ScopedQueryDriver delegate;
  private Query<Object> query;
  private String log = "";
  private final QueryScope scope = new QueryScope(STRICT) {
    @Override
    public void begin(Map<Key<?>, Object> relaxedCache) {
      log += "begin()";
    }

    @Override
    public void end() {
      log += "end()";
    }
  };

  @Before
  @SuppressWarnings("unchecked")
  public void setup() {
    mocks = new Mockery();
    delegate = mocks.mock(ScopedQueryDriver.class);
    query = mocks.mock(Query.class);
  }

  @Test
  public void execute() throws Exception {
    final Provider<? extends PostProcessor> processorProvider =
        Providers.of(new NullPostProcessor());
    mocks.checking(new Expectations() {{
      oneOf(delegate).execute(query, processorProvider);
    }});
    QueryDriver driver = new ScopingQueryDriver(delegate, scope);
    driver.execute(query, EMPTY_CACHE, processorProvider);
    mocks.assertIsSatisfied();
    assertThat("begin()end()", equalTo(log));
  }

  @Test
  public void executeThrowsExceptions() throws Exception {
    final RuntimeException exception = new RuntimeException();
    mocks.checking(new Expectations() {{
      oneOf(delegate).execute(query, null);
      will(throwException(exception));
    }});
    QueryDriver driver = new ScopingQueryDriver(delegate, scope);
    try {
      driver.execute(query, EMPTY_CACHE, null);
      fail();
    } catch (RuntimeException e) {
      assertThat(exception, sameInstance(exception));
    }
    mocks.assertIsSatisfied();
    assertThat("begin()end()", equalTo(log));
  }

}
