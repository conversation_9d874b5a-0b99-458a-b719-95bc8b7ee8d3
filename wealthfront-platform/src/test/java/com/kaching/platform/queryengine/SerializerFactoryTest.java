package com.kaching.platform.queryengine;

import static com.google.common.base.Charsets.UTF_8;
import static com.google.common.collect.Iterables.getOnlyElement;
import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Sets.newHashSet;
import static com.google.inject.Guice.createInjector;
import static com.kaching.DefaultKachingMarshallers.createMarshaller;
import static com.kaching.platform.SampleProto.SampleMessage.newBuilder;
import static com.twolattes.json.Json.array;
import static com.twolattes.json.Json.number;
import static com.twolattes.json.Json.object;
import static com.twolattes.json.Json.string;
import static com.wealthfront.test.Assert.assertBigDecimalEquals;
import static com.wealthfront.test.Assert.assertOptionEmpty;
import static com.wealthfront.test.Assert.assertOptionEquals;
import static com.wealthfront.test.Assert.assertSameJson;
import static com.wealthfront.test.Assert.assertThrows;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static java.math.BigDecimal.valueOf;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptySet;
import static java.util.Collections.singletonList;
import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UncheckedIOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.junit.Test;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.google.inject.TypeLiteral;
import com.kaching.platform.SampleProto.SampleMessage;
import com.kaching.platform.common.AbstractIdentifier;
import com.kaching.platform.common.AbstractLongIdentifier;
import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.AbstractHibernateEntity;
import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;
import com.twolattes.json.Entity;
import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;
import com.twolattes.json.Value;

public class SerializerFactoryTest {

  private static final SerializerFactory FACTORY = createInjector().getInstance(SerializerFactory.class);

  @Test
  public void testString() {
    assertHandledBySerializer(DefaultTypesSerializerProvider.class, String.class);
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<String>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<String>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<String>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<String>>() {});

    withSerializer(String.class, s -> {
      assertEquals("foo", s.serialize("foo"));
      assertEquals("null", s.serialize(null));
      assertEquals("", s.serialize(""));
      assertEquals("\"\"", s.serialize("\"\""));
      assertEquals("\"foo\"", s.serialize("\"foo\""));

      assertEquals("foo", s.deserialize("foo"));
      assertNull(s.deserialize("null"));
      assertEquals("", s.deserialize(""));
      assertEquals("\"\"", s.deserialize("\"\""));
      assertEquals("\"foo\"", s.deserialize("\"foo\""));
    });

    withSerializer(new TypeLiteral<List<String>>() {}, s -> {
      assertEquals("[\"foo\",\"bar\"]", s.serialize(newArrayList("foo", "bar")));
      assertEquals("[]", s.serialize(emptyList()));
      assertEquals(newArrayList("foo", "bar"), s.deserialize("[\"foo\",\"bar\"]"));
    });

    withSerializer(new TypeLiteral<Set<String>>() {}, s -> {
      TreeSet<String> set = Sets.newTreeSet(String.CASE_INSENSITIVE_ORDER);
      set.addAll(asList("foo", "bar"));
      assertEquals("[\"bar\",\"foo\"]", s.serialize(set));
      assertEquals("[]", s.serialize(emptySet()));

      assertEquals(newHashSet("foo", "bar"), s.deserialize("[\"foo\",\"bar\"]"));
    });

    withSerializer(new TypeLiteral<Map<String, String>>() {}, s -> {
      assertEquals("{\"foo\":\"bar\"}", s.serialize(ImmutableMap.of("foo", "bar")));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      assertEquals(ImmutableMap.of("foo", "bar"), s.deserialize("{\"foo\":\"bar\"}"));
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertThrows(NullPointerException.class, () -> s.deserialize("null"));
    });
  }

  @Test
  public void testByteArray() {
    assertHandledBySerializer(DefaultTypesSerializerProvider.class, byte[].class);
    assertHandledBySerializer(JacksonSerializerProvider.class, new TypeLiteral<Option<byte[]>>() {});
    assertHandledBySerializer(JacksonSerializerProvider.class, new TypeLiteral<List<byte[]>>() {});
    assertHandledBySerializer(JacksonSerializerProvider.class, new TypeLiteral<Set<byte[]>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<byte[]>>() {});

    withSerializer(byte[].class, s -> {
      assertEquals("abc", s.serialize(new byte[]{'a', 'b', 'c'}));

      assertArrayEquals(new byte[]{'a', 'b', 'c'}, s.deserialize("abc"));
    });

    withSerializer(new TypeLiteral<Option<byte[]>>() {}, s -> {
      assertEquals("\"YWJj\"", s.serialize(Option.of(new byte[]{'a', 'b', 'c'})));
      assertEquals("null", s.serialize(Option.none()));

      assertArrayEquals(new byte[]{'a', 'b', 'c'}, s.deserialize("\"YWJj\"").getOrThrow());
      assertOptionEmpty(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<List<byte[]>>() {}, s -> {
      assertEquals("[\"YWJj\"]", s.serialize(ImmutableList.of(new byte[]{'a', 'b', 'c'})));
      assertEquals("[]", s.serialize(ImmutableList.of()));

      List<byte[]> list = s.deserialize("[\"YWJj\"]");
      assertArrayEquals(new byte[]{'a', 'b', 'c'}, getOnlyElement(list));
      assertEquals(ImmutableList.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Set<byte[]>>() {}, s -> {
      assertEquals("[\"YWJj\"]", s.serialize(ImmutableSet.of(new byte[]{'a', 'b', 'c'})));
      assertEquals("[]", s.serialize(ImmutableSet.of()));

      Set<byte[]> set = s.deserialize("[\"YWJj\"]");
      assertArrayEquals(new byte[]{'a', 'b', 'c'}, getOnlyElement(set));
      assertEquals(ImmutableSet.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Map<String, byte[]>>() {}, s -> {
      assertEquals("{\"foo\":\"YWJj\"}", s.serialize(ImmutableMap.of("foo", new byte[]{'a', 'b', 'c'})));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      Map.Entry<String, byte[]> entry = getOnlyElement(s.deserialize("{\"foo\":\"YWJj\"}").entrySet());
      assertEquals("foo", entry.getKey());
      assertArrayEquals(new byte[]{'a', 'b', 'c'}, entry.getValue());
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertNull(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Map<byte[], String>>() {}, s -> {
      assertEquals("{\"YWJj\":\"foo\"}", s.serialize(ImmutableMap.of(new byte[]{'a', 'b', 'c'}, "foo")));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      Map.Entry<byte[], String> entry = getOnlyElement(s.deserialize("{\"YWJj\":\"foo\"}").entrySet());
      assertArrayEquals(new byte[]{'a', 'b', 'c'}, entry.getKey());
      assertEquals("foo", entry.getValue());
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertNull(s.deserialize("null"));
    });
  }

  @Test
  public void testByte() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, Byte.class);
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<Byte>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<Byte>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<Byte>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<Byte>>() {});

    withSerializer(Byte.class, s -> {
      assertEquals("127", s.serialize(Byte.MAX_VALUE));
      assertEquals("null", s.serialize(null));

      assertEquals(Byte.MAX_VALUE, s.deserialize("127").byteValue());
      assertEquals(Byte.MIN_VALUE, s.deserialize("128").byteValue());
      assertNull(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<Byte>>() {}, s -> {
      assertEquals("127", s.serialize(Option.of(Byte.MAX_VALUE)));
      assertEquals("null", s.serialize(Option.none()));

      assertOptionEquals(Byte.MAX_VALUE, s.deserialize("127"));
      assertOptionEmpty(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<List<Byte>>() {}, s -> {
      assertEquals("[127]", s.serialize(ImmutableList.of(Byte.MAX_VALUE)));
      assertEquals("[]", s.serialize(ImmutableList.of()));

      assertEquals(ImmutableList.of(Byte.MAX_VALUE), s.deserialize("[127]"));
      assertEquals(ImmutableList.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Set<Byte>>() {}, s -> {
      assertEquals("[127]", s.serialize(ImmutableSet.of(Byte.MAX_VALUE)));
      assertEquals("[]", s.serialize(ImmutableSet.of()));

      assertEquals(ImmutableSet.of(Byte.MAX_VALUE), s.deserialize("[127]"));
      assertEquals(ImmutableSet.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Map<Byte, Byte>>() {}, s -> {
      assertEquals("{\"-128\":127}", s.serialize(ImmutableMap.of(Byte.MIN_VALUE, Byte.MAX_VALUE)));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      assertEquals(ImmutableMap.of(Byte.MIN_VALUE, Byte.MAX_VALUE), s.deserialize("{\"-128\":127}"));
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertThrows(NullPointerException.class, () -> s.deserialize("null"));
    });
  }

  @Test
  public void testBoolean() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, Boolean.class);
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<Boolean>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<Boolean>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<Boolean>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<Boolean>>() {});

    withSerializer(Boolean.class, s -> {
      assertEquals("true", s.serialize(true));
      assertEquals("false", s.serialize(false));
      assertEquals("null", s.serialize(null));

      assertEquals(true, s.deserialize("true"));
      assertEquals(false, s.deserialize("false"));
      assertNull(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<Boolean>>() {}, s -> {
      assertEquals("true", s.serialize(Option.of(true)));
      assertEquals("null", s.serialize(Option.none()));

      assertOptionEquals(true, s.deserialize("true"));
      assertOptionEmpty(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<List<Boolean>>() {}, s -> {
      assertEquals("[true,true]", s.serialize(ImmutableList.of(true, true)));
      assertEquals("[]", s.serialize(ImmutableList.of()));

      assertEquals(ImmutableList.of(true, false), s.deserialize("[true, false]"));
      assertEquals(ImmutableList.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Set<Boolean>>() {}, s -> {
      assertEquals("[true]", s.serialize(ImmutableSet.of(true, true)));
      assertEquals("[]", s.serialize(ImmutableSet.of()));

      assertEquals(ImmutableSet.of(true), s.deserialize("[true, true]"));
      assertEquals(ImmutableSet.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Map<Boolean, Boolean>>() {}, s -> {
      assertEquals("{\"true\":false}", s.serialize(ImmutableMap.of(true, false)));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      assertEquals(ImmutableMap.of(true, false), s.deserialize("{\"true\":false}"));
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertThrows(NullPointerException.class, () -> s.deserialize("null"));
    });
  }

  @Test
  public void testBigDecimal() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, BigDecimal.class);
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<BigDecimal>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<BigDecimal>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<BigDecimal>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<BigDecimal>>() {});

    withSerializer(BigDecimal.class, s -> {
      assertEquals("0.1", s.serialize(BigDecimal.valueOf(0.1)));
      assertEquals("null", s.serialize(null));

      assertBigDecimalEquals(new BigDecimal("0.1"), s.deserialize("0.1"));
      assertNull(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<BigDecimal>>() {}, s -> {
      assertEquals("10", s.serialize(Option.of(BigDecimal.TEN)));
      assertEquals("null", s.serialize(Option.none()));

      assertBigDecimalEquals(BigDecimal.TEN, s.deserialize("10").getOrThrow());
      assertOptionEmpty(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<List<BigDecimal>>() {}, s -> {
      assertEquals("[10,10]", s.serialize(ImmutableList.of(BigDecimal.TEN, BigDecimal.TEN)));
      assertEquals("[]", s.serialize(ImmutableList.of()));

      assertEquals(ImmutableList.of(BigDecimal.TEN, BigDecimal.TEN), s.deserialize("[10,10]"));
      assertEquals(ImmutableList.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Set<BigDecimal>>() {}, s -> {
      assertEquals("[10]", s.serialize(ImmutableSet.of(BigDecimal.TEN, BigDecimal.TEN)));
      assertEquals("[]", s.serialize(ImmutableSet.of()));

      assertEquals(ImmutableSet.of(BigDecimal.TEN), s.deserialize("[10, 10]"));
      assertEquals(ImmutableSet.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Map<BigDecimal, BigDecimal>>() {}, s -> {
      assertEquals("{\"0\":1}", s.serialize(ImmutableMap.of(BigDecimal.ZERO, BigDecimal.ONE)));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      assertEquals(ImmutableMap.of(BigDecimal.ZERO, BigDecimal.ONE), s.deserialize("{\"0\":1}"));
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertThrows(NullPointerException.class, () -> s.deserialize("null"));
    });
  }

  @Test
  public void testHibernateId() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Id<TestHibernateEntity>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<Id<TestHibernateEntity>>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<Id<TestHibernateEntity>>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<Id<TestHibernateEntity>>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<Id<TestHibernateEntity>>>() {});

    withSerializer(new TypeLiteral<Id<TestHibernateEntity>>() {}, s -> {
      assertEquals("10", s.serialize(Id.of(10)));
      assertEquals("null", s.serialize(null));

      assertEquals(Id.<TestHibernateEntity>of(10), s.deserialize("10"));
      assertNull(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<Id<TestHibernateEntity>>>() {}, s -> {
      assertEquals("10", s.serialize(Option.of(Id.of(10))));
      assertEquals("null", s.serialize(Option.none()));

      assertOptionEquals(Id.of(10), s.deserialize("10"));
      assertOptionEmpty(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<List<Id<TestHibernateEntity>>>() {}, s -> {
      assertEquals("[10,10]", s.serialize(ImmutableList.of(Id.of(10), Id.of(10))));
      assertEquals("[]", s.serialize(ImmutableList.of()));

      assertEquals(ImmutableList.of(Id.of(10), Id.of(10)), s.deserialize("[10,10]"));
      assertEquals(ImmutableList.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Set<Id<TestHibernateEntity>>>() {}, s -> {
      assertEquals("[10]", s.serialize(ImmutableSet.of(Id.of(10), Id.of(10))));
      assertEquals("[]", s.serialize(ImmutableSet.of()));

      assertEquals(ImmutableSet.of(Id.of(10)), s.deserialize("[10, 10]"));
      assertEquals(ImmutableSet.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Map<Id<TestHibernateEntity>, Id<TestHibernateEntity>>>() {}, s -> {
      assertEquals("{\"1\":2}", s.serialize(ImmutableMap.of(Id.of(1), Id.of(2))));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      assertEquals(ImmutableMap.of(Id.of(1), Id.of(2)), s.deserialize("{\"1\":2}"));
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertThrows(NullPointerException.class, () -> s.deserialize("null"));
    });
  }

  @Test
  public void testAbstractIdentifier() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<LongId>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<LongId>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<LongId>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<LongId>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<LongId>>() {});

    withSerializer(LongId.class, s -> {
      assertEquals("10", s.serialize(new LongId(10L)));
      assertEquals("null", s.serialize(null));

      assertEquals(new LongId(10L), s.deserialize("10"));
      assertNull(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<LongId>>() {}, s -> {
      assertEquals("10", s.serialize(Option.of(new LongId(10L))));
      assertEquals("null", s.serialize(Option.none()));

      assertOptionEquals(new LongId(10L), s.deserialize("10"));
      assertOptionEmpty(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<List<LongId>>() {}, s -> {
      assertEquals("[10]", s.serialize(ImmutableList.of(new LongId(10L))));
      assertEquals("[]", s.serialize(ImmutableList.of()));

      assertEquals(ImmutableList.of(new LongId(10L)), s.deserialize("[10]"));
      assertEquals(ImmutableList.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Set<LongId>>() {}, s -> {
      assertEquals("[10]", s.serialize(ImmutableSet.of(new LongId(10L))));
      assertEquals("[]", s.serialize(ImmutableSet.of()));

      assertEquals(ImmutableSet.of(new LongId(10L)), s.deserialize("[10]"));
      assertEquals(ImmutableSet.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Map<LongId, LongId>>() {}, s -> {
      assertEquals("{\"1\":2}", s.serialize(ImmutableMap.of(new LongId(1), new LongId(2))));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      assertEquals(ImmutableMap.of(new LongId(1), new LongId(2)), s.deserialize("{\"1\":2}"));
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertThrows(NullPointerException.class, () -> s.deserialize("null"));
    });
  }

  @Test
  public void testPrimitiveAbstractIdentifier() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<PrimitiveLongId>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<PrimitiveLongId>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<PrimitiveLongId>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<PrimitiveLongId>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<PrimitiveLongId>>() {});

    withSerializer(PrimitiveLongId.class, s -> {
      assertEquals("10", s.serialize(new PrimitiveLongId(10L)));
      assertEquals("null", s.serialize(null));

      assertEquals(new PrimitiveLongId(10L), s.deserialize("10"));
      assertNull(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<PrimitiveLongId>>() {}, s -> {
      assertEquals("10", s.serialize(Option.of(new PrimitiveLongId(10L))));
      assertEquals("null", s.serialize(Option.none()));

      assertOptionEquals(new PrimitiveLongId(10L), s.deserialize("10"));
      assertOptionEmpty(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<List<PrimitiveLongId>>() {}, s -> {
      assertEquals("[10]", s.serialize(ImmutableList.of(new PrimitiveLongId(10L))));
      assertEquals("[]", s.serialize(ImmutableList.of()));

      assertEquals(ImmutableList.of(new PrimitiveLongId(10L)), s.deserialize("[10]"));
      assertEquals(ImmutableList.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Set<PrimitiveLongId>>() {}, s -> {
      assertEquals("[10]", s.serialize(ImmutableSet.of(new PrimitiveLongId(10L))));
      assertEquals("[]", s.serialize(ImmutableSet.of()));

      assertEquals(ImmutableSet.of(new PrimitiveLongId(10L)), s.deserialize("[10]"));
      assertEquals(ImmutableSet.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Map<PrimitiveLongId, PrimitiveLongId>>() {}, s -> {
      assertEquals("{\"1\":2}", s.serialize(ImmutableMap.of(new PrimitiveLongId(1), new PrimitiveLongId(2))));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      assertEquals(ImmutableMap.of(new PrimitiveLongId(1), new PrimitiveLongId(2)), s.deserialize("{\"1\":2}"));
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertThrows(NullPointerException.class, () -> s.deserialize("null"));
    });
  }

  @Test
  public void idAndAbstractIdEquivalentSerialization() {
    String idOutput = "10";
    withSerializer(new TypeLiteral<Id<TestHibernateEntity>>() {}, s -> assertEquals(idOutput, s.serialize(Id.of(10L))));
    withSerializer(new TypeLiteral<LongId>() {}, s -> assertEquals(idOutput, s.serialize(new LongId(10L))));
    withSerializer(new TypeLiteral<PrimitiveLongId>() {},
        s -> assertEquals(idOutput, s.serialize(new PrimitiveLongId(10L))));

    String someOutput = "10";
    withSerializer(new TypeLiteral<Option<Id<TestHibernateEntity>>>() {},
        s -> assertEquals(someOutput, s.serialize(Option.of(Id.of(10L)))));
    withSerializer(new TypeLiteral<Option<LongId>>() {},
        s -> assertEquals(someOutput, s.serialize(Option.of(new LongId(10L)))));
    withSerializer(new TypeLiteral<Option<PrimitiveLongId>>() {},
        s -> assertEquals(someOutput, s.serialize(Option.of(new PrimitiveLongId(10L)))));

    String noneOutput = "null";
    withSerializer(new TypeLiteral<Option<Id<TestHibernateEntity>>>() {},
        s -> assertEquals(noneOutput, s.serialize(Option.none())));
    withSerializer(new TypeLiteral<Option<LongId>>() {},
        s -> assertEquals(noneOutput, s.serialize(Option.none())));

    String listOutput = "[10]";
    withSerializer(new TypeLiteral<List<Id<TestHibernateEntity>>>() {},
        s -> assertEquals(listOutput, s.serialize(ImmutableList.of(Id.of(10L)))));
    withSerializer(new TypeLiteral<List<LongId>>() {},
        s -> assertEquals(listOutput, s.serialize(ImmutableList.of(new LongId(10L)))));
    withSerializer(new TypeLiteral<List<PrimitiveLongId>>() {},
        s -> assertEquals(listOutput, s.serialize(ImmutableList.of(new PrimitiveLongId(10L)))));

    String setOutput = "[10]";
    withSerializer(new TypeLiteral<Set<Id<TestHibernateEntity>>>() {},
        s -> assertEquals(setOutput, s.serialize(ImmutableSet.of(Id.of(10L)))));
    withSerializer(new TypeLiteral<Set<LongId>>() {},
        s -> assertEquals(setOutput, s.serialize(ImmutableSet.of(new LongId(10L)))));
    withSerializer(new TypeLiteral<Set<PrimitiveLongId>>() {},
        s -> assertEquals(setOutput, s.serialize(ImmutableSet.of(new PrimitiveLongId(10L)))));

    String mapOutput = "{\"10\":10}";
    withSerializer(new TypeLiteral<Map<Id<TestHibernateEntity>, Id<TestHibernateEntity>>>() {},
        s -> assertEquals(mapOutput, s.serialize(ImmutableMap.of(Id.of(10L), Id.of(10L)))));
    withSerializer(new TypeLiteral<Map<LongId, LongId>>() {},
        s -> assertEquals(mapOutput, s.serialize(ImmutableMap.of(new LongId(10L), new LongId(10L)))));
    withSerializer(new TypeLiteral<Map<PrimitiveLongId, PrimitiveLongId>>() {},
        s -> assertEquals(mapOutput, s.serialize(ImmutableMap.of(new PrimitiveLongId(10L), new PrimitiveLongId(10L)))));
  }

  @Test
  public void testJsonObject() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, Json.Object.class);
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<Json.Object>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<Json.Object>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<Json.Object>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<Json.Object>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Map<String, Json.Object>>() {});

    withSerializer(Json.Object.class, s -> {
      assertEquals("{\"foo\":3.14}", s.serialize(object(string("foo"), number(3.14))));
      assertEquals("null", s.serialize(Json.NULL));
      assertEquals("null", s.serialize(null));

      assertSameJson(object(string("foo"), string("bar")), s.deserialize("{\"foo\":\"bar\"}"));
      assertEquals(Json.NULL, s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<Json.Object>>() {}, s -> {
      assertEquals("{}", s.serialize(Option.some(Json.object())));
      assertEquals("null", s.serialize(Option.some(Json.NULL)));
      assertEquals("null", s.serialize(Option.none()));

      assertSameJson(Json.object(), s.deserialize("{}").getOrThrow());
      assertOptionEmpty(s.deserialize("null"));
    });
  }

  @Test
  public void testJsonArray() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, Json.Array.class);
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<Json.Array>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<Json.Array>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<Json.Array>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<Json.Array>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Map<String, Json.Array>>() {});

    withSerializer(Json.Array.class, s -> {
      assertEquals("[\"foo\",3.14,{\"bar\":false}]", s.serialize(
          array(string("foo"), number(3.14), object(string("bar"), Json.FALSE))));
      assertEquals("null", s.serialize(Json.NULL));
      assertEquals("null", s.serialize(null));

      assertEquals(array(string("foo"), Json.NULL, Json.TRUE), s.deserialize("[\"foo\",null,true]"));
      assertEquals(Json.NULL, s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<Json.Array>>() {}, s -> {
      assertEquals("[]", s.serialize(Option.some(Json.array())));
      assertEquals("null", s.serialize(Option.some(Json.NULL)));
      assertEquals("null", s.serialize(Option.none()));

      assertSameJson(Json.array(), s.deserialize("[]").getOrThrow());
      assertOptionEmpty(s.deserialize("null"));
    });
  }

  @Test
  public void testJsonString() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, Json.String.class);
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<Json.String>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<Json.String>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<Json.String>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<Json.String>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Map<String, Json.String>>() {});

    withSerializer(Json.String.class, s -> {
      assertEquals("\"hi\"", s.serialize(Json.string("hi")));
      assertEquals("null", s.serialize(Json.NULL));
      assertEquals("null", s.serialize(null));

      assertSameJson(Json.string("hi"), s.deserialize("\"hi\""));
      assertEquals(Json.NULL, s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<Json.String>>() {}, s -> {
      assertEquals("\"hi\"", s.serialize(Option.some(Json.string("hi"))));
      assertEquals("null", s.serialize(Option.some(Json.NULL)));
      assertEquals("null", s.serialize(Option.none()));

      assertSameJson(Json.string("hi"), s.deserialize("\"hi\"").getOrThrow());
      assertOptionEmpty(s.deserialize("null"));
    });
  }

  @Test
  public void testJsonBoolean() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, Json.Boolean.class);
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<Json.Boolean>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<Json.Boolean>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<Json.Boolean>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<Json.Boolean>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Map<String, Json.Boolean>>() {});

    withSerializer(Json.Boolean.class, s -> {
      assertEquals("true", s.serialize(Json.TRUE));
      assertEquals("null", s.serialize(Json.NULL));
      assertEquals("null", s.serialize(null));

      assertSameJson(Json.TRUE, s.deserialize("true"));
      assertEquals(Json.NULL, s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<Json.Boolean>>() {}, s -> {
      assertEquals("true", s.serialize(Option.some(Json.TRUE)));
      assertEquals("null", s.serialize(Option.some(Json.NULL)));
      assertEquals("null", s.serialize(Option.none()));

      assertSameJson(Json.TRUE, s.deserialize("true").getOrThrow());
      assertOptionEmpty(s.deserialize("null"));
    });
  }

  @Test
  public void testJsonNumber() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, Json.Number.class);
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<Json.Number>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<Json.Number>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<Json.Number>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<Json.Number>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Map<String, Json.Number>>() {});

    withSerializer(Json.Number.class, s -> {
      assertEquals("6", s.serialize(Json.number(6)));
      assertEquals("null", s.serialize(Json.NULL));
      assertEquals("null", s.serialize(null));

      assertSameJson(Json.TRUE, s.deserialize("true"));
      assertEquals(Json.NULL, s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<Json.Number>>() {}, s -> {
      assertEquals("5", s.serialize(Option.some(Json.number(5))));
      assertEquals("null", s.serialize(Option.some(Json.NULL)));
      assertEquals("null", s.serialize(Option.none()));

      assertSameJson(Json.number(6), s.deserialize("6").getOrThrow());
      assertOptionEmpty(s.deserialize("null"));
    });
  }

  @Test
  public void testJsonValue() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, Json.Value.class);
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<Json.Value>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<Json.Value>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<Json.Value>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<Json.Value>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Map<String, Json.Value>>() {});

    withSerializer(Json.Value.class, s -> {
      assertEquals("6", s.serialize(Json.number(6)));
      assertEquals("null", s.serialize(Json.NULL));
      assertEquals("null", s.serialize(null));

      assertSameJson(Json.TRUE, s.deserialize("true"));
      assertEquals(Json.NULL, s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<Json.Value>>() {}, s -> {
      assertEquals("5", s.serialize(Option.some(Json.number(5))));
      assertEquals("null", s.serialize(Option.none()));
      assertEquals("null", s.serialize(Option.some(Json.NULL)));

      assertSameJson(Json.number(6), s.deserialize("6").getOrThrow());
      assertOptionEmpty(s.deserialize("null"));
    });
  }

  @Test
  public void testJsonNull() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, Json.Null.class);
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<Json.Null>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<Json.Null>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<Json.Null>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<Json.Null>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Map<String, Json.Null>>() {});

    withSerializer(Json.Null.class, s -> {
      assertEquals("null", s.serialize(Json.NULL));
      assertEquals("null", s.serialize(null));

      assertEquals(Json.NULL, s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<Json.Null>>() {}, s -> {
      assertEquals("null", s.serialize(Option.some(Json.NULL)));
      assertEquals("null", s.serialize(Option.none()));

      assertOptionEmpty(s.deserialize("null"));
    });
  }

  @Test
  public void testInteger() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, Integer.class);
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<Integer>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<Integer>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<Integer>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<Integer>>() {});
    assertUnhandled(new TypeLiteral<SortedSet<Integer>>() {});

    withSerializer(Integer.class, s -> {
      assertEquals("**********", s.serialize(Integer.MAX_VALUE));
      assertEquals("null", s.serialize(null));

      assertEquals(Integer.MAX_VALUE, s.deserialize("**********").intValue());
      assertEquals(Integer.MIN_VALUE, s.deserialize("**********").intValue());
      assertNull(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<Integer>>() {}, s -> {
      assertEquals("**********", s.serialize(Option.of(Integer.MAX_VALUE)));
      assertEquals("null", s.serialize(Option.none()));

      assertOptionEquals(Integer.MAX_VALUE, s.deserialize("**********"));
      assertOptionEmpty(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<List<Integer>>() {}, s -> {
      assertEquals("[**********,**********]", s.serialize(ImmutableList.of(Integer.MAX_VALUE, Integer.MAX_VALUE)));
      assertEquals("[]", s.serialize(ImmutableList.of()));

      assertEquals(ImmutableList.of(Integer.MAX_VALUE, Integer.MAX_VALUE), s.deserialize("[**********,**********]"));
      assertEquals(ImmutableList.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Set<Integer>>() {}, s -> {
      assertEquals("[**********]", s.serialize(ImmutableSet.of(Integer.MAX_VALUE, Integer.MAX_VALUE)));
      assertEquals("[]", s.serialize(ImmutableSet.of()));

      assertEquals(ImmutableSet.of(Integer.MAX_VALUE), s.deserialize("[**********,**********]"));
      assertEquals(ImmutableSet.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Map<Integer, Integer>>() {}, s -> {
      assertEquals("{\"**********\":-**********}", s.serialize(ImmutableMap.of(Integer.MAX_VALUE, Integer.MIN_VALUE)));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      assertEquals(ImmutableMap.of(Integer.MAX_VALUE, Integer.MIN_VALUE),
          s.deserialize("{\"**********\":-**********}"));
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertThrows(NullPointerException.class, () -> s.deserialize("null"));
    });
  }

  @Test
  public void testTwoLattesEntity() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, TwoLattesEntity.class);
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<TwoLattesEntity>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<TwoLattesEntity>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<TwoLattesEntity>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<TwoLattesEntity>>() {});
    assertUnhandled(new TypeLiteral<ArrayList<TwoLattesEntity>>() {});
    assertUnhandled(new TypeLiteral<Collection<TwoLattesEntity>>() {});

    assertUnhandled(new TypeLiteral<List<NonEntity>>() {});
    assertUnhandled(new TypeLiteral<Set<NonEntity>>() {});
    assertUnhandled(new TypeLiteral<Option<NonEntity>>() {});
    assertUnhandled(new TypeLiteral<Map<String, NonEntity>>() {});

    withSerializer(TwoLattesEntity.class, s -> {
      assertEquals("{\"foo\":\"bar\"}", s.serialize(new TwoLattesEntity("bar")));
      assertEquals("null", s.serialize(null));

      assertEquals(new TwoLattesEntity("bar"), s.deserialize("{\"foo\":\"bar\"}"));
      assertNull(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<List<TwoLattesEntity>>() {}, s -> {
      assertEquals("[{\"foo\":\"bar\"}]", s.serialize(newArrayList(new TwoLattesEntity("bar"))));
      assertEquals("[]", s.serialize(newArrayList()));

      assertEquals(singletonList(new TwoLattesEntity("bar")), s.deserialize("[{\"foo\":\"bar\"}]"));
      assertEquals(ImmutableList.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Set<TwoLattesEntity>>() {}, s -> {
      assertEquals("[{\"foo\":\"bar\"}]", s.serialize(newHashSet(new TwoLattesEntity("bar"))));
      assertEquals("[]", s.serialize(newHashSet()));

      assertEquals(newHashSet(new TwoLattesEntity("bar")), s.deserialize("[{\"foo\":\"bar\"}]"));
      assertEquals(ImmutableSet.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Option<TwoLattesEntity>>() {}, s -> {
      assertEquals("{\"foo\":\"bar\"}", s.serialize(Option.some(new TwoLattesEntity("bar"))));
      assertEquals("null", s.serialize(Option.none()));
      assertEquals("null", s.serialize(null));

      assertOptionEquals(new TwoLattesEntity("bar"), s.deserialize("{\"foo\":\"bar\"}"));
      assertOptionEmpty(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Map<TwoLattesEntity, TwoLattesEntity>>() {}, s -> {
      assertEquals("{\"{\\\"foo\\\":\\\"baz\\\"}\":{\"foo\":\"quix\"}}",
          s.serialize(ImmutableMap.of(new TwoLattesEntity("baz"), new TwoLattesEntity("quix"))));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      assertEquals(ImmutableMap.of(new TwoLattesEntity("baz"), new TwoLattesEntity("quix")),
          s.deserialize("{\"{\\\"foo\\\":\\\"baz\\\"}\":{\"foo\":\"quix\"}}"));
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertThrows(NullPointerException.class, () -> s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Map<String, TwoLattesEntity>>() {}, s -> {
      assertEquals("{\"foo\":{\"foo\":\"baz\"}}", s.serialize(ImmutableMap.of("foo", new TwoLattesEntity("baz"))));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      assertEquals(ImmutableMap.of("foo", new TwoLattesEntity("baz")), s.deserialize("{\"foo\":{\"foo\":\"baz\"}}"));
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertThrows(NullPointerException.class, () -> s.deserialize("null"));
    });
  }

  @Test
  public void testJacksonEntity() {
    assertHandledBySerializer(JacksonSerializerProvider.class, JacksonEntity.class);
    assertHandledBySerializer(JacksonSerializerProvider.class, new TypeLiteral<Option<JacksonEntity>>() {});
    assertHandledBySerializer(JacksonSerializerProvider.class, new TypeLiteral<List<JacksonEntity>>() {});
    assertHandledBySerializer(JacksonSerializerProvider.class, new TypeLiteral<Set<JacksonEntity>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<JacksonEntity>>() {});
    assertUnhandled(new TypeLiteral<ArrayList<JacksonEntity>>() {});
    assertUnhandled(new TypeLiteral<Collection<JacksonEntity>>() {});

    withSerializer(JacksonEntity.class, s -> {
      assertEquals("{\"foo\":\"bar\"}", s.serialize(JacksonEntity.create("bar")));
      assertEquals("null", s.serialize(null));

      assertEquals(JacksonEntity.create("bar"), s.deserialize("{\"foo\":\"bar\"}"));
      assertNull(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<List<JacksonEntity>>() {}, s -> {
      assertEquals("[{\"foo\":\"bar\"}]", s.serialize(newArrayList(JacksonEntity.create("bar"))));
      assertEquals("[]", s.serialize(newArrayList()));

      assertEquals(singletonList(JacksonEntity.create("bar")), s.deserialize("[{\"foo\":\"bar\"}]"));
      assertEquals(ImmutableList.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Set<JacksonEntity>>() {}, s -> {
      assertEquals("[{\"foo\":\"bar\"}]", s.serialize(newHashSet(JacksonEntity.create("bar"))));
      assertEquals("[]", s.serialize(newHashSet()));

      assertEquals(newHashSet(JacksonEntity.create("bar")), s.deserialize("[{\"foo\":\"bar\"}]"));
      assertEquals(ImmutableSet.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Option<JacksonEntity>>() {}, s -> {
      assertEquals("{\"foo\":\"bar\"}", s.serialize(Option.some(JacksonEntity.create("bar"))));
      assertEquals("null", s.serialize(Option.none()));
      assertEquals("null", s.serialize(null));

      assertOptionEquals(JacksonEntity.create("bar"), s.deserialize("{\"foo\":\"bar\"}"));
      assertOptionEmpty(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Map<JacksonEntity, JacksonEntity>>() {}, s -> {
      JacksonEntity key = JacksonEntity.create("baz");
      assertEquals("{\"" + key + "\":{\"foo\":\"quix\"}}",
          s.serialize(ImmutableMap.of(key, JacksonEntity.create("quix"))));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      assertThrows(UncheckedIOException.class, () -> s.deserialize("{\"anykey\":{\"foo\":\"quix\"}}"));
      assertThrows(UncheckedIOException.class, () -> s.deserialize("{}"));
      assertThrows(UncheckedIOException.class, () -> s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Map<String, JacksonEntity>>() {}, s -> {
      assertEquals("{\"foo\":{\"foo\":\"baz\"}}", s.serialize(ImmutableMap.of("foo", JacksonEntity.create("baz"))));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      assertEquals(ImmutableMap.of("foo", JacksonEntity.create("baz")), s.deserialize("{\"foo\":{\"foo\":\"baz\"}}"));
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertNull(s.deserialize("null"));
    });
  }

  @Test
  public void testProtobuf() {
    assertHandledBySerializer(ProtobufSerializerProvider.class, SampleMessage.class);
    assertUnhandled(new TypeLiteral<Option<SampleMessage>>() {});
    assertHandledBySerializer(ProtobufSerializerProvider.class, new TypeLiteral<List<SampleMessage>>() {});
    assertHandledBySerializer(ProtobufSerializerProvider.class, new TypeLiteral<Set<SampleMessage>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<SampleMessage>>() {});
    assertUnhandled(new TypeLiteral<ArrayList<SampleMessage>>() {});
    assertUnhandled(new TypeLiteral<Collection<SampleMessage>>() {});
    assertUnhandled(new TypeLiteral<Map<String, SampleMessage>>() {});
    assertUnhandled(new TypeLiteral<Map<SampleMessage, String>>() {});

    withSerializer(SampleMessage.class, s -> {
      assertEquals("0a0568656c6c6f", s.serializeToHex(newBuilder().setContent("hello").build()));

      assertEquals("hello", s.deserializeFromHex("0a0568656c6c6f").getContent());
    });

    withSerializer(new TypeLiteral<List<SampleMessage>>() {}, s -> {
      assertEquals("070a0568656c6c6f070a05776f726c64", s.serializeToHex(Stream.of("hello", "world")
          .map(c -> newBuilder().setContent(c).build())
          .collect(Collectors.toList())));

      assertEquals(ImmutableList.of("hello", "world"), s.deserializeFromHex("070a0568656c6c6f070a05776f726c64")
          .stream()
          .map(SampleMessage::getContent)
          .collect(Collectors.toList()));
    });

    withSerializer(new TypeLiteral<Set<SampleMessage>>() {}, s -> {
      byte[] bs = s.serializeToBytes(Stream.of("hello", "world")
          .map(c -> newBuilder().setContent(c).build())
          .collect(Collectors.toSet()));

      assertEquals(ImmutableSet.of("hello", "world"), s.deserializeFromBytes(bs)
          .stream()
          .map(SampleMessage::getContent)
          .collect(Collectors.toSet()));

      assertEquals(ImmutableSet.of("hello", "world"), s.deserializeFromHex("070a0568656c6c6f070a05776f726c64")
          .stream()
          .map(SampleMessage::getContent)
          .collect(Collectors.toSet()));
    });
  }

  @Test
  public void deserializeProtobufTwice() {
    SampleMessage message = newBuilder().setContent("hello").build();
    Serializer<SampleMessage> serializer = getOrThrow(TypeLiteral.get(SampleMessage.class));

    serializer.deserialize(message.toByteArray());
    serializer.deserialize(message.toByteArray());
  }

  @Test
  public void testDateTime() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, DateTime.class);
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<DateTime>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<DateTime>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<DateTime>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<DateTime>>() {});

    DateTime date = new DateTime(2004, 10, 1, 0, 0, 0, 0, ET);
    DateTime dateTime = new DateTime(2004, 10, 1, 1, 3, 5, 7, ET);

    withSerializer(DateTime.class, s -> {
      assertEquals("\"2004-10-01\"", s.serialize(date));
      assertEquals("\"2004-10-01 01:03:05.007\"", s.serialize(dateTime));
      assertEquals("null", s.serialize(null));

      assertEquals(date, s.deserialize("\"2004-10-01\""));
      assertEquals(date, s.deserialize("\"2004-10-01 00:00:00.000\""));
      assertEquals(dateTime, s.deserialize("\"2004-10-01 01:03:05.007\""));
      assertNull(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<DateTime>>() {}, s -> {
      assertEquals("\"2004-10-01\"", s.serialize(Option.some(date)));
      assertEquals("null", s.serialize(Option.none()));
      assertEquals("null", s.serialize(null));

      assertOptionEquals(date, s.deserialize("\"2004-10-01\""));
      assertOptionEmpty(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<List<DateTime>>() {}, s -> {
      assertEquals("[\"2004-10-01\",\"2004-10-01\"]", s.serialize(ImmutableList.of(date, date)));
      assertEquals("[]", s.serialize(ImmutableList.of()));

      assertEquals(ImmutableList.of(date, date), s.deserialize("[\"2004-10-01\",\"2004-10-01\"]"));
      assertEquals(ImmutableList.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Set<DateTime>>() {}, s -> {
      assertEquals("[\"2004-10-01\"]", s.serialize(ImmutableSet.of(date, date)));
      assertEquals("[]", s.serialize(ImmutableSet.of()));

      assertEquals(ImmutableSet.of(date), s.deserialize("[\"2004-10-01\",\"2004-10-01\"]"));
      assertEquals(ImmutableSet.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Map<DateTime, DateTime>>() {}, s -> {
      assertEquals("{\"2004-10-01\":\"2004-10-01\"}", s.serialize(ImmutableMap.of(date, date)));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      assertEquals(ImmutableMap.of(date, date), s.deserialize("{\"2004-10-01\":\"2004-10-01\"}"));
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertThrows(NullPointerException.class, () -> s.deserialize("null"));
    });
  }

  @Test
  public void testEnum() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, FooEnum.class);
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Option<FooEnum>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<List<FooEnum>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Set<FooEnum>>() {});
    assertHandledBySerializer(SerializerFactory.class, new TypeLiteral<StreamingInput<FooEnum>>() {});

    withSerializer(FooEnum.class, s -> {
      assertEquals("\"BAZ\"", s.serialize(FooEnum.BAZ));
      assertEquals("null", s.serialize(null));

      assertEquals(FooEnum.BAR, s.deserialize("\"BAR\""));
      assertNull(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<Option<FooEnum>>() {}, s -> {
      assertEquals("\"BAZ\"", s.serialize(Option.some(FooEnum.BAZ)));
      assertEquals("null", s.serialize(Option.none()));
      assertEquals("null", s.serialize(null));

      assertOptionEquals(FooEnum.BAZ, s.deserialize("\"BAZ\""));
      assertOptionEmpty(s.deserialize("null"));
    });

    withSerializer(new TypeLiteral<List<FooEnum>>() {}, s -> {
      assertEquals("[\"BAR\",\"BAZ\"]", s.serialize(ImmutableList.of(FooEnum.BAR, FooEnum.BAZ)));
      assertEquals("[]", s.serialize(ImmutableList.of()));

      assertEquals(ImmutableList.of(FooEnum.BAR, FooEnum.BAZ), s.deserialize("[\"BAR\",\"BAZ\"]"));
      assertEquals(ImmutableList.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Set<FooEnum>>() {}, s -> {
      assertEquals("[\"BAZ\"]", s.serialize(ImmutableSet.of(FooEnum.BAZ, FooEnum.BAZ)));
      assertEquals("[]", s.serialize(ImmutableSet.of()));

      assertEquals(ImmutableSet.of(FooEnum.BAZ), s.deserialize("[\"BAZ\",\"BAZ\"]"));
      assertEquals(ImmutableSet.of(), s.deserialize("[]"));
    });

    withSerializer(new TypeLiteral<Map<FooEnum, FooEnum>>() {}, s -> {
      assertEquals("{\"FOO\":\"BAR\"}", s.serialize(ImmutableMap.of(FooEnum.FOO, FooEnum.BAR)));
      assertEquals("{}", s.serialize(ImmutableMap.of()));

      assertEquals(ImmutableMap.of(FooEnum.FOO, FooEnum.BAR), s.deserialize("{\"FOO\":\"BAR\"}"));
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertThrows(NullPointerException.class, () -> s.deserialize("null"));
    });
  }

  @Test
  public void testStreamingInput_String() {
    withSerializer(new TypeLiteral<StreamingInput<String>>() {}, s -> {
      assertEquals("foo", s.serialize(() -> "foo"));
      assertEquals("foo", s.deserialize("foo").get());
    });
  }

  @Test
  public void testStreamingInputJsonArray() {
    withSerializer(new TypeLiteral<StreamingInput<Json.Array>>() {}, s -> {
      List<String> items = ImmutableList.of("hello", "world");
      Json.Array json = Json.array(items.stream().map(Json::string).collect(Collectors.toList()));
      assertEquals("[\"hello\",\"world\"]", s.serialize(() -> json));
      assertSameJson(json, s.deserialize("[\"hello\",\"world\"]").get());
    });
  }

  @Test
  public void testStreamingOutputJsonArray() {
    List<String> items = ImmutableList.of("hello", "world");
    String output = ((Supplier<String>) () -> {
      ByteArrayOutputStream baos = new ByteArrayOutputStream();
      Marshaller<String> marshaller = createMarshaller(String.class);
      try {
        new StreamingOutput<Json.Array>() {
          @Override
          public void write(OutputStream os) throws IOException {
            os.write(items.stream()
                .map(i -> marshaller.marshall(i).toString())
                .collect(Collectors.joining(",", "[", "]")).getBytes(UTF_8));
          }
        }.write(baos);
        return new String(baos.toByteArray(), UTF_8);
      } catch (IOException e) {
        throw new RuntimeException(e);
      }
    }).get();
    assertEquals("[\"hello\",\"world\"]", output);

    withSerializer(new TypeLiteral<StreamingInput<Json.Array>>() {}, s -> {
      Json.Array json = Json.array(items.stream().map(Json::string).collect(Collectors.toList()));
      assertSameJson(json, s.deserialize("[\"hello\",\"world\"]").get());
    });
  }

  @Test
  public void deserializeStreamingInputListTwoLattesEntity() {
    List<TwoLattesEntity> items = ImmutableList.of(new TwoLattesEntity("one"), new TwoLattesEntity("two"));
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    try {
      new StreamingJsonOutput<TwoLattesEntity>(createMarshaller(TwoLattesEntity.class)) {
        @Override
        public void generate(JsonStreamer<TwoLattesEntity> streamer) throws IOException {
          for (TwoLattesEntity item : items) {
            streamer.stream(item);
          }
        }
      }.write(baos);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    String output = new String(baos.toByteArray(), UTF_8);
    assertEquals("[{\"foo\":\"one\"},{\"foo\":\"two\"}]", output);

    withSerializer(new TypeLiteral<StreamingInput<List<TwoLattesEntity>>>() {}, s -> {
      assertEquals(items, s.deserialize("[{\"foo\":\"one\"},{\"foo\":\"two\"}]").get());
    });
  }

  @Test
  public void testMap() {
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Map<String, Integer>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Map<LocalDate, BigDecimal>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class,
        new TypeLiteral<Map<Id<TestHibernateEntity>, Integer>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Map<String, BigDecimal>>() {});
    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Map<Integer, BigDecimal>>() {});

    assertHandledBySerializer(TwoLattesSerializerProvider.class, new TypeLiteral<Map<String, String>>() {});
    assertHandledBySerializer(JacksonSerializerProvider.class,
        new TypeLiteral<Map<Id<TestHibernateEntity>, List<Id<TestHibernateEntity>>>>() {});
    assertUnhandled(new TypeLiteral<Map<String, NonEntity>>() {});
    assertUnhandled(new TypeLiteral<Map<NonEntity, String>>() {});
    assertUnhandled(new TypeLiteral<Map<NonEntity, NonEntity>>() {});
    assertUnhandled(new TypeLiteral<Map.Entry<String, String>>() {});

    withSerializer(new TypeLiteral<Map<String, Integer>>() {}, s -> {
      assertEquals(ImmutableMap.of("foo", 10), s.deserialize("{\"foo\":10}"));

      assertEquals("{\"foo\":10}", s.serialize(ImmutableMap.of("foo", 10)));
    });

    withSerializer(new TypeLiteral<Map<LocalDate, BigDecimal>>() {}, s -> {
      assertEquals("{\"20110325\":10}", s.serialize(ImmutableMap.of(new LocalDate(2011, 3, 25), valueOf(10))));

      assertEquals(ImmutableMap.of(new LocalDate(2011, 3, 25), valueOf(10)), s.deserialize("{\"20110325\":10}"));
    });

    withSerializer(new TypeLiteral<Map<Id<TestHibernateEntity>, Integer>>() {}, s -> {
      assertEquals("{\"1\":10}", s.serialize(ImmutableMap.of(Id.of(1), 10)));

      assertEquals(ImmutableMap.of(Id.<TestHibernateEntity>of(1), 10), s.deserialize("{\"1\":10}"));
    });

    withSerializer(new TypeLiteral<Map<Id<TestHibernateEntity>, List<Id<TestHibernateEntity>>>>() {}, s -> {
      Map<Id<TestHibernateEntity>, List<Id<TestHibernateEntity>>> map =
          ImmutableMap.of(Id.of(1), ImmutableList.of(Id.of(2), Id.of(3)));
      assertEquals("{\"1\":[2,3]}", s.serialize(map));
      assertEquals("null", s.serialize(null));

      assertEquals(map, s.deserialize("{\"1\":[2,3]}"));
      assertEquals(ImmutableMap.of(), s.deserialize("{}"));
      assertNull(s.deserialize("null"));
    });
  }

  @Test
  public void canSerialize() {
    assertTrue(getSerializerFactory().canSerialize(new TypeLiteral<Integer>() {}));
  }

  @Test
  public void cannotSerialize() {
    assertFalse(getSerializerFactory().canSerialize(new TypeLiteral<SerializerFactoryTest>() {}));
  }

  @Test
  public void getThrows() {
    try {
      getOrThrow(new TypeLiteral<SerializerFactoryTest>() {});
      fail();
    } catch (IllegalArgumentException e) {
      assertTrue(
          e.getMessage().contains("cannot serialize"));
    }
  }

  private <T> void withSerializer(Class<T> klass, Consumer<SerializerTestHelper<T>> cb) {
    withSerializer(TypeLiteral.get(klass), cb);
  }

  private <T> void withSerializer(TypeLiteral<T> type, Consumer<SerializerTestHelper<T>> cb) {
    cb.accept(new SerializerTestHelper<>(getOrThrow(type)));
  }

  private static SerializerFactory getSerializerFactory() {
    return FACTORY;
  }

  private static <T> Serializer<T> getOrThrow(TypeLiteral<T> type) {
    return getSerializerFactory().getOrThrow(type);
  }

  private static void assertHandledBySerializer(Class<?> providerClass, Class<?> type) {
    assertHandledBySerializer(providerClass, TypeLiteral.get(type));
  }

  private static void assertHandledBySerializer(Class<?> providerClass, TypeLiteral<?> type) {
    Class<? extends Serializer> serializerClass = getOrThrow(type).getClass();
    if (providerClass != serializerClass) {
      assertEquals(providerClass, serializerClass.getEnclosingClass());
    }
  }

  private static void assertUnhandled(TypeLiteral<?> type) {
    try {
      getOrThrow(type);
      fail("expected " + type + " to fail without an available serializer.");
    } catch (IllegalArgumentException ignored) {

    }
  }

  public static class TestHibernateEntity extends AbstractHibernateEntity {

    @Override
    public Id<? extends HibernateEntity> getId() {
      throw new UnsupportedOperationException();
    }

  }

  public static class LongId extends AbstractIdentifier<Long> {

    protected LongId(long id) {
      super(id);
    }

  }

  public static class PrimitiveLongId extends AbstractLongIdentifier {

    protected PrimitiveLongId(long id) {
      super(id);
    }

  }

  public static class StringId extends AbstractIdentifier<String> {

    protected StringId(String id) {
      super(id);
    }

  }

  @Entity
  public static class TwoLattesEntity {

    @Value String foo;

    /* JSON */
    @SuppressWarnings("unused")
    public TwoLattesEntity() {}

    public TwoLattesEntity(String foo) {
      this.foo = foo;
    }

    @Override
    public int hashCode() {
      final int prime = 31;
      int result = 1;
      result = prime * result + ((foo == null) ? 0 : foo.hashCode());
      return result;
    }

    @Override
    public boolean equals(Object obj) {
      if (this == obj) {
        return true;
      }
      if (obj == null) {
        return false;
      }
      if (getClass() != obj.getClass()) {
        return false;
      }
      TwoLattesEntity other = (TwoLattesEntity) obj;
      if (foo == null) {
        if (other.foo != null) {
          return false;
        }
      } else if (!foo.equals(other.foo)) {
        return false;
      }
      return true;
    }

  }

  public static class JacksonEntity {

    @JsonProperty
    String foo;

    JacksonEntity() {/* json */}

    public JacksonEntity with(String foo) {
      this.foo = foo;
      return this;
    }

    // Using this method to avoid using a default constructor that Jackson can find with reflection.
    public static JacksonEntity create(String foo) {
      return new JacksonEntity().with(foo);
    }

    @Override
    public int hashCode() {
      return Objects.hashCode(foo);
    }

    @Override
    public boolean equals(Object obj) {
      if (this == obj) {
        return true;
      }
      if (obj == null) {
        return false;
      }
      if (getClass() != obj.getClass()) {
        return false;
      }
      JacksonEntity other = (JacksonEntity) obj;
      if (foo == null) {
        return other.foo == null;
      } else {
        return foo.equals(other.foo);
      }
    }

  }

  public static class NonEntity {}

  public enum FooEnum {
    FOO, BAR, BAZ
  }

}
