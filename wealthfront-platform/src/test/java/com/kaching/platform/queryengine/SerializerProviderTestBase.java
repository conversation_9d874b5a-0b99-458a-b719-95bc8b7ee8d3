package com.kaching.platform.queryengine;

import static java.nio.charset.StandardCharsets.UTF_8;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.util.function.Consumer;

import com.google.common.io.BaseEncoding;
import com.google.inject.TypeLiteral;

public abstract class SerializerProviderTestBase {

  public abstract SerializerProvider getProvider();

  @SuppressWarnings("unchecked")
  protected <T> Serializer<T> getSerializer(TypeLiteral<T> type) {
    return getProvider().getSerializer(type)
        .getOrThrow(new RuntimeException("Could not find serializer for: " + type));
  }

  protected <T> Serializer<T> getSerializer(Class<T> type) {
    return getSerializer(TypeLiteral.get(type));
  }

  protected <T> T roundtripHex(T object, String hex, TypeLiteral<T> type) {
    byte[] bs = serializeToBytes(object, type);
    assertEquals(hex, BaseEncoding.base16().encode(bs));
    return deserialize(bs, type);
  }

  protected <T> T roundtripHex(T object, String hex, Class<T> type) {
    return roundtripHex(object, hex, TypeLiteral.get(type));
  }

  protected <T> T roundtrip(T object, String toString, TypeLiteral<T> type) {
    byte[] bs = serializeToBytes(object, type);
    assertEquals(toString, new String(bs, UTF_8));
    return deserialize(bs, type);
  }

  protected <T> T roundtrip(T object, String toString, Class<T> type) {
    return roundtrip(object, toString, TypeLiteral.get(type));
  }

  protected <T> byte[] serializeToBytes(T object, TypeLiteral<T> returnType) {
    ClearingStreamHolder clearingStreamHolder = new ClearingStreamHolder();
    getSerializer(returnType).serialize(object, clearingStreamHolder);
    return clearingStreamHolder.getContent();
  }

  protected <T> byte[] serializeToBytes(T object, Class<T> returnType) {
    return serializeToBytes(object, TypeLiteral.get(returnType));
  }

  protected <T> String serializeToString(T object, TypeLiteral<T> returnType) {
    return new String(serializeToBytes(object, returnType));
  }

  protected <T> String serializeToString(T object, Class<T> returnType) {
    return serializeToString(object, TypeLiteral.get(returnType));
  }

  protected <T> String serializeToHex(T object, TypeLiteral<T> returnType) {
    return BaseEncoding.base16().encode(serializeToBytes(object, returnType));
  }

  protected <T> String serializeToHex(T object, Class<T> returnType) {
    return serializeToHex(object, TypeLiteral.get(returnType));
  }

  protected <T> T deserialize(byte[] response, TypeLiteral<T> returnType) {
    return getSerializer(returnType).deserialize(response);
  }

  protected <T> T deserialize(byte[] response, Class<T> returnType) {
    return deserialize(response, TypeLiteral.get(returnType));
  }

  protected <T> T deserialize(String response, TypeLiteral<T> returnType) {
    return deserialize(response.getBytes(), returnType);
  }

  protected <T> T deserialize(String response, Class<T> returnType) {
    return deserialize(response, TypeLiteral.get(returnType));
  }

  protected <T> T deserializeHex(String response, TypeLiteral<T> returnType) {
    return deserialize(BaseEncoding.base16().decode(response), returnType);
  }

  protected <T> T deserializeHex(String response, Class<T> returnType) {
    return deserialize(response, TypeLiteral.get(returnType));
  }

  protected boolean isSerializable(Class<?> type) {
    return isSerializable(TypeLiteral.get(type));
  }

  protected boolean isSerializable(TypeLiteral<?> type) {
    return getProvider().getSerializer(type).isDefined();
  }

  protected void assertIsSerializable(Class<?> type) {
    assertIsSerializable(TypeLiteral.get(type));
  }

  protected void assertIsSerializable(TypeLiteral<?> type) {
    assertTrue(type.toString() + " is expected to be serializable.", isSerializable(type));
  }

  protected void assertIsNotSerializable(Class<?> type) {
    assertIsNotSerializable(TypeLiteral.get(type));
  }

  protected void assertIsNotSerializable(TypeLiteral<?> type) {
    assertTrue(type.toString() + " is expected to NOT be serializable.", !isSerializable(type));
  }

  protected void assertUnhandled(Class<?> klass) {
    assertUnhandled(TypeLiteral.get(klass));
  }

  protected void assertUnhandled(TypeLiteral<?> type) {
    try {
      getProvider().getSerializer(type).getOrThrow();
      fail("expected " + type + " to fail without an available serializer.");
    } catch (IllegalArgumentException ignored) {

    }
  }

  protected <T> void withSerializer(Class<T> klass, Consumer<SerializerTestHelper<T>> cb) {
    withSerializer(TypeLiteral.get(klass), cb);
  }

  @SuppressWarnings("unchecked")
  protected <T> void withSerializer(TypeLiteral<T> type, Consumer<SerializerTestHelper<T>> cb) {
    Serializer<T> serializer = getProvider().getSerializer(type).getOrThrow();
    cb.accept(new SerializerTestHelper<>(serializer));
  }

}
