package com.kaching.platform.queryengine;

import static com.google.inject.Guice.createInjector;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

import com.amazonaws.xray.AWSXRayRecorder;
import com.google.inject.Key;
import com.google.inject.TypeLiteral;
import com.kaching.platform.common.Option;
import com.kaching.platform.queryengine.xray.NoOpXrayRecorder;
import com.kaching.platform.queryengine.xray.XrayRecorder;
import com.kaching.platform.queryengine.xray.XrayRecorderImpl;
import com.kaching.platform.queryengine.xray.XrayTracingQueryEngineModule;

public class XrayTracingQueryEngineModuleTest {

  @Test
  public void configure() {
    assertFalse(createInjector()
        .getInstance(XrayTracingQueryEngineModule.XrayTracingFeature.class)
        .isTracingEnabled());

    assertTrue(createInjector(new XrayTracingQueryEngineModule(true))
        .getInstance(XrayTracingQueryEngineModule.XrayTracingFeature.class)
        .isTracingEnabled());

    assertTrue(createInjector().getInstance(XrayRecorder.class) instanceof NoOpXrayRecorder);
    assertTrue(createInjector(new XrayTracingQueryEngineModule(true))
        .getInstance(XrayRecorder.class) instanceof XrayRecorderImpl);
  }

  @Test
  public void provideXrayRecorder_noop() {
    XrayTracingQueryEngineModule nonTracingModule = new XrayTracingQueryEngineModule(false);
    assertTrue(createInjector(nonTracingModule)
        .getInstance(XrayRecorder.class) instanceof NoOpXrayRecorder);

    XrayTracingQueryEngineModule tracingEnabledModule = new XrayTracingQueryEngineModule(true);
    assertTrue(tracingEnabledModule.provideXrayRecorder(Option.none()) instanceof NoOpXrayRecorder);
  }

  @Test
  public void configure_alwaysBindsAwsXrayRecorder() {
    XrayTracingQueryEngineModule nonTracingModule = new XrayTracingQueryEngineModule(false);
    createInjector(nonTracingModule).getInstance(Key.get(new TypeLiteral<Option<AWSXRayRecorder>>() {}));
  }

}
