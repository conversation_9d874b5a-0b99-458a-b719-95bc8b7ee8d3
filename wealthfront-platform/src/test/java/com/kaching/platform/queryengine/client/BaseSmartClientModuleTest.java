package com.kaching.platform.queryengine.client;

import static com.google.inject.Guice.createInjector;
import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertSame;

import java.util.Timer;

import org.jmock.Mockery;
import org.junit.Before;
import org.junit.Test;

import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;
import com.kaching.platform.discovery.Resolver;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.guice.KachingServices.CBUS;
import com.kaching.platform.guice.KachingServices.FBANK;
import com.kaching.platform.guice.KachingServices.HTF;
import com.kaching.platform.guice.KachingServices.NOP;
import com.kaching.platform.testing.Mockeries;
import com.kaching.util.http.HttpClient;
import com.kaching.util.http.LongOnlineTimeoutPooling;
import com.kaching.util.http.LongTimeoutPooling;

public class BaseSmartClientModuleTest {

  private final Mockery mock = Mockeries.mockery();
  private final TestSmartClientModule module = new TestSmartClientModule();

  @Inject @LongTimeoutPooling SmartClient<NOP> nopClient;
  @Inject @LongTimeoutPooling SmartClient<NOP> nopClient2;
  @Inject @LongTimeoutPooling RetryingSmartClient<NOP> nopClientRetrying;
  @Inject @LongOnlineTimeoutPooling SmartClient<FBANK> fbankClient;
  @Inject @LongOnlineTimeoutPooling RetryingSmartClient<FBANK> fbankClientRetrying;
  @Inject SmartClient<HTF> htfClient;
  @Inject @LongTimeoutPooling SmartClient<HTF> htfClient2;
  @Inject SmartClient<CBUS> cbusClient;
  @Inject @LongOnlineTimeoutPooling SmartClient<CBUS> cbusClient2;

  @Before
  public void setup() {
    Injector injector = createInjector(module);
    injector.injectMembers(this);
  }

  @Test
  public void bindSmartClientToAnnotation() {
    assertSame(
        ((SmartHttpClient) nopClient).getExecutorService(),
        ((SmartHttpClient) nopClientRetrying).getExecutorService());
    assertSame(
        ((SmartHttpClient) nopClient).getExecutorService(),
        ((SmartHttpClient) nopClient2).getExecutorService());
    assertSame(
        ((SmartHttpClient) fbankClient).getExecutorService(),
        ((SmartHttpClient) fbankClientRetrying).getExecutorService());
  }

  @Test
  public void bindDefaultSmartClient() {
    assertSame(
        ((SmartHttpClient) htfClient).getExecutorService(),
        ((SmartHttpClient) htfClient2).getExecutorService());
    assertSame(
        ((SmartHttpClient) cbusClient).getExecutorService(),
        ((SmartHttpClient) cbusClient2).getExecutorService());
  }

  @Test
  public void bindSmartClientToKindAndAnnotation_invalidMapping_throws() {
    assertThrows(
        IllegalStateException.class,
        "Cross service binding is only allowed from a batch service to its online counterpart.",
        () -> module.bindSmartClientToKindAndAnnotation(
            KachingServices.BLINK.class, KachingServices.BI.class, LongOnlineTimeoutPooling.class));

    assertThrows(
        IllegalStateException.class,
        "Cross service binding is only allowed from a batch service to its online counterpart.",
        () -> module.bindSmartClientToKindAndAnnotation(
            KachingServices.UM.class, KachingServices.BUM.class, LongOnlineTimeoutPooling.class));
  }

  private class TestSmartClientModule extends BaseSmartClientModule {

    @Override @SuppressWarnings("unchecked")
    protected void configure() {
      SMART_CLIENT_ANNOTATIONS.forEach(a ->
          bind(HttpClient.class).annotatedWith(a).toInstance(mock.mock(HttpClient.class, a.getSimpleName())));

      SMART_CLIENT_ANNOTATIONS.forEach(a -> bindSmartClientToAnnotation(NOP.class, a));
      SMART_CLIENT_ANNOTATIONS.forEach(a -> bindSmartClientToAnnotation(FBANK.class, a));
      SMART_CLIENT_ANNOTATIONS.forEach(a -> bindSmartClientToAnnotation(HTF.class, a));
      SMART_CLIENT_ANNOTATIONS.forEach(a -> bindSmartClientToAnnotation(CBUS.class, a));
      bind(new TypeLiteral<Resolver<FBANK>>() {}).toInstance(mock.mock(Resolver.class, "FBANK"));
      bind(new TypeLiteral<Resolver<NOP>>() {}).toInstance(mock.mock(Resolver.class, "NOP"));

      bindDefaultSmartClient(HTF.class, LongTimeoutPooling.class);
      bindDefaultSmartClient(CBUS.class, LongOnlineTimeoutPooling.class);
      bind(new TypeLiteral<Resolver<HTF>>() {}).toInstance(mock.mock(Resolver.class, "HTF"));
      bind(new TypeLiteral<Resolver<CBUS>>() {}).toInstance(mock.mock(Resolver.class, "CBUS"));
      bind(Timer.class)
          .annotatedWith(Names.named("daemon"))
          .toInstance(new Timer(true));
    }

  }

}