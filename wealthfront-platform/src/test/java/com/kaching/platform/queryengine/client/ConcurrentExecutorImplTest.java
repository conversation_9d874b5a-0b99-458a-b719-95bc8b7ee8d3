package com.kaching.platform.queryengine.client;

import static com.wealthfront.test.Assert.assertEmpty;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.joda.time.Duration;
import org.junit.After;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.inject.TypeLiteral;
import com.kaching.platform.common.Pair;
import com.kaching.platform.common.Strings;
import com.kaching.platform.guice.KachingServices.ICG;
import com.kaching.platform.queryengine.Ping;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;
import com.kaching.util.DefaultSleeper;
import com.kaching.util.Sleeper;
import com.kaching.util.concurrent.ConcurrentExecutorImpl;
import com.kaching.util.functional.Pointer;
import com.kaching.util.functional.Result;

public class ConcurrentExecutorImplTest {

  private static final Duration SLEEP_TIME = Duration.millis(10L);

  private final ExecutorService futureExecutor = Executors.newSingleThreadExecutor();
  private final Sleeper sleeper = new DefaultSleeper();
  private final WFMockery mockery = Mockeries.mockery();
  private final SmartClient<ICG> smartClient = mockery.mockGeneric(new TypeLiteral<SmartClient<ICG>>() {});

  @After
  public void mockeryAssert() throws InterruptedException {
    mockery.assertIsSatisfied();
    futureExecutor.shutdown();
    assertTrue(futureExecutor.awaitTermination(10, TimeUnit.SECONDS));
  }

  @Test
  public void executeConcurrently_respectsConcurrencyLimit() throws InterruptedException, ExecutionException {
    ConcurrentExecutorImpl executor = getExecutor();

    Pointer<Boolean> future1Complete = Pointer.pointer(false);

    mockery.checking(new WExpectations() {{
      oneOf(smartClient).invokeAsync(with(query(new Ping())));
      will(returnValue(CompletableFuture.supplyAsync(() -> {
        while (!future1Complete.get()) {
          sleeper.sleep(SLEEP_TIME);
        }
        return "abc";
      }, futureExecutor)));
    }});

    CompletableFuture<List<Pair<Ping, Result<String>>>> future = executor.executeConcurrently(
        smartClient::invokeAsync, ImmutableList.of(new Ping(), new Ping()), 1);

    sleeper.sleep(SLEEP_TIME.multipliedBy(5L));
    mockery.assertIsSatisfied();

    mockery.checking(new WExpectations() {{
      oneOf(smartClient).invokeAsync(with(query(new Ping())));
      will(returnValue(CompletableFuture.completedFuture("def")));
    }});

    assertFalse(future.isDone());

    future1Complete.set(true);
    List<Pair<Ping, Result<String>>> results = future.get();
    mockery.assertIsSatisfied();

    assertEquals(2, results.size());
    assertEquals("abc", results.get(0).getRight().getOrThrow());
    assertEquals("def", results.get(1).getRight().getOrThrow());
  }

  @Test
  public void executeConcurrently_supportsConcurrency() throws InterruptedException, ExecutionException {
    ConcurrentExecutorImpl executor = getExecutor();

    Pointer<Boolean> future1Complete = Pointer.pointer(false);
    Pointer<Boolean> future2Complete = Pointer.pointer(false);

    mockery.checking(new WExpectations() {{
      oneOf(smartClient).invokeAsync(with(query(new Ping())));
      will(returnValue(CompletableFuture.supplyAsync(() -> {
        while (!future1Complete.get()) {
          sleeper.sleep(SLEEP_TIME);
        }
        return "abc";
      }, futureExecutor)));

      oneOf(smartClient).invokeAsync(with(query(new Ping())));
      will(returnValue(CompletableFuture.supplyAsync(() -> {
        while (!future2Complete.get()) {
          sleeper.sleep(SLEEP_TIME);
        }
        return "def";
      }, futureExecutor)));
    }});

    CompletableFuture<List<Pair<Ping, Result<String>>>> future = executor.executeConcurrently(
        smartClient::invokeAsync, ImmutableList.of(new Ping(), new Ping()), 2);
    sleeper.sleep(SLEEP_TIME.multipliedBy(5L));
    mockery.assertIsSatisfied();

    assertFalse(future.isDone());

    future1Complete.set(true);
    future2Complete.set(true);
    List<Pair<Ping, Result<String>>> results = future.get();

    assertEquals(2, results.size());
    assertEquals(ImmutableSet.of("abc", "def"),
        results.stream().map(Pair::getRight).map(Result::getOrThrow).collect(Collectors.toSet()));
  }

  @Test
  public void executeConcurrently_collectsErrors() throws ExecutionException, InterruptedException {
    ConcurrentExecutorImpl executor = getExecutor();

    mockery.checking(new WExpectations() {{
      oneOf(smartClient).invokeAsync(with(query(new Ping())));
      will(returnValue(CompletableFuture.supplyAsync(() -> "abc", futureExecutor)));

      oneOf(smartClient).invokeAsync(with(query(new Ping())));
      will(returnValue(CompletableFuture.supplyAsync(() -> {
        throw new IllegalArgumentException("bad argument!");
      }, futureExecutor)));
    }});

    List<Pair<Ping, Result<String>>> results = executor.executeConcurrently(
        smartClient::invokeAsync, ImmutableList.of(new Ping(), new Ping()), 1).get();

    assertEquals(2, results.size());
    assertEquals("abc", results.get(0).getRight().getOrThrow());
    assertEquals("java.lang.IllegalArgumentException: bad argument!",
        results.get(1).getRight().getException().getOrThrow().getMessage());
  }

  @Test
  public void executeConcurrently_worksWithHighConcurrency() throws InterruptedException, ExecutionException {
    int threads = 128;
    int workPerThread = 200;
    int work = threads * workPerThread;
    ExecutorService futureExecutor = Executors.newFixedThreadPool(threads);
    ConcurrentExecutorImpl executor = getExecutor();
    List<Integer> items = IntStream.range(0, work).boxed().collect(Collectors.toList());

    List<Pair<Integer, Result<Boolean>>> results = executor.executeConcurrently(item -> {
          if (item % 17 == 0) {
            throw new RuntimeException(item.toString());
          }
          return CompletableFuture.supplyAsync(() -> {
            if (item % 3 == 0) {
              sleeper.sleep(SLEEP_TIME);
            }
            if (item % 5 == 0) {
              sleeper.sleep(SLEEP_TIME.multipliedBy(2));
            }
            if (item % 7 == 0) {
              sleeper.sleep(SLEEP_TIME.multipliedBy(3));
            }
            if (item % 13 == 0) {
              throw new RuntimeException(item.toString());
            }
            return item % 2 == 0;
          }, futureExecutor);
        },
        items, threads)
        .get();

    assertEquals(work, results.size());
    for (Pair<Integer, Result<Boolean>> pair : results) {
      if (pair.getLeft() % 17 == 0) {
        assertEquals(Strings.format("%s", pair.getLeft()),
            pair.getRight().getException().getOrThrow().getMessage());
      } else if (pair.getLeft() % 13 == 0) {
        assertEquals(Strings.format("java.lang.RuntimeException: %s", pair.getLeft()),
            pair.getRight().getException().getOrThrow().getMessage());
      } else {
        assertEquals(pair.getLeft() % 2 == 0, pair.getRight().getOrThrow());
      }
    }

    futureExecutor.shutdown();
    assertTrue(futureExecutor.awaitTermination(10, TimeUnit.SECONDS));
  }

  @Test
  public void executeConcurrently_highWaterMark() throws InterruptedException, ExecutionException {
    int threads = 128;
    int concurrency = threads / 2;
    int workPerThread = 100;
    int work = threads * workPerThread;
    ExecutorService futureExecutor = Executors.newFixedThreadPool(threads);
    ConcurrentExecutorImpl executor = getExecutor();
    List<Integer> items = IntStream.range(0, work).boxed().collect(Collectors.toList());

    AtomicInteger activeThreads = new AtomicInteger(0);
    BlockingQueue<Integer> highWaterMarks = new ArrayBlockingQueue<>(work);

    List<Pair<Integer, Result<Boolean>>> results = executor.executeConcurrently(item ->
            CompletableFuture.supplyAsync(() -> {
              activeThreads.incrementAndGet();
              sleeper.sleep(SLEEP_TIME);
              highWaterMarks.add(activeThreads.getAndDecrement());
              return true;
            }, futureExecutor),
        items, concurrency)
        .get();

    assertEquals(work, results.size());
    assertEquals(work, highWaterMarks.size());
    highWaterMarks.forEach(highWaterMark -> assertTrue(highWaterMark <= concurrency));

    futureExecutor.shutdown();
    assertTrue(futureExecutor.awaitTermination(10, TimeUnit.SECONDS));
  }

  @Test
  public void executeConcurrently_handleExceptionInFutureGenerator() throws ExecutionException, InterruptedException {
    ConcurrentExecutorImpl executor = getExecutor();

    mockery.checking(new WExpectations() {{
      oneOf(smartClient).invokeAsync(with(query(new Ping())));
      will(throwException(new RuntimeException("oops")));
    }});

    List<Pair<Ping, Result<String>>> results = executor.executeConcurrently(
        smartClient::invokeAsync, ImmutableList.of(new Ping()), 1).get();

    assertEquals(1, results.size());
    assertEquals("oops", results.get(0).getRight().getException().getOrThrow().getMessage());
  }

  @Test
  public void executeConcurrently_handleThrowableInFutureGenerator() throws InterruptedException {
    ConcurrentExecutorImpl executor = getExecutor();

    mockery.checking(new WExpectations() {{
      oneOf(smartClient).invokeAsync(with(query(new Ping())));
      will(throwException(new AssertionError("oops")));
    }});

    CompletableFuture<?> results = executor.executeConcurrently(
        smartClient::invokeAsync, ImmutableList.of(new Ping()), 1);

    assertTrue(results.isCompletedExceptionally());
    boolean foundError = false;
    try {
      results.get();
    } catch (ExecutionException e) {
      if (e.getCause() instanceof AssertionError) {
        foundError = e.getCause().getMessage().equals("oops");
      }
    }
    assertTrue(foundError);
  }

  @Test
  public void executeConcurrently_worksWithHighConcurrency_errorThrownInFutureCreation() throws InterruptedException, ExecutionException {
    int threads = 128;
    int workPerThread = 200;
    int work = threads * workPerThread;
    ExecutorService futureExecutor = Executors.newFixedThreadPool(threads);
    ConcurrentExecutorImpl executor = getExecutor();
    List<Integer> items = IntStream.range(0, work).boxed().collect(Collectors.toList());

    CompletableFuture<?> future = executor.executeConcurrently(item -> {
          if (item == threads / 2 * workPerThread) {
            sleeper.sleep(SLEEP_TIME);
            throw new AssertionError(item.toString());
          }
          return CompletableFuture.supplyAsync(() -> {
            if (item % 3 == 0) {
              sleeper.sleep(SLEEP_TIME);
            }
            if (item % 5 == 0) {
              sleeper.sleep(SLEEP_TIME.multipliedBy(2));
            }
            if (item % 7 == 0) {
              sleeper.sleep(SLEEP_TIME.multipliedBy(3));
            }
            if (item % 13 == 0) {
              throw new RuntimeException(item.toString());
            }
            return item % 2 == 0;
          }, futureExecutor);
        },
        items, threads);

    boolean foundError = false;
    try {
      future.get();
    } catch (ExecutionException e) {
      if (e.getCause() instanceof AssertionError) {
        foundError = e.getCause().getMessage().equals(Integer.toString(threads / 2 * workPerThread));
      }
    }
    assertTrue(foundError);
    assertTrue(future.isCompletedExceptionally());

    futureExecutor.shutdown();
    assertTrue(futureExecutor.awaitTermination(10, TimeUnit.SECONDS));
  }

  @Test
  public void executeConcurrently_zeroItems() throws InterruptedException, ExecutionException {
    ConcurrentExecutorImpl executor = getExecutor();

    List<Pair<Ping, Result<String>>> results = executor.executeConcurrently(
        (Function<Ping, CompletableFuture<String>>) smartClient::invokeAsync, ImmutableList.of(), 1)
        .get();

    assertEmpty(results);
  }

  private ConcurrentExecutorImpl getExecutor() {
    return new ConcurrentExecutorImpl();
  }

}