package com.kaching.platform.queryengine.client;

import static com.kaching.platform.testing.WExpectations.returnList;
import static com.wealthfront.test.Assert.assertSetEquals;
import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertSame;

import java.io.IOException;
import java.net.URI;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.jmock.Expectations;
import org.jmock.Mockery;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.google.common.base.Charsets;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.inject.TypeLiteral;
import com.google.inject.util.Providers;
import com.kaching.DefaultJsonMarshallerFactory;
import com.kaching.platform.discovery.Resolver;
import com.kaching.platform.discovery.ServiceDescriptor;
import com.kaching.platform.discovery.ServiceId;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.queryengine.SerializerFactory;
import com.kaching.platform.queryengine.TwoLattesSerializerProvider;
import com.kaching.util.functional.Result;
import com.kaching.util.http.HttpClientStub;
import com.kaching.util.http.Parameter;
import com.kaching.util.http.QP0P1Formatter;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

public class QueryEngineHttpClientTest {

  private Mockery mockery;
  private Resolver<?> resolver;

  @Before
  public void before() {
    mockery = new Mockery();
    resolver = mockery.mock(Resolver.class);
  }

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void invoke() throws Exception {
    final byte[] response = new byte[]{};
    final URI uri = new URI("http://localhost:1234");

    mockery.checking(new Expectations() {{
      oneOf(resolver).resolve();
      will(returnValue(new ServiceDescriptor(new ServiceId(""), new ServiceKind() {}.getClass(), uri)));
    }});

    QueryEngineHttpClient client = createQueryEngineHttpClient(new Callback() {
      @Override
      public byte[] post(URI actualUri, List<Parameter> parameters) {
        assertEquals(uri, actualUri);

        Iterator<Parameter> iterator = parameters.iterator();
        {
          Parameter parameter = iterator.next();
          assertEquals("q", parameter.getName());
          assertEquals("query", parameter.getValue());
        }
        {
          Parameter parameter = iterator.next();
          assertEquals("p0", parameter.getName());
          assertEquals("hello", parameter.getValue());
        }
        {
          Parameter parameter = iterator.next();
          assertEquals("p1", parameter.getName());
          assertEquals("world", parameter.getValue());
        }
        assertFalse(iterator.hasNext());
        return response;
      }
    });

    byte[] actualResponse = client.invoke(resolver, "query", "hello", "world");

    assertSame(response, actualResponse);
  }

  @Test
  public void invokeWithUri() throws Exception {
    final byte[] response = new byte[]{};
    final URI uri = new URI("http://localhost:1234");

    QueryEngineHttpClient client = createQueryEngineHttpClient(new Callback() {
      @Override
      public byte[] post(URI actualUri, List<Parameter> parameters) {
        assertEquals(uri, actualUri);

        Iterator<Parameter> iterator = parameters.iterator();
        {
          Parameter parameter = iterator.next();
          assertEquals("q", parameter.getName());
          assertEquals("query", parameter.getValue());
        }
        {
          Parameter parameter = iterator.next();
          assertEquals("p0", parameter.getName());
          assertEquals("hello", parameter.getValue());
        }
        {
          Parameter parameter = iterator.next();
          assertEquals("p1", parameter.getName());
          assertEquals("world", parameter.getValue());
        }
        assertFalse(iterator.hasNext());
        return response;
      }
    });

    byte[] actualResponse = client.invoke(uri, "query", Lists.newArrayList("hello", "world"));
    assertSame(response, actualResponse);
  }

  @Test
  public void invokeWithDeserialization() throws Exception {
    mockery.checking(new Expectations() {{
      oneOf(resolver).resolve();
      will(returnValue(
          new ServiceDescriptor(new ServiceId(""), new ServiceKind() {}.getClass(), new URI("http://localhost:1234"))));
    }});

    QueryEngineHttpClient client = createQueryEngineHttpClient(new Callback() {
      @Override
      public byte[] post(URI actualUri, List<Parameter> parameters) {
        return "{\"greeting\":\"Hello, World!\"}".getBytes(Charsets.UTF_8);
      }
    });

    MyJsonObject actualResponse = client.invoke(resolver, "query", MyJsonObject.class, "hello", "world");

    assertEquals("Hello, World!", actualResponse.greeting);
  }

  @Test
  public void invokeWithLiteralDeserialization() throws Exception {
    mockery.checking(new Expectations() {{
      oneOf(resolver).resolve();
      will(returnValue(
          new ServiceDescriptor(new ServiceId(""), new ServiceKind() {}.getClass(), new URI("http://localhost:1234"))));
    }});

    QueryEngineHttpClient client = createQueryEngineHttpClient(new Callback() {
      @Override
      public byte[] post(URI actualUri, List<Parameter> parameters) {
        return "[{\"greeting\":\"Hello, World!\"},{\"greeting\":\"Goodbye\"}]".getBytes(Charsets.UTF_8);
      }
    });

    Set<MyJsonObject> actualResponse = client.invoke(resolver, "query",
        new TypeLiteral<Set<MyJsonObject>>() {}, "hello", "world");

    assertEquals(ImmutableSet.of(new MyJsonObject("Hello, World!"), new MyJsonObject("Goodbye")), actualResponse);
  }

  @Test
  public void scatterGatherInvoke() throws Exception {
    mockery.checking(new Expectations() {{
      oneOf(resolver).resolveAll();
      will(returnList(
          new ServiceDescriptor(new ServiceId("1"), new ServiceKind() {}.getClass(), new URI("http://localhost:1234")),
          new ServiceDescriptor(new ServiceId("2"), new ServiceKind() {}.getClass(), new URI("http://localhost:2345"))
      ));
    }});

    QueryEngineHttpClient client = createQueryEngineHttpClient(
        (actualUri, parameters) -> "[{\"greeting\":\"Hello, World!\"},{\"greeting\":\"Goodbye\"}]"
            .getBytes(Charsets.UTF_8));

    Map<ServiceId, Result<Set<MyJsonObject>>> actualResponse = client.scatterGatherInvoke(resolver, "query",
        new TypeLiteral<Set<MyJsonObject>>() {}, Lists.newArrayList("hello", "world"));

    Set<MyJsonObject> serviceResult = ImmutableSet.of(new MyJsonObject("Hello, World!"), new MyJsonObject("Goodbye"));
    assertEquals(2, actualResponse.size());
    assertSetEquals(serviceResult, actualResponse.get(new ServiceId("1")).getOrThrow());
    assertSetEquals(serviceResult, actualResponse.get(new ServiceId("2")).getOrThrow());
  }

  @Test
  public void scatterGatherInvokeFailure() throws Exception {
    mockery.checking(new Expectations() {{
      oneOf(resolver).resolveAll();
      will(returnList(
          new ServiceDescriptor(new ServiceId("1"), new ServiceKind() {}.getClass(), new URI("http://localhost:1234")),
          new ServiceDescriptor(new ServiceId("2"), new ServiceKind() {}.getClass(), new URI("http://localhost:2345"))
      ));
    }});

    QueryEngineHttpClient client = createQueryEngineHttpClient((actualUri, parameters) -> {
      throw new IOException("Oh no!");
    });

    Map<ServiceId, Result<Set<MyJsonObject>>> actualResponse = client.scatterGatherInvoke(resolver, "query",
        new TypeLiteral<Set<MyJsonObject>>() {}, Lists.newArrayList("hello", "world"));

    assertEquals(2, actualResponse.size());
    assertThrows(RuntimeException.class, "java.io.IOException: Oh no!", () -> {
      actualResponse.get(new ServiceId("1")).throwOnFailure();
    });
    assertThrows(RuntimeException.class, "java.io.IOException: Oh no!", () -> {
      actualResponse.get(new ServiceId("2")).throwOnFailure();
    });
  }

  private QueryEngineHttpClient createQueryEngineHttpClient(final Callback callback) {
    SerializerFactory serializerFactory = new SerializerFactory(
        new TwoLattesSerializerProvider(new DefaultJsonMarshallerFactory()));
    return new QueryEngineHttpClient(Providers.of(new HttpClientStub() {
      @Override
      public byte[] post(URI actualUri, List<Parameter> parameters) throws IOException {
        return callback.post(actualUri, parameters);
      }
    }), new QP0P1Formatter(), serializerFactory);
  }

  private interface Callback {

    byte[] post(URI actualUri, List<Parameter> parameters) throws IOException;

  }

  @Entity
  private static class MyJsonObject {

    @Value String greeting;

    MyJsonObject() { /* JSON */ }

    MyJsonObject(String greeting) {
      this.greeting = greeting;
    }

    @Override
    public boolean equals(Object o) {
      if (this == o) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }

      MyJsonObject that = (MyJsonObject) o;

      return greeting.equals(that.greeting);

    }

    @Override
    public int hashCode() {
      return greeting.hashCode();
    }
  }

}
