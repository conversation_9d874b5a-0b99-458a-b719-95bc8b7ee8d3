package com.kaching.platform.queryengine.client;

import static com.google.inject.Guice.createInjector;
import static com.kaching.platform.queryengine.client.SmartClientServiceAnnotations.smartClientServiceAnnotation;
import static com.wealthfront.test.EquivalenceTester.check;
import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.google.inject.AbstractModule;
import com.google.inject.Inject;
import com.google.inject.name.Names;
import com.kaching.platform.guice.KachingServices.FBANK;
import com.kaching.platform.guice.KachingServices.NOP;
import com.kaching.util.http.LongOnlineTimeoutPooling;
import com.kaching.util.http.LongTimeoutPooling;

public class SmartClientServiceAnnotationsTest {

  @SmartClientServiceAnnotation(kind = NOP.class, annotation = LongTimeoutPooling.class)
  @Inject
  String nopLongTimeout;

  @SmartClientServiceAnnotation(kind = FBANK.class, annotation = LongOnlineTimeoutPooling.class)
  @Inject
  String fbankLongTimeoutPooling;

  @Test
  public void annotation() throws NoSuchFieldException {
    SmartClientServiceAnnotation expectedNopLongTimeout =
        getClass().getDeclaredField("nopLongTimeout").getAnnotation(SmartClientServiceAnnotation.class);
    SmartClientServiceAnnotation expectedFbankLongTimeoutPooling =
        getClass().getDeclaredField("fbankLongTimeoutPooling").getAnnotation(SmartClientServiceAnnotation.class);

    SmartClientServiceAnnotation nopLongTimeoutImpl =
        smartClientServiceAnnotation(NOP.class, LongTimeoutPooling.class);
    SmartClientServiceAnnotation fbankLongTimeoutPoolingImpl =
        smartClientServiceAnnotation(FBANK.class, LongOnlineTimeoutPooling.class);

    check(
        asList(expectedNopLongTimeout, nopLongTimeoutImpl),
        singletonList(smartClientServiceAnnotation(NOP.class, LongOnlineTimeoutPooling.class)),
        singletonList(smartClientServiceAnnotation(FBANK.class, LongTimeoutPooling.class)),
        asList(expectedFbankLongTimeoutPooling, expectedFbankLongTimeoutPooling),
        singletonList(Names.named("foo")));
  }

  @Test
  public void inject() {
    createInjector(new AbstractModule() {
      @Override
      protected void configure() {
        bind(String.class).annotatedWith(smartClientServiceAnnotation(NOP.class, LongTimeoutPooling.class))
            .toInstance("NOP/LongTimeout");
        bind(String.class).annotatedWith(smartClientServiceAnnotation(NOP.class, LongOnlineTimeoutPooling.class))
            .toInstance("NOP/LongOnlineTimeoutPooling");
        bind(String.class).annotatedWith(smartClientServiceAnnotation(FBANK.class, LongTimeoutPooling.class))
            .toInstance("FBANK/LongTimeout");
        bind(String.class).annotatedWith(smartClientServiceAnnotation(FBANK.class, LongOnlineTimeoutPooling.class))
            .toInstance("FBANK/LongOnlineTimeoutPooling");
        bind(String.class).annotatedWith(Names.named("foo")).toInstance("bar");
      }
    }).injectMembers(this);
    assertEquals("NOP/LongTimeout", nopLongTimeout);
    assertEquals("FBANK/LongOnlineTimeoutPooling", fbankLongTimeoutPooling);
  }

}