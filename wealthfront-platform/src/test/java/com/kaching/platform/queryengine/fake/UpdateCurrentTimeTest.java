package com.kaching.platform.queryengine.fake;

import org.jmock.Mockery;
import org.joda.time.DateTime;
import org.junit.After;
import org.junit.Test;

import com.kaching.platform.common.Option;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.WExpectations;
import com.wealthfront.util.time.DateTimeZones;

public class UpdateCurrentTimeTest {

  private static final DateTime UPDATE = new DateTime(2019, 1, 2, 3, 4, 5, DateTimeZones.ET);
  private final Mockery mockery = Mockeries.mockery(true);
  private final OverrideDateTimeProvider dateTimeProvider = mockery.mock(OverrideDateTimeProvider.class);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process_update() {
    mockery.checking(new WExpectations() {{
      oneOf(dateTimeProvider).setOverride(UPDATE);
    }});

    buildQuery(Option.some(UPDATE)).process();
  }

  @Test
  public void process_reset() {
    mockery.checking(new WExpectations() {{
      oneOf(dateTimeProvider).resetOverride();
    }});

    buildQuery(Option.none()).process();
  }

  private UpdateCurrentTime buildQuery(Option<DateTime> maybeTimeUpdate) {
    UpdateCurrentTime query = new UpdateCurrentTime(maybeTimeUpdate);
    query.overrideDateTimeProvider = dateTimeProvider;
    return query;
  }

}