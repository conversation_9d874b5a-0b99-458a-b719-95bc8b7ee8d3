package com.kaching.platform.queryengine.perf;

import static com.wealthfront.test.Assert.assertSorted;

import org.junit.Test;

import com.kaching.platform.components.Component;

public class QueryPerformanceComponentTest {

  @Test
  public void sorted() {
    var component = QueryPerformanceComponent.class.getAnnotation(Component.class);
    assertSorted(component.modules());
    assertSorted(component.queries());
    assertSorted(component.icingaChecks());
    assertSorted(component.dependsOn());
    assertSorted(component.hibernateEntitites());
  }

}