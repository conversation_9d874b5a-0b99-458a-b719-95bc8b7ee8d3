package com.kaching.platform.queryengine.predicates;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.joda.time.LocalDate;
import org.junit.BeforeClass;
import org.junit.Test;

import com.wealthfront.entities.Market;

public class FirstMarketOpenDayOfMonthPredicateTest {

  private static final FirstMarketOpenDayOfMonthPredicate predicate = new FirstMarketOpenDayOfMonthPredicate();

  @BeforeClass
  public static void setup() {
    predicate.market = Market.MARKET;
  }

  @Test
  public void test() {
    predicate.today = new LocalDate(2018, 3, 1);
    assertTrue(predicate.satisfied());
    predicate.today = new LocalDate(2018, 3, 2);
    assertFalse(predicate.satisfied());

    predicate.today = new LocalDate(2018, 4, 1);
    assertFalse(predicate.satisfied());
    predicate.today = new LocalDate(2018, 4, 2);
    assertTrue(predicate.satisfied());
  }

}