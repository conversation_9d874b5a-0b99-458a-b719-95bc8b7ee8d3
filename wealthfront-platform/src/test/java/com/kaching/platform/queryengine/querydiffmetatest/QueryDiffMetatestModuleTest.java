package com.kaching.platform.queryengine.querydiffmetatest;

import static com.google.inject.Guice.createInjector;
import static com.google.inject.Stage.PRODUCTION;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;

import org.junit.Before;
import org.junit.Test;

import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.name.Names;
import com.kaching.platform.guice.KawalaTestModule;
import com.kaching.security.Cipher;

public class QueryDiffMetatestModuleTest {

  private Injector injector;

  @Before
  public void before() {
    injector = createInjector(PRODUCTION, new KawalaTestModule(), new QueryDiffMetatestModule());
  }

  @Test
  public void bindsCiphers() {
    assertNotNull(injector.getInstance(Key.get(Cipher.class, Names.named("QueryDiffMetatest"))));
  }

  @Test
  public void otherBindings() {
    assertFalse(injector.getInstance(Key.get(Boolean.class, Names.named("disableQueryDiffMetatest"))));
  }

}
