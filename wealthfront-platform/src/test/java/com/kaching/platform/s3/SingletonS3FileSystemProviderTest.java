package com.kaching.platform.s3;

import static org.junit.Assert.assertSame;

import java.io.IOException;
import java.nio.file.FileSystem;

import org.junit.Test;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;

public class SingletonS3FileSystemProviderTest {

  @Test
  public void producesSingletonInstance() throws IOException {
    AWSCredentials creds = new BasicAWSCredentials("access", "secret");
    try (FileSystem fs1 = new SingletonS3FileSystemProvider(creds).get();
        FileSystem fs2 = new SingletonS3FileSystemProvider(creds).get()) {
      assertSame(fs1, fs2);
    }
  }

}
