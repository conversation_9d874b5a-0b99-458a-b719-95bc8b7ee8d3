package com.kaching.platform.testing;

import static com.kaching.platform.testing.KachingMatchers.json;
import static com.kaching.platform.testing.KachingMatchers.jsonString;
import static com.kaching.platform.testing.KachingMatchers.matchesJacksonEntity;
import static com.twolattes.json.Json.object;
import static com.wealthfront.test.Assert.assertThrows;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.junit.Assert.assertThat;

import org.jmock.Mockery;
import org.junit.After;
import org.junit.Test;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.twolattes.json.Entity;
import com.twolattes.json.Json;
import com.twolattes.json.Value;

public class KachingMatchersTest {

  private final Mockery mockery = new Mockery();

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Entity(discriminatorName = "type", subclasses = FakeJsonSubclass1.class)
  static class FakeJsonParentClass {

    @Value Integer someInt;
    @Value String someString;

    FakeJsonParentClass() { /* JSON */ }

    FakeJsonParentClass(Integer someInt, String someString) {
      this.someInt = someInt;
      this.someString = someString;
    }

  }

  static class FakeJacksonClass {

    @JsonProperty Integer someJacksonInt;
    @JsonProperty String someJacksonString;

    FakeJacksonClass(Integer someJacksonInt, String someJacksonString) {
      this.someJacksonInt = someJacksonInt;
      this.someJacksonString = someJacksonString;
    }

  }

  static class FakeJacksonClass2 {

    @JsonProperty Integer someJacksonInt2;
    @JsonProperty String someJacksonString2;

    FakeJacksonClass2(Integer someJacksonInt2, String someJacksonString2) {
      this.someJacksonInt2 = someJacksonInt2;
      this.someJacksonString2 = someJacksonString2;
    }

  }

  @Entity(discriminator = "subclass1")
  static class FakeJsonSubclass1 extends FakeJsonParentClass {

    @Value String otherString;

    @SuppressWarnings("unused")
    FakeJsonSubclass1() { /* JSON */ }

    FakeJsonSubclass1(Integer someInt, String someString, String otherString) {
      super(someInt, someString);
      this.otherString = otherString;
    }

  }

  @Entity(discriminator = "subclass2")
  static class FakeJsonSubclass2 extends FakeJsonParentClass {

    @Value String otherString;

    @SuppressWarnings("unused")
    FakeJsonSubclass2() { /* JSON */ }

    FakeJsonSubclass2(Integer someInt, String someString, String otherString) {
      super(someInt, someString);
      this.otherString = otherString;
    }

  }

  @Test
  public void json_withExplicitParentClass_match() {
    FakeJsonParentClass a = new FakeJsonSubclass1(5, "A", "B");
    assertThat(a, is(json(FakeJsonParentClass.class, new FakeJsonSubclass1(5, "A", "B"))));
  }

  @Test
  public void json_withExplicitParentClass_nonMatch() {
    FakeJsonParentClass a = new FakeJsonSubclass1(5, "A", "B");
    assertThat(a, is(not(json(FakeJsonParentClass.class, new FakeJsonSubclass1(5, "A", "X")))));
  }

  @Test
  public void json_withExplicitParentClass_wrongSubclass() {
    FakeJsonParentClass a = new FakeJsonSubclass1(5, "A", "B");
    assertThrows(IllegalArgumentException.class,
        () -> assertThat(a, is(not(json(FakeJsonParentClass.class, new FakeJsonSubclass2(5, "A", "X"))))));
  }

  @Test
  public void json_withoutClass_match() {
    FakeJsonParentClass a = new FakeJsonSubclass1(5, "A", "B");
    assertThat(a, is(json(new FakeJsonSubclass1(5, "A", "B"))));
  }

  @Test
  public void json_withoutClass_nonMatch() {
    FakeJsonParentClass a = new FakeJsonSubclass1(5, "A", "B");
    assertThat(a, is(not(json(new FakeJsonSubclass1(5, "A", "X")))));
  }

  @Test
  public void json_withoutClass_wrongSubclass() {
    FakeJsonParentClass a = new FakeJsonSubclass1(5, "A", "B");
    assertThrows(IllegalArgumentException.class,
        () -> assertThat(a, is(json(new FakeJsonSubclass2(5, "A", "B")))));
  }

  @Test
  public void json_withoutClass_parentClass() {
    FakeJsonParentClass a = new FakeJsonSubclass1(5, "A", "B");
    assertThrows(IllegalArgumentException.class,
        () -> assertThat(a, is(json(new FakeJsonParentClass(5, "A")))));
  }

  @Test
  public void jsonString_withValue() {
    Json.Object json = object(
        "someInt", 5,
        "someString", "A",
        "otherString", "B",
        "type", "subclass1"
    );
    assertThat(json.toString(), is(jsonString(json)));
  }

  @Test
  public void jsonString_withValue_nonMatch() {
    Json.Object json = object(
        "someInt", 5,
        "someString", "A",
        "otherString", "B",
        "type", "subclass2"
    );
    Json.Object otherJson = object(
        "someInt", 5,
        "someString", "A",
        "otherString", "B",
        "type", "subclass1"
    );
    assertThat(otherJson.toString(), is(not(jsonString(json))));
  }

  @Test
  public void jsonString_withEntity() {
    FakeJsonParentClass a = new FakeJsonSubclass1(5, "A", "B");
    String jsonString = object(
        "someInt", 5,
        "someString", "A",
        "otherString", "B",
        "type", "subclass1"
    ).toString();
    assertThat(jsonString, is(jsonString(FakeJsonParentClass.class, a)));
  }

  @Test
  public void jsonString_withEntity_nonMatch() {
    FakeJsonParentClass a = new FakeJsonSubclass1(5, "A", "B");
    String jsonString = object(
        "someInt", 5,
        "someString", "A",
        "otherString", "B",
        "type", "subclass2"
    ).toString();
    assertThat(jsonString, is(not(jsonString(FakeJsonParentClass.class, a))));
  }

  @Test
  public void matchesJacksonEntity_match() {
    FakeJacksonClass a = new FakeJacksonClass(5, "A");
    assertThat(a, is(matchesJacksonEntity(new FakeJacksonClass(5, "A"))));
  }

  @Test
  public void matchesJacksonEntity_nonMatch() {
    FakeJacksonClass a = new FakeJacksonClass(5, "A");
    assertThat(a, is(not(matchesJacksonEntity(new FakeJacksonClass(5, "B")))));
    assertThrows(RuntimeException.class,
        "com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Incompatible types: declared root type ([simple type, class com.kaching.platform.testing.KachingMatchersTest$FakeJacksonClass]) vs `com.kaching.platform.testing.KachingMatchersTest$FakeJacksonClass2`",
        () -> matchesJacksonEntity(new FakeJacksonClass2(5, "A")).matches(new FakeJacksonClass(5, "A")));
  }

}
