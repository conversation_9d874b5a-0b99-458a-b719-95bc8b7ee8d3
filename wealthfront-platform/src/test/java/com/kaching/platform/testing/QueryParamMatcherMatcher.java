package com.kaching.platform.testing;

import static com.google.common.base.Preconditions.checkArgument;
import static com.kaching.KachingInstantiators.createInstantiator;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

import org.hamcrest.Description;
import org.hamcrest.Matcher;
import org.hamcrest.Matchers;
import org.hamcrest.TypeSafeMatcher;

import com.google.common.collect.Lists;
import com.kaching.DefaultJsonMarshallerFactory;
import com.kaching.platform.converters.Instantiator;
import com.kaching.platform.queryengine.Query;

public class QueryParamMatcherMatcher<T extends Query<?>> extends TypeSafeMatcher<T> {

  private final List<Matcher<?>> expectedParams;
  private final Class<T> queryClass;

  public QueryParamMatcherMatcher(Class<T> queryClass, Matcher<?>... params) {
    super(queryClass);
    this.queryClass = queryClass;
    this.expectedParams = Lists.newArrayList(params);
  }

  public QueryParamMatcherMatcher(Class<T> queryClass, List<Matcher<?>> params) {
    super(queryClass);
    this.queryClass = queryClass;
    this.expectedParams = params;
  }

  @Override
  public boolean matchesSafely(T item) {
    Instantiator<T> instantiator = createInstantiator(queryClass, new DefaultJsonMarshallerFactory());
    List<Object> itemParams = instantiator.fromInstanceAsObjects(item);
    if (expectedParams.size() != itemParams.size()) {
      return false;
    }
    for (int i = 0; i < expectedParams.size(); i++) {
      if (!expectedParams.get(i).matches(itemParams.get(i))) {
        return false;
      }
    }
    return true;
  }

  @Override
  public void describeTo(Description description) {
    description.appendText(queryClass.getSimpleName() + "(");
    boolean first = true;
    for (Matcher<?> matcher : expectedParams) {
      if (!first) {
        description.appendText(", ");
      }
      first = false;
      matcher.describeTo(description);
    }
    description.appendText(")");
  }

  @Override
  protected void describeMismatchSafely(T item, Description mismatchDescription) {
    Instantiator<T> instantiator = createInstantiator(queryClass, new DefaultJsonMarshallerFactory());
    List<Object> itemParams = instantiator.fromInstanceAsObjects(item);
    if (expectedParams.size() != itemParams.size()) {
      mismatchDescription
          .appendText("query had " + itemParams.size() + " parameters; expected " + expectedParams.size());
    } else {
      for (int i = 0; i < expectedParams.size(); i++) {
        if (!expectedParams.get(i).matches(itemParams.get(i))) {
          mismatchDescription.appendText("query parameter " + (i + 1) + " ");
          expectedParams.get(i).describeMismatch(itemParams.get(i), mismatchDescription);
          mismatchDescription.appendText(" (expected ");
          expectedParams.get(i).describeTo(mismatchDescription);
          mismatchDescription.appendText("). ");
        }
      }
    }
  }

  public static <T extends Query<?>> QueryParamMatcherMatcher<T> query(Class<T> queryClass, Matcher<?>... params) {
    return new QueryParamMatcherMatcher<>(queryClass, params);
  }

  public static <T extends Query<?>> QueryParamMatcherMatcher<T> query(Function<QueryParameterMatcherBuilder, T> function) {
    QueryParameterMatcherBuilder builder = new QueryParameterMatcherBuilder();
    T query = function.apply(builder);
    List<Matcher<?>> matchers = new ArrayList<>(builder.parameterMatchers);
    QueryParameterMatcherBuilder.validateMatchers(query, matchers);
    return new QueryParamMatcherMatcher<>((Class<T>) query.getClass(), matchers);
  }

  public static class QueryParameterMatcherBuilder {

    private final List<Matcher<?>> parameterMatchers = new ArrayList<>();

    public <T> T with(T value) {
      parameterMatchers.add(Matchers.equalTo(value));
      return value;
    }

    public <T> T with(Matcher<T> matcher) {
      if (matcher == null) {
        parameterMatchers.add(Matchers.nullValue());
      } else {
        parameterMatchers.add(matcher);
      }
      return null;
    }

    public int withInt(Matcher<Integer> matcher) {
      parameterMatchers.add(matcher);
      return 0;
    }

    public double withDouble(Matcher<Double> matcher) {
      parameterMatchers.add(matcher);
      return 0.0;
    }

    public boolean withBoolean(Matcher<Boolean> matcher) {
      parameterMatchers.add(matcher);
      return false;
    }

    public long withLong(Matcher<Long> matcher) {
      parameterMatchers.add(matcher);
      return 0L;
    }

    private static void validateMatchers(Query<?> query, List<Matcher<?>> argumentMatchers) {
      Instantiator instantiator = createInstantiator(query.getClass(), new DefaultJsonMarshallerFactory());
      int numQueryArgs = instantiator.fromInstanceAsObjects(query).size();
      checkArgument(numQueryArgs == argumentMatchers.size(), "QueryParameterMatcherBuilder.with() should be called exactly once per query argument. " +
          "Have %s matchers but expected %s for query %s", argumentMatchers.size(), numQueryArgs, query.getClass().getSimpleName());
    }

  }

}
