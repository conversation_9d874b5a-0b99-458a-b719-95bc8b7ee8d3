package com.kaching.platform.testing;

import static com.kaching.platform.testing.Mockeries.mockery;
import static com.wealthfront.test.Assert.assertThrows;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.lessThan;
import static org.hamcrest.core.IsAnything.anything;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.util.List;

import org.hamcrest.CustomTypeSafeMatcher;
import org.hamcrest.Description;
import org.hamcrest.StringDescription;
import org.junit.After;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.kaching.platform.functional.Unit;
import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.testing.Mockeries.WFMockery;

public class QueryParamMatcherMatcherTest {

  private final WFMockery mockery = mockery();

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  private final QueryParamMatcherMatcher<TestQuery> matcher = new QueryParamMatcherMatcher<>(TestQuery.class,
      equalTo(33),
      containsString("bcd"),
      new CustomTypeSafeMatcher<Id<HibernateEntity>>("an id over 20") {
        @Override
        protected boolean matchesSafely(Id<HibernateEntity> item) {
          return item.getId() > 20;
        }
      }
  );

  private final QueryParamMatcherMatcher<TestQuery> matcherWithExtraParam =
      new QueryParamMatcherMatcher<>(TestQuery.class,
          equalTo(33),
          containsString("bcd"),
          new CustomTypeSafeMatcher<Id<HibernateEntity>>("an id over 20") {
            @Override
            protected boolean matchesSafely(Id<HibernateEntity> item) {
              return item.getId() > 20;
            }
          },
          anything()
      );

  @Test
  public void matches() {
    assertTrue(matcher.matches(new TestQuery(33, "abcde", Id.of(21))));
  }

  @Test
  public void describeTo() {
    Description description = new StringDescription();
    matcher.describeTo(description);
    assertEquals("TestQuery(<33>, a string containing \"bcd\", an id over 20)", description.toString());
  }

  @Test
  public void doesNotMatch_paramMismatch() {
    assertFalse(matcher.matches(new TestQuery(11, "abcde", Id.of(21))));
    assertFalse(matcher.matches(new TestQuery(33, "fffff", Id.of(21))));
    assertFalse(matcher.matches(new TestQuery(33, "abcde", Id.of(19))));
  }

  @Test
  public void doesNotMatch_paramCountMismatch() {
    assertFalse(matcherWithExtraParam.matches(new TestQuery(33, "abcde", Id.of(21))));
  }

  @Test
  public void describeMismatchSafely_oneWrong() {
    Description description = new StringDescription();
    matcher.describeMismatchSafely(new TestQuery(33, "fffff", Id.of(21)), description);
    assertEquals("query parameter 2 was \"fffff\" (expected a string containing \"bcd\"). ", description.toString());
  }

  @Test
  public void describeMismatchSafely_allWrong() {
    Description description = new StringDescription();
    matcher.describeMismatchSafely(new TestQuery(11, "fffff", Id.of(19)), description);
    assertEquals(
        "query parameter 1 was <11> (expected <33>). query parameter 2 was \"fffff\" (expected a string containing \"bcd\"). query parameter 3 was <19> (expected an id over 20). ",
        description.toString());
  }

  @Test
  public void describeMismatchSafely_paramCountWrong() {
    Description description = new StringDescription();
    matcherWithExtraParam.describeMismatchSafely(new TestQuery(33, "abcde", Id.of(21)), description);
    assertEquals("query had 3 parameters; expected 4", description.toString());
  }

  @Test
  public void usingBuilder_successfulMatch() {
    SomeInterface someInterface = mockery.mock(SomeInterface.class);
    mockery.checking(new WExpectations() {{
      oneOf(someInterface).call(with(queryWithParams(b -> new MyQuery(b.with(10), b.with("hello"), b.with(ImmutableList.of(34L, 11L))))));
      
      never(someInterface).call(with(queryWithParams(b -> new MyQuery(b.with(10), b.with("hello"), b.with(ImmutableList.of(34L, 12L))))));
      
      never(someInterface).call(with(queryWithParams(b -> new MyQuery(b.with(10), b.with("world"), b.with(ImmutableList.of(34L, 11L))))));
    }});

    someInterface.call(new MyQuery(10, "hello", ImmutableList.of(34L, 11L)));
  }

  @Test
  public void usingCustomMatcher_successfulMatch() {
    SomeInterface someInterface = mockery.mock(SomeInterface.class);
    mockery.checking(new WExpectations() {{
      oneOf(someInterface).call(with(queryWithParams(b -> new MyQuery(b.withInt(greaterThan(10)), b.with(""), b.with(ImmutableList.of())))));
      
      oneOf(someInterface).call(with(queryWithParams(b -> new MyQuery(b.withInt(lessThan(10)), b.with(""), b.with(ImmutableList.of())))));
      
      never(someInterface).call(with(queryWithParams(b -> new MyQuery(b.withInt(equalTo(10)), b.with(""), b.with(ImmutableList.of())))));
    }});

    someInterface.call(new MyQuery(11, "", ImmutableList.of()));
    someInterface.call(new MyQuery(9, "", ImmutableList.of()));
  }

  @Test
  public void usingBuilder_mixingRealArgsAndMatchers_throws() {
    SomeInterface someInterface = mockery.mock(SomeInterface.class);
    
    assertThrows(IllegalArgumentException.class, () -> {
      mockery.checking(new WExpectations() {{
        oneOf(someInterface).call(with(queryWithParams(b -> new MyQuery(b.with(10), "hello", b.with(ImmutableList.of(34L, 11L))))));
      }});
    });
  }

  @Test
  public void usingBuilder_onlyRealArgs_throws() {
    SomeInterface someInterface = mockery.mock(SomeInterface.class);

    assertThrows(IllegalArgumentException.class, () -> {
      mockery.checking(new WExpectations() {{
        oneOf(someInterface).call(with(queryWithParams(b -> new MyQuery(10, "hello", ImmutableList.of(34L, 11L)))));
      }});
    });
  }
  
  @Test
  public void usingBuilder_withNullMatcher_success() {
    SomeInterface someInterface = mockery.mock(SomeInterface.class);
    mockery.checking(new WExpectations() {{
      oneOf(someInterface).call(with(queryWithParams(b -> new MyQuery(b.with(10), b.with(null), b.with(ImmutableList.of(34L, 11L))))));
    }});
    someInterface.call(new MyQuery(10, null, ImmutableList.of(34L, 11L)));
  }

  private interface SomeInterface {

    void call(Query<?> query);

  }

  private static class MyQuery extends AbstractQuery<Unit> {

    private final int param1;
    private final String param2;
    private final List<Long> param3;

    MyQuery(int param1, String param2, List<Long> param3) {
      this.param1 = param1;
      this.param2 = param2;
      this.param3 = param3;
    }

    @Override
    public Unit process() {
      throw new UnsupportedOperationException();
    }

  }

  @SuppressWarnings("unused,FieldCanBeLocal")
  private static class TestQuery extends AbstractQuery<Void> {

    private final Integer integer;
    private final String string;
    private final Id<HibernateEntity> id;

    TestQuery(Integer integer, String string, Id<HibernateEntity> id) {
      this.integer = integer;
      this.string = string;
      this.id = id;
    }

    @Override
    public Void process() {
      return null;
    }

  }

}