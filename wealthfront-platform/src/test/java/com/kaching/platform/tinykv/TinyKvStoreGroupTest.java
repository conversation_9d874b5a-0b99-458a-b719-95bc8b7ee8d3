package com.kaching.platform.tinykv;

import static org.junit.Assert.assertEquals;

import java.util.Arrays;
import java.util.stream.Collectors;

import org.junit.Test;

import com.kaching.platform.common.AbstractIntIdentifier;

public class TinyKvStoreGroupTest {

  @Test
  public void groupId_unique() {
    assertEquals(
        TinyKvStoreGroup.values().length,
        Arrays.stream(TinyKvStoreGroup.values())
            .map(TinyKvStoreGroup::getGroupId)
            .map(AbstractIntIdentifier::getId)
            .collect(Collectors.toSet())
            .size()
    );
  }

}