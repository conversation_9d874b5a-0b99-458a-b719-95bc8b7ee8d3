package com.kaching.platform.tinykv.impl;

import static com.kaching.platform.testing.Mockeries.mockery;
import static com.wealthfront.test.Assert.assertEquals;
import static com.wealthfront.test.Assert.assertOptionEmpty;

import java.util.Map;
import java.util.Set;

import org.junit.After;
import org.junit.Test;

import com.google.inject.Inject;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.WithReadOnlySession;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;
import com.kaching.platform.tinykv.TinyKvTestBase;
import com.kaching.platform.util.WrappedBytes;
import com.kaching.user.UserId;

public class ProfileTinyKvStoresTest extends TinyKvTestBase {

  private final WFMockery mockery = mockery(true);
  private final IsTinyKvAdminTaskRunner isAdminTaskRunner = mockery.mock(IsTinyKvAdminTaskRunner.class);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process_empty() {
    mockery.checking(new WExpectations() {{
      allowing(isAdminTaskRunner).getStoresThisServiceIsAdminFor();
      will(returnValue(Set.of(userIdStoreId, entityStoreId)));
    }});
    ProfileTinyKvStores query = getQuery();
    query.process();
    assertEquals(Map.of(), getAllSettings());
  }
  
  @Test
  public void process_nonEmpty() {
    persistIntoUserIdStore(Map.of(
        new UserId(11), "42",
        new UserId(12), "43"
    ));
    persistIntoEntityStore(Map.of(
        "1", new MyEntity(1, "hello")
    ));
    mockery.checking(new WExpectations() {{
      allowing(isAdminTaskRunner).getStoresThisServiceIsAdminFor();
      will(returnValue(Set.of(userIdStoreId, entityStoreId)));
    }});
    getQuery().process();
    assertEquals(Map.of(
        "StoreSize-" + userIdStoreId, WrappedBytes.wrapUtf8("2"),
        "StoreSize-" + entityStoreId, WrappedBytes.wrapUtf8("1")
    ), getAllSettings());
    
    transacter().execute(new WithReadOnlySession() {
      @Inject TinyKvStoreSizeFetcher storeSizeFetcher;
      @Override
      public void run(DbSession session) {
        assertEquals(2L, storeSizeFetcher.getSize(userIdStoreId).getOrThrow().intValue());
        assertEquals(1, storeSizeFetcher.getSize(entityStoreId).getOrThrow().intValue());
      }
    });
  }
  
  @Test
  public void process_notAdminTaskRunner_doesNotQuery() {
    persistIntoUserIdStore(Map.of(
        new UserId(11), "42",
        new UserId(12), "43"
    ));
    persistIntoEntityStore(Map.of(
        "1", new MyEntity(1, "hello")
    ));
    mockery.checking(new WExpectations() {{
      allowing(isAdminTaskRunner).getStoresThisServiceIsAdminFor();
      will(returnValue(Set.of(userIdStoreId)));
    }});
    getQuery().process();
    assertEquals(Map.of(
        "StoreSize-" + userIdStoreId, WrappedBytes.wrapUtf8("2")
    ), getAllSettings());

    transacter().execute(new WithReadOnlySession() {
      @Inject TinyKvStoreSizeFetcher storeSizeFetcher;
      @Override
      public void run(DbSession session) {
        assertEquals(2L, storeSizeFetcher.getSize(userIdStoreId).getOrThrow().intValue());
        assertOptionEmpty(storeSizeFetcher.getSize(entityStoreId));
      }
    });
  }
  
  private ProfileTinyKvStores getQuery() {
    ProfileTinyKvStores query = new ProfileTinyKvStores();
    query.isAdminTaskRunner = isAdminTaskRunner;
    query.transacter = injector().getInstance(RetryingTransacter.class);
    return query;
  }

}