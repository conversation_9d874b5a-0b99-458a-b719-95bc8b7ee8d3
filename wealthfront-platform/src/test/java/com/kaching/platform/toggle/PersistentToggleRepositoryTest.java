package com.kaching.platform.toggle;

import static com.kaching.platform.toggle.ToggleFactory.createToggle;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;

import com.google.inject.Inject;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.hibernate.WithReadOnlySession;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.platform.testing.PersistentTestRunner;
import com.kaching.util.functional.Pointer;
import com.kaching.util.tests.PersistentTest;

@RunWith(PersistentTestRunner.class)
@PersistentTest(
    entities = {
        Toggle.class
    })
public class PersistentToggleRepositoryTest {

  @Test
  public void getToggleFromName(Transacter transacter) {
    Pointer<Id<Toggle>> toggle1IdPointer = Pointer.pointer();
    Pointer<Id<Toggle>> toggle2IdPointer = Pointer.pointer();

    transacter.execute((WithSession) dbSession -> {
      toggle1IdPointer.set(
          createToggle()
              .withName("name1")
              .withState(true)
              .buildAndPersist(dbSession)
              .getId());
      toggle2IdPointer.set(
          createToggle()
              .withName("name2")
              .withState(false)
              .buildAndPersist(dbSession)
              .getId());
    });

    transacter.execute(new WithReadOnlySession() {
      @Inject ToggleRepository toggleRepository;

      @Override
      public void run(DbSession dbSession) {
        Toggle toggle1FromDb = toggleRepository.getByName("name1").getOrThrow();
        Toggle toggle2FromDb = toggleRepository.getByName("name2").getOrThrow();

        assertEquals(toggle1IdPointer.get(), toggle1FromDb.getId());
        assertEquals("name1", toggle1FromDb.getName());
        assertTrue(toggle1FromDb.getState());

        assertEquals(toggle2IdPointer.get(), toggle2FromDb.getId());
        assertEquals("name2", toggle2FromDb.getName());
        assertFalse(toggle2FromDb.getState());

        assertNull(toggleRepository.getByName("name3").getOrNull());
      }
    });
  }

  @Test
  public void getOrCreate(Transacter transacter) {
    Pointer<Id<Toggle>> toggle1IdPointer = Pointer.pointer();
    toggle1IdPointer.set(
        createToggle()
            .withName("name1")
            .withState(true)
            .buildAndPersist(transacter)
            .getId());

    transacter.execute(new WithReadOnlySession() {
      @Inject ToggleRepository toggleRepository;

      @Override
      public void run(DbSession dbSession) {
        Toggle toggle1FromDb = toggleRepository.getOrCreate("name1", false, new DateTime(2016, 1, 2, 1, 1, 0, ET));

        assertEquals(toggle1IdPointer.get(), toggle1FromDb.getId());
        assertEquals("name1", toggle1FromDb.getName());
        assertTrue(toggle1FromDb.getState());
      }
    });

    Pointer<Id<Toggle>> toggle2IdPointer = Pointer.pointer();
    transacter.execute(new WithSession() {
      @Inject ToggleRepository toggleRepository;

      @Override
      public void run(DbSession dbSession) {
        Toggle toggle2FromDb = toggleRepository.getOrCreate("name2", false, new DateTime(2016, 1, 2, 1, 1, 0, ET));

        toggle2IdPointer.set(toggle2FromDb.getId());
        assertEquals("name2", toggle2FromDb.getName());
        assertFalse(toggle2FromDb.getState());
      }
    });

    transacter.execute(new WithReadOnlySession() {
      @Inject ToggleRepository toggleRepository;

      @Override
      public void run(DbSession dbSession) {
        assertEquals(toggle2IdPointer.get(), toggleRepository.getByName("name2").getOrThrow().getId());
      }
    });
  }

  @Test
  public void setOrCreate(Transacter transacter) {
    Pointer<Id<Toggle>> toggle1IdPointer = Pointer.pointer();
    toggle1IdPointer.set(
        createToggle()
            .withName("name1")
            .withState(true)
            .buildAndPersist(transacter)
            .getId());

    transacter.execute(new WithSession() {
      @Inject ToggleRepository toggleRepository;

      @Override
      public void run(DbSession dbSession) {
        Toggle toggle1FromDb = toggleRepository.setOrCreate("name1", false, new DateTime(2016, 1, 2, 1, 1, 0, ET));

        assertEquals(toggle1IdPointer.get(), toggle1FromDb.getId());
        assertEquals("name1", toggle1FromDb.getName());
        assertFalse(toggle1FromDb.getState());
      }
    });

    Pointer<Id<Toggle>> toggle2IdPointer = Pointer.pointer();
    transacter.execute(new WithSession() {
      @Inject ToggleRepository toggleRepository;

      @Override
      public void run(DbSession dbSession) {
        Toggle toggle2FromDb = toggleRepository.setOrCreate("name2", false, new DateTime(2016, 1, 2, 1, 1, 0, ET));

        toggle2IdPointer.set(toggle2FromDb.getId());
        assertEquals("name2", toggle2FromDb.getName());
        assertFalse(toggle2FromDb.getState());
      }
    });

    transacter.execute(new WithReadOnlySession() {
      @Inject ToggleRepository toggleRepository;

      @Override
      public void run(DbSession dbSession) {
        assertEquals(toggle2IdPointer.get(), toggleRepository.getByName("name2").getOrThrow().getId());
      }
    });
  }

}