package com.kaching.platform.util;

import static org.hamcrest.Matchers.containsString;
import static org.junit.Assert.assertThat;

import org.joda.time.LocalDate;
import org.junit.Test;

public class FakeDeadCodeMonitorTest {

  @Test
  public void passesIfNotExpired() {
    FakeDeadCodeMonitor monitor = new FakeDeadCodeMonitor() {
      @Override
      LocalDate getRealDate() {
        return new LocalDate(2023, 2, 13);
      }

      @Override
      boolean isRunningOnLaptop() {
        return true;
      }
    };

    monitor.thisCodeCanBeDeletedAfter(new LocalDate(2023, 2, 15));
  }

  @Test
  public void failsIfExpiredOnLaptop() {
    FakeDeadCodeMonitor monitor = new FakeDeadCodeMonitor() {
      @Override
      LocalDate getRealDate() {
        return new LocalDate(2023, 2, 13);
      }

      @Override
      boolean isRunningOnLaptop() {
        return true;
      }
    };

    try {
      monitor.thisCodeCanBeDeletedAfter(new LocalDate(2023, 2, 11));
      throw new RuntimeException("Assertion error should be thrown");
    } catch (AssertionError e) {
      assertThat(e.getMessage(), containsString("This code is past its expiration date. Please remove the dead code."));
    }
  }

  @Test
  public void passesIfExpiredOnCi() {
    FakeDeadCodeMonitor monitor = new FakeDeadCodeMonitor() {
      @Override
      LocalDate getRealDate() {
        return new LocalDate(2023, 2, 13);
      }

      @Override
      boolean isRunningOnLaptop() {
        return false;
      }
    };

    monitor.thisCodeCanBeDeletedAfter(new LocalDate(2023, 2, 11));
  }

}