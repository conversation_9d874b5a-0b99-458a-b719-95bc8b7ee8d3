package com.kaching.platform.zk;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import java.net.URISyntaxException;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.kaching.platform.components.KawalaService;
import com.kaching.platform.discovery.ServiceDescriptor;
import com.kaching.platform.discovery.ServiceId;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.guice.ApplicationOptions;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.guice.KachingServices.KaChingService;
import com.kaching.platform.guice.Revision;
import com.kaching.platform.zk.ZkAnnounceModule.DisabledZkAnnounceModule;
import com.kaching.platform.zk.ZkAnnounceModule.DisabledZkElectionModule;

public class ZkAnnounceModuleTest {

  @Zk
  @KawalaService(port = 9999)
  @KaChingService(authors = {})
  static class TestKind implements ServiceKind {}

  private ApplicationOptions options;

  @Before
  public void before() {
    options = new ApplicationOptions();
    System.setProperty("com.sun.management.jmxremote.port", "7777");
  }

  @After
  public void after() {
    System.clearProperty("com.sun.management.jmxremote.port");
  }

  @Test
  public void localDescriptor() throws URISyntaxException {
    options.host = "localhost";
    options.ip = "127.0.0.1";
    options.serviceId = "tk0";
    ZkAnnounceModule module = ZkAnnounceModule.newZkAnnounceModule(TestKind.class, options);
    ServiceDescriptor descriptor = module.localDescriptor(module.serviceId(), new Revision("abc123"));
    assertEquals("http://127.0.0.1:9999", descriptor.getURI().toString());
    assertEquals(ServiceId.of(options.serviceId), descriptor.getId());
    assertEquals(7777, descriptor.getDebugPort().intValue());
    assertEquals("abc123", descriptor.getRevision());
  }

  @Test
  public void localDescriptor_customServicePortAndNoJmxPort() throws URISyntaxException {
    System.clearProperty("com.sun.management.jmxremote.port");
    options.host = "localhost";
    options.ip = "127.0.0.1";
    options.serviceId = "tk0";
    options.servicePort = 8888;
    ZkAnnounceModule module = ZkAnnounceModule.newZkAnnounceModule(TestKind.class, options);
    ServiceDescriptor descriptor = module.localDescriptor(module.serviceId(), new Revision("abc123"));
    assertEquals("http://127.0.0.1:8888", descriptor.getURI().toString());
    assertEquals(ServiceId.of(options.serviceId), descriptor.getId());
    assertNull(descriptor.getDebugPort());
    assertEquals("abc123", descriptor.getRevision());
  }

  @Test
  public void localDescriptor_missingIPUsesHost() throws URISyntaxException {
    options.host = "localhost";
    ZkAnnounceModule module = ZkAnnounceModule.newZkAnnounceModule(TestKind.class, options);
    ServiceDescriptor descriptor = module.localDescriptor(module.serviceId(), new Revision("abc123"));
    assertEquals("http://localhost:9999", descriptor.getURI().toString());
    assertEquals("abc123", descriptor.getRevision());
  }

  @Test
  public void localDescriptor_missingServiceIdUsesHost() throws URISyntaxException {
    options.host = "localhost";
    ZkAnnounceModule module = ZkAnnounceModule.newZkAnnounceModule(TestKind.class, options);
    ServiceDescriptor descriptor = module.localDescriptor(module.serviceId(), new Revision("abc123"));
    assertEquals(ServiceId.of(options.host), descriptor.getId());
    assertEquals("abc123", descriptor.getRevision());
  }

  @Test
  public void localDescriptor_nullRevision() throws URISyntaxException {
    options.host = "localhost";
    ZkAnnounceModule module = ZkAnnounceModule.newZkAnnounceModule(TestKind.class, options);
    ServiceDescriptor descriptor = module.localDescriptor(module.serviceId(), null);
    assertEquals(ServiceId.of(options.host), descriptor.getId());
    assertNull(descriptor.getRevision());
  }

  @Test
  public void localServiceKind() {
    options.host = "localhost";
    ZkAnnounceModule module = ZkAnnounceModule.newZkAnnounceModule(KachingServices.UM.class, options);
    ServiceKind serviceKind = module.localServiceKind();
    assertEquals(KachingServices.singleton(KachingServices.UM.class), serviceKind);
  }

  @Test
  public void localServiceKind_disabled() {
    options.disableZk = true;
    ZkAnnounceModule module = ZkAnnounceModule.newZkAnnounceModule(KachingServices.UM.class, options);
    ServiceKind serviceKind = module.localServiceKind();
    assertEquals(KachingServices.singleton(KachingServices.UM.class), serviceKind);
    assertTrue(module instanceof DisabledZkAnnounceModule);

    module = ZkAnnounceModule.newZkAnnounceModule(TestKind.class, options);
    serviceKind = module.localServiceKind();
    assertTrue(module instanceof DisabledZkAnnounceModule);
    assertFalse(serviceKind.toString().contains("UM"));
  }

  @Test
  public void disableAnnouncements() {
    options.host = "foo";
    options.disableZk = false;
    options.disableZkAnnounce = true;
    assertTrue(ZkAnnounceModule.newZkAnnounceModule(TestKind.class, options) instanceof DisabledZkAnnounceModule);

    options.disableZk = true;
    options.disableZkAnnounce = false;
    assertTrue(ZkAnnounceModule.newZkAnnounceModule(TestKind.class, options) instanceof DisabledZkAnnounceModule);
  }

  @Test
  public void disableElection() {
    options.host = "foo";
    options.disableZk = false;
    options.disableZkAnnounce = false;
    options.disableZkElection = true;
    assertTrue(ZkAnnounceModule.newZkAnnounceModule(TestKind.class, options) instanceof DisabledZkElectionModule);
  }

}
