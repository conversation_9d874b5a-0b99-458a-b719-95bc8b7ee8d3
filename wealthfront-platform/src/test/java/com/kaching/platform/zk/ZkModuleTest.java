package com.kaching.platform.zk;

import static com.google.inject.Guice.createInjector;

import javax.inject.Provider;

import org.junit.Test;

import com.google.inject.AbstractModule;
import com.google.inject.Guice;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.Module;
import com.google.inject.name.Names;
import com.kaching.DefaultJsonMarshallerFactory;
import com.kaching.KachingInstantiators;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.guice.ApplicationOptions;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.guice.TypeLiterals;

public class ZkModuleTest {

  interface TestKind extends ServiceKind {}

  @Inject SyncAnnouncements syncAnnouncements;
  @Inject SyncManifest syncManifest;
  @Inject ZkLifecycle lifecycle;

  @Test
  public void injector() throws Exception {
    createInjector(createZkModule()).injectMembers(this);
  }

  private static Module[] createZkModule() {
    ApplicationOptions options = new ApplicationOptions();
    return new Module[]{ZkModule.newZkModule(options), new AbstractModule() {
      @Override
      protected void configure() {
        bind(String.class)
            .annotatedWith(Names.named("zk.servers"))
            .toInstance("127.0.0.1:2181");

      }
    }};
  }

  @Test
  public void instantiators() {
    for (Class<?> queryClass : ZkModule.QUERIES) {
      KachingInstantiators.createInstantiator(queryClass, new DefaultJsonMarshallerFactory());
    }
  }

  @Test
  public void shouldRegisterResolversForEveryKachingServices() {
    Injector injector = Guice.createInjector(createZkModule());
    for (Class<? extends ServiceKind> kind : KachingServices.KINDS) {
      injector.getInstance(Key.get(TypeLiterals.get(Provider.class, kind)));
    }
  }

}
