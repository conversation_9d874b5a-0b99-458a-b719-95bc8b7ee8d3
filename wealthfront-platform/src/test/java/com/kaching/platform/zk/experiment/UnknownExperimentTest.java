package com.kaching.platform.zk.experiment;

import static com.kaching.DefaultKachingMarshallers.createEntityMarshaller;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

import com.twolattes.json.EntityMarshaller;

public class UnknownExperimentTest {

  @Test
  public void testMarshall() {
    EntityMarshaller<ExperimentStatus> marshaller = createEntityMarshaller(ExperimentStatus.class);
    UnknownExperiment experiment = new UnknownExperiment(new ExperimentName("hello"));
    assertEquals(UnknownExperiment.class, marshaller.unmarshall(marshaller.marshall(experiment)).getClass());
    assertTrue(experiment.isUnknown());
  }

}