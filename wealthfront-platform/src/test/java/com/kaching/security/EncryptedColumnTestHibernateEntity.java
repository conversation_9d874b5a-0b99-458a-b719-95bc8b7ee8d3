package com.kaching.security;

import static java.nio.charset.StandardCharsets.UTF_8;

import org.joda.time.DateTime;

import com.kaching.DefaultKachingMarshallers;
import com.kaching.platform.hibernate.AbstractHibernateEntity;
import com.kaching.platform.hibernate.Archived;
import com.kaching.platform.hibernate.Id;
import com.kaching.util.types.AbstractFieldsType;
import com.twolattes.json.Entity;
import com.twolattes.json.Marshaller;
import com.twolattes.json.Value;

public class EncryptedColumnTestHibernateEntity extends AbstractHibernateEntity {

  private Id<EncryptedColumnTestHibernateEntity> id;
  private EncryptedColumn encryptedTwoLattesColumn;
  private EncryptedColumn encryptedJacksonColumn;
  private byte[] encryptedByteArrayColumn;
  private EncryptedColumn encryptedAbstractFieldsType;
  private String plainColumn;
  private Archived<EncryptedColumn> archivedEncryptedColumn;

  public EncryptedColumnTestHibernateEntity() {/* hibernate */}

  public EncryptedColumnTestHibernateEntity(String content, DateTime now) {
    this.encryptedTwoLattesColumn = new EncryptedColumn(content);
    this.encryptedJacksonColumn = new EncryptedColumn(content);
    this.encryptedByteArrayColumn = content.getBytes(UTF_8);
    this.encryptedAbstractFieldsType = new EncryptedColumn(content);
    this.archivedEncryptedColumn = new Archived<>(this, now, new EncryptedColumn(content));
    archivedEncryptedColumn.update(now, new EncryptedColumn(content));
    this.plainColumn = content;
  }

  @Override
  public Id<EncryptedColumnTestHibernateEntity> getId() {
    return id;
  }

  public EncryptedColumn getEncryptedTwoLattesColumn() {
    return encryptedTwoLattesColumn;
  }

  public EncryptedColumn getEncryptedJacksonColumn() {
    return encryptedJacksonColumn;
  }

  public byte[] getEncryptedByteArrayColumn() {
    return encryptedByteArrayColumn;
  }

  public EncryptedColumn getEncryptedAbstractFieldsType() {
    return encryptedAbstractFieldsType;
  }

  public Archived<EncryptedColumn> getArchivedEncryptedColumn() {
    return archivedEncryptedColumn;
  }

  @Entity
  public static class EncryptedColumn {

    @Value
    public String content;

    public EncryptedColumn() {/* json */}

    public EncryptedColumn(String content) {
      this.content = content;
    }

    public String getContent() {
      return content;
    }

  }

  public static class TestAbstractFieldsType extends AbstractFieldsType<EncryptedColumn> {

    @Override
    public Marshaller<EncryptedColumn> marshaller() {
      return DefaultKachingMarshallers.createMarshaller(EncryptedColumn.class);
    }

    @Override
    public Class<EncryptedColumn> returnedClass() {
      return EncryptedColumn.class;
    }

    @Override
    public String getCipherNameKey() {
      return "EncryptedColumn";
    }

  }

}