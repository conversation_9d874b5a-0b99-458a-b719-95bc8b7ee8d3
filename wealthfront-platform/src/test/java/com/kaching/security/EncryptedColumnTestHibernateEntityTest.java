package com.kaching.security;

import static com.google.inject.name.Names.named;
import static com.kaching.security.SecurityModule.CipherInfo.Cipher.AES;
import static java.nio.charset.StandardCharsets.UTF_8;
import static org.junit.Assert.assertEquals;

import java.util.List;

import org.joda.time.DateTime;
import org.junit.BeforeClass;
import org.junit.Test;

import com.google.inject.AbstractModule;
import com.kaching.platform.guice.KawalaTestModule;
import com.kaching.platform.hibernate.Archived;
import com.kaching.platform.hibernate.Id;
import com.kaching.util.tests.PersistentTestBase;
import com.wealthfront.util.time.DateTimeZones;

public class EncryptedColumnTestHibernateEntityTest extends PersistentTestBase {

  private static final DateTime NOW = new DateTime(2024, 8, 19, 1, 3, 5, 7, DateTimeZones.ET);

  private static final String KEY1 = "9eqK2zJ/MFEix051k4i6jBP6AFb7gLcXQSMxND0P3wM=";
  private static final String KEY2 = "JGndw+D90fnPFQ/KX5FI6rm/yZ43ptB0AXw53ly4q3g=";

  @BeforeClass
  public static void beforeClass() {
    configure(new AbstractModule() {
      @Override
      protected void configure() {
        bind(String.class).annotatedWith(named("um.cipher_keys.encrypted_column.primary.key")).toInstance(KEY1);
        bind(String.class).annotatedWith(named("um.cipher_keys.encrypted_column.primary.cipher_type")).toInstance("AES");
        bind(String.class).annotatedWith(named("um.cipher_keys.encrypted_column.secondary.key")).toInstance(KEY2);
        bind(String.class).annotatedWith(named("um.cipher_keys.encrypted_column.secondary.cipher_type")).toInstance("AES");

        install(new KawalaTestModule());
        install(new SecurityModule(List.of(
            new SecurityModule.CipherInfo("EncryptedColumn", "um.cipher_keys.encrypted_column", AES))));
      }
    }, EncryptedColumnTestHibernateEntity.class);
  }

  @Test
  public void test() {
    Id<EncryptedColumnTestHibernateEntity> id = transacter.save(
        new EncryptedColumnTestHibernateEntity("content", NOW));
    transacter.executeWithReadOnlySession(session -> {
      EncryptedColumnTestHibernateEntity entity = session.getOrThrow(EncryptedColumnTestHibernateEntity.class, id);
      assertEquals("content", entity.getEncryptedTwoLattesColumn().getContent());
      assertEquals("content", entity.getEncryptedJacksonColumn().getContent());
      assertEquals("content", new String(entity.getEncryptedByteArrayColumn(), UTF_8));
      assertEquals("content", entity.getEncryptedAbstractFieldsType().getContent());
      Archived<EncryptedColumnTestHibernateEntity.EncryptedColumn> archived = entity.getArchivedEncryptedColumn();
      assertEquals("content", archived.current().getContent());
      assertEquals("content", archived.getAllVersions().get(0).get().getContent());
    });
  }

}