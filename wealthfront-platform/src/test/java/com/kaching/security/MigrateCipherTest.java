package com.kaching.security;

import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;

import org.joda.time.DateTime;
import org.junit.Test;

import com.kaching.platform.hibernate.Id;
import com.wealthfront.util.time.DateTimeZones;

public class MigrateCipherTest extends CipherMigratorPersistentTestBase {

  private static final DateTime NOW = new DateTime(2024, 8, 19, 1, 3, 5, 7, DateTimeZones.ET);

  @Test
  public void process() {
    Id<EncryptedColumnTestHibernateEntity> id = transacter.save(
        new EncryptedColumnTestHibernateEntity("one", NOW));

    assertEquals("{\"content\":\"one\"}",
        getEncryptedGZippedJsonHibernateType(id, "encrypted_twolattes_column", CIPHER1));
    assertThrows(RuntimeException.class, () ->
        getEncryptedGZippedJsonHibernateType(id, "encrypted_twolattes_column", CIPHER2));

    setEncryptedGZippedJsonHibernateType(
        id, "encrypted_twolattes_column", CIPHER2, "{\"content\":\"two\",\"unused\":\"unused\"}");

    assertThrows(RuntimeException.class, () ->
        getEncryptedGZippedJsonHibernateType(id, "encrypted_twolattes_column", CIPHER1));
    assertEquals("{\"content\":\"two\",\"unused\":\"unused\"}",
        getEncryptedGZippedJsonHibernateType(id, "encrypted_twolattes_column", CIPHER2));

    MigrateCipher query = new MigrateCipher("encrypted_column_entities", "encrypted_twolattes_column", "EncryptedColumn", id);
    query.migrator = injector.getInstance(CipherMigrator.class);
    query.process();

    assertEquals("two", transacter.executeWithReadOnlySessionExpression(session ->
        session.getOrThrow(EncryptedColumnTestHibernateEntity.class, id).getEncryptedTwoLattesColumn().getContent()));

    assertEquals("{\"content\":\"two\",\"unused\":\"unused\"}",
        getEncryptedGZippedJsonHibernateType(id, "encrypted_twolattes_column", CIPHER1));
    assertThrows(RuntimeException.class, () ->
        getEncryptedGZippedJsonHibernateType(id, "encrypted_twolattes_column", CIPHER2));
  }

}