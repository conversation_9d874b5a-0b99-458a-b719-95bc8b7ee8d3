package com.kaching.util;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.google.common.collect.ImmutableSet;
import com.twolattes.json.Json;

public class JsonCursorTest {

  @Test
  public void testCursor() {
    assertEquals("the string", new JsonCursor(Json.string("the string")).string());
    assertEquals(42,
        new JsonCursor(Json.object("amount", 42)).get("amount").numberMaybe().getOrThrow().intValueExact());
    assertEquals("world", new JsonCursor(Json.object("hello", "world")).get("hello").stringMaybe().getOrThrow());
    assertEquals(true, new JsonCursor(Json.object("isFalse", true)).get("isFalse").boolMaybe().getOrThrow());
    assertEquals("b", new JsonCursor(Json.array("a", "b", "c")).get(1).string());
    assertEquals(true, new JsonCursor(Json.NULL).isNull());
    assertEquals(true, new JsonCursor(null).isNull());
  }

  @Test
  public void testFromString() {
    assertEquals("world", JsonCursor.cursor("{\"hello\":\"world\"}").get("hello").string());
  }

  @Test
  public void testKeys() {
    assertEquals(ImmutableSet.of("hello", "world"), JsonCursor.cursor("{\"hello\":1,\"world\":2}").keys());
  }

  @Test
  public void testAsJsonValue() {
    assertEquals("good", new JsonCursor(Json.object("hello", "world"))
        .visit(new AbstractJsonVisitor<String>(null) {
          @Override
          public String caseObject(Json.Object object) {
            return "good";
          }
        }));
  }

  @Test
  public void testMultiGet() {
    assertEquals(true, new JsonCursor(Json.object("one", Json.object("two", Json.object("three", true))))
        .get("one", "two", "three")
        .boolMaybe()
        .getOrThrow());
    assertEquals(true, new JsonCursor(Json.object("one", Json.object("two", Json.object("three", true))))
        .get("one", "two", "notthree")
        .boolMaybe()
        .isEmpty());
    assertEquals(true,
        new JsonCursor(Json.object("one", Json.object("two", Json.object("three", true)))).get("whatever").isNull());

    assertEquals(true, new JsonCursor(Json.array("one", Json.array("two", Json.array("three", true))))
        .get(1, 1, 1)
        .boolMaybe()
        .getOrThrow());
    assertEquals(true, new JsonCursor(Json.array("one", Json.array("two", Json.array("three", true))))
        .get(1, 1, 2)
        .boolMaybe()
        .isEmpty());
    assertEquals(true, new JsonCursor(Json.array("one", Json.array("two", Json.array("three", true))))
        .get(1, 1, -1)
        .boolMaybe()
        .isEmpty());
    assertEquals(true, new JsonCursor(Json.array("one", Json.array("two", Json.array("three", true)))).get(3).isNull());
  }

}
