package com.kaching.util.concurrent;

import static com.google.common.base.Preconditions.checkState;
import static com.kaching.util.concurrent.DeterministicThreadScheduler.ThreadState.READY;
import static com.kaching.util.concurrent.DeterministicThreadScheduler.ThreadState.WAITING;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Date;
import java.util.Deque;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;

import org.jetbrains.annotations.NotNull;

import com.kaching.platform.common.Option;
import com.kaching.util.concurrent.DeterministicThreadScheduler.ParticipatingThread;

public class DeterministicCondition implements Condition {
  
  private final DeterministicThreadScheduler scheduler;
  private final Lock lock;
  private final Deque<Integer> waitingThreadIds = new ArrayDeque<>();

  public DeterministicCondition(DeterministicThreadScheduler scheduler, Lock lock) {
    this.scheduler = scheduler;
    this.lock = lock;
  }
  
  public List<Integer> getWaitingThreadIds() {
    return new ArrayList<>(waitingThreadIds);
  }

  @Override
  public void await() throws InterruptedException {
    int myThreadId = scheduler.getCurrentThreadId();
    checkState(scheduler.getThread(myThreadId).getState().compareAndSet(READY, WAITING));
    waitingThreadIds.add(myThreadId);
    lock.unlock();
    try {
      scheduler.yield();
    } finally {
      lock.lock();
    }
  }

  @Override
  public void awaitUninterruptibly() {
    throw new UnsupportedOperationException();
  }

  @Override
  public long awaitNanos(long nanosTimeout) throws InterruptedException {
    throw new UnsupportedOperationException();
  }

  @Override
  public boolean await(long time, TimeUnit unit) throws InterruptedException {
    throw new UnsupportedOperationException();
  }

  @Override
  public boolean awaitUntil(@NotNull Date deadline) throws InterruptedException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void signal() {
    Integer polled = waitingThreadIds.pollFirst();
    if (polled == null) {
      return;
    }
    signalThreadId(polled);
  }

  @Override
  public void signalAll() {
    List<Integer> allWaiting = new ArrayList<>(waitingThreadIds);
    for (int waitingId : allWaiting) {
      ParticipatingThread waitingThread = scheduler.getThread(waitingId);
      checkState(waitingThread.getState().get() == WAITING, "Thread %s was not waiting", waitingThread.getThread().getName());
    }
    for (int waitingId : allWaiting) {
      if (waitingThreadIds.contains(waitingId)) {
        waitingThreadIds.remove(waitingId);
        signalThreadId(waitingId);
      }
    }
  }

  private void signalThreadId(int polledId) {
    ParticipatingThread polledThread = scheduler.getThread(polledId);
    checkState(polledThread.getState().compareAndSet(WAITING, READY), "Thread %s was not waiting", polledThread.getThread().getName());
    lock.unlock();
    try {
      if (polledThread.getState().get() == READY) {
        scheduler.yield(Option.some(polledId), Option.none());
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    } finally {
      lock.lock();
    }
  }
  
}
