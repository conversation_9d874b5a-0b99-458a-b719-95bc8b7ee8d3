package com.kaching.util.ftp;

import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPSClient;
import org.apache.ftpserver.FtpServer;
import org.apache.ftpserver.FtpServerFactory;
import org.apache.ftpserver.ftplet.FtpException;
import org.apache.ftpserver.listener.Listener;
import org.apache.ftpserver.listener.ListenerFactory;
import org.apache.ftpserver.ssl.SslConfigurationFactory;
import org.apache.ftpserver.usermanager.ClearTextPasswordEncryptor;
import org.apache.ftpserver.usermanager.PropertiesUserManagerFactory;
import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;

@Ignore("Ignore test until we roll keys")
public class FtpsClientIntegrationTest {

  private static final Log log = Log.getLog(FtpsClientIntegrationTest.class);

  FtpServer server;
  int port;
  Path tmpFile;

  @Before
  public void before() throws FtpException, IOException {
    Pair<FtpServer, Integer> ftpServerAndPort = getStartedFtpServerAndPort();
    server = ftpServerAndPort.getLeft();
    port = ftpServerAndPort.getRight();

    tmpFile = Paths.get("./ftps.tmp");
    Files.write(tmpFile, Strings.format("Created by %s", FtpsClientIntegrationTest.class.getName()).getBytes());
  }

  @After
  public void tearDown() throws IOException {
    server.stop();
    Files.deleteIfExists(tmpFile);
  }

  @Test
  public void download() throws Exception {
    FtpsClient ftpsClient1 = new FtpsClient("localhost", "admin", "admin");
    try {
      ftpsClient1.setPort(port);
      ftpsClient1.connectetIfNeeded();
      assertTrue(ftpsClient1.isConnected());
      String content = IOUtils.toString(ftpsClient1.download("ftps.tmp")
          .getOrThrow());
      assertThat(content, is("Created by com.kaching.util.ftp.FtpsClientIntegrationTest"));
    } finally {
      ftpsClient1.quit();
    }
  }

  @Test
  @Ignore("exploratory")
  public void apacheClient() throws Exception {
    FTPSClient ftpsClient = new FTPSClient();
    try {
      ftpsClient.setConnectTimeout(1000);
      ftpsClient.connect("localhost", port);
      assertTrue(ftpsClient.login("admin", "admin"));
      log.info(ftpsClient.printWorkingDirectory());
      ftpsClient.pasv();
      log.info(Stream.of(ftpsClient.listFiles())
          .map(FTPFile::toFormattedString)
          .collect(Collectors.joining("\n")));
      ftpsClient.setFileType(FTP.BINARY_FILE_TYPE);
      String s = IOUtils.toString(ftpsClient.retrieveFileStream("ftps.tmp"));
      log.info(s);
    } finally {
      ftpsClient.disconnect();
    }
  }

  private static Pair<FtpServer, Integer> getStartedFtpServerAndPort() throws FtpException {
    FtpServerFactory serverFactory = new FtpServerFactory();

    SslConfigurationFactory ssl = new SslConfigurationFactory();
    ssl.setKeystoreFile(new File("src/test/java/com/kaching/util/ftp/keystore.jks"));
    ssl.setKeystorePassword("password");

    ListenerFactory factory = new ListenerFactory();
    factory.setPort(0);
    factory.setSslConfiguration(ssl.createSslConfiguration());
    factory.setImplicitSsl(false);
    Listener listener = factory.createListener();
    serverFactory.addListener("default", listener);

    PropertiesUserManagerFactory userManagerFactory = new PropertiesUserManagerFactory();
    userManagerFactory.setPasswordEncryptor(new ClearTextPasswordEncryptor());
    userManagerFactory.setFile(new File("com/kaching/util/ftp/users.properties"));
    serverFactory.setUserManager(userManagerFactory.createUserManager());

    FtpServer server = serverFactory.createServer();
    server.start();
    return Pair.of(server, listener.getPort());
  }

}