package com.kaching.util.ip;

import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.*;

import org.junit.Test;

public class IpSetTest {

  @Test
  public void listedIps() {
    IpSet set = new IpSet("************", "*******");
    assertThat(set.contains("************"), is(false));
    assertThat(set.contains("************"), is(true));
    assertThat(set.contains("************"), is(false));
    assertThat(set.contains("*******"), is(false));
    assertThat(set.contains("*******"), is(true));
    assertThat(set.contains("*******"), is(false));
  }

  @Test
  public void unlistedIp() {
    IpSet emptySet = new IpSet();
    assertThat(emptySet.contains("0.0.0.0"), is(false));
    assertThat(emptySet.contains("************"), is(false));
    assertThat(emptySet.contains("***************"), is(false));
  }

  @Test
  public void ipv6Mapping() {
    IpSet ipv4set = new IpSet("***********");
    assertThat(ipv4set.contains("0:0:0:0:0:FFFF:***********"), is(true));
    assertThat(ipv4set.contains("::FFFF:***********"), is(true));

    IpSet ipv6set = new IpSet("0:0:0:0:0:FFFF:***********");
    assertThat(ipv6set.contains("***********"), is(true));
  }

  @Test
  public void specialIpsAreNotTreatedSpecially() {
    IpSet specifiedNetwork = new IpSet("********", "*********", "************");
    assertThat(specifiedNetwork.contains("********"), is(true));
    assertThat(specifiedNetwork.contains("********"), is(false));
    assertThat(specifiedNetwork.contains("*********"), is(true));
    assertThat(specifiedNetwork.contains("*********"), is(false));
    assertThat(specifiedNetwork.contains("************"), is(true));
    assertThat(specifiedNetwork.contains("************"), is(false));

    IpSet allHostsOnSpecifiedNetwork =
        new IpSet("**************", "*************", "**************");
    assertThat(allHostsOnSpecifiedNetwork.contains("**************"), is(true));
    assertThat(allHostsOnSpecifiedNetwork.contains("**************"), is(false));
    assertThat(allHostsOnSpecifiedNetwork.contains("*************"), is(true));
    assertThat(allHostsOnSpecifiedNetwork.contains("*************"), is(false));
    assertThat(allHostsOnSpecifiedNetwork.contains("**************"), is(true));
    assertThat(allHostsOnSpecifiedNetwork.contains("**************"), is(false));

    IpSet allHostsOnTheNetwork = new IpSet("***************");
    assertThat(allHostsOnTheNetwork.contains("***************"), is(true));
    assertThat(allHostsOnTheNetwork.contains("***************"), is(false));
    assertThat(allHostsOnTheNetwork.contains("***********"), is(false));
    assertThat(allHostsOnTheNetwork.contains("**********"), is(false));
    assertThat(allHostsOnTheNetwork.contains("**************"), is(false));
  }

  @Test
  public void constructorThrowsWithInvalidIps() {
    assertConstructorThrowsIllegalArgumentException("256.0.0.1");
    assertConstructorThrowsIllegalArgumentException("127.0.0.1", "127.0.0");
    assertConstructorThrowsIllegalArgumentException("");
    assertConstructorThrowsIllegalArgumentException("sandwich");
    assertConstructorThrowsIllegalArgumentException("google.com");
  }

  @Test
  public void allowsThrowsWithInvalidIps() {
    assertAllowsThrowsIllegalArgumentException("256.0.0.1");
    assertAllowsThrowsIllegalArgumentException("127.0.0");
    assertAllowsThrowsIllegalArgumentException("");
    assertAllowsThrowsIllegalArgumentException("sandwich");
    assertAllowsThrowsIllegalArgumentException("google.com");
  }

  private static void assertAllowsThrowsIllegalArgumentException(String arg) {
    try {
      IpSet set = new IpSet();
      set.contains(arg);
      fail("Expected IllegalArgumentException");
    } catch (IllegalArgumentException ex) {
      assertThat(ex.getMessage(), containsString("is not an IP string literal."));
    }
  }

  private static void assertConstructorThrowsIllegalArgumentException(String... args) {
    try {
      new IpSet(args);
      fail("Expected IllegalArgumentException");
    } catch (IllegalArgumentException ex) {
      assertThat(ex.getMessage(), containsString("is not an IP string literal."));
    }
  }

}