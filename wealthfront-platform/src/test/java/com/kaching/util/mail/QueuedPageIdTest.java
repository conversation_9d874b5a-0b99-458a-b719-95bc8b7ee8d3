package com.kaching.util.mail;

import static com.twolattes.json.Json.number;
import static com.wealthfront.test.Assert.assertSameJson;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

public class QueuedPageIdTest {

  @Test
  public void convert() {
    QueuedPageId.Converter converter = new QueuedPageId.Converter();
    assertEquals("123", converter.nonNullableToString(new QueuedPageId(123)));
    assertEquals(new QueuedPageId(123), converter.fromNonNullableString("123"));
  }

  @Test
  public void marshall() {
    QueuedPageId.JsonType type = new QueuedPageId.JsonType();
    assertSameJson(number(123), type.nullSafeMarshall(new QueuedPageId(123)));
    assertEquals(new QueuedPageId(123), type.nullSafeUnmarshall(number(123)));
  }

}