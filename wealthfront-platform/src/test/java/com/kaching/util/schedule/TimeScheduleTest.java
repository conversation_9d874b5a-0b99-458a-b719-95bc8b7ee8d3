package com.kaching.util.schedule;

import static com.wealthfront.util.time.DateTimeZones.ET;
import static com.wealthfront.util.time.DateTimeZones.PT;
import static com.wealthfront.util.time.DateTimeZones.UTC;
import static org.junit.Assert.assertEquals;

import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;

import com.google.common.collect.ImmutableList;

public class TimeScheduleTest {

  public static void assertConsistentBehavior(TimeSchedule ts) {
    for (DateTimeZone tz : ImmutableList.of(ET, PT, UTC)) {
      for (DateTime dateTime = new DateTime(2023, 2, 13, 0, 0, tz);
           dateTime.isBefore(new DateTime(2024, 4, 1, 0, 0, tz));
           dateTime = dateTime.plusMinutes(37)) {
        assertEquals(
            "expected includes() to be consistent with actual getNextAllowedInstant() for " + dateTime,
            ts.includes(dateTime),
            dateTime.compareTo(ts.getNextAllowedInstant(dateTime)) == 0
        );
      }
    }
  }

}