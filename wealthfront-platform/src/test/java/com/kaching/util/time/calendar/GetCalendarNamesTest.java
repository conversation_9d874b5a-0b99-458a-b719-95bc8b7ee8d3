package com.kaching.util.time.calendar;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.junit.After;
import org.junit.Test;

import com.google.inject.Guice;
import com.google.inject.Key;
import com.google.inject.name.Names;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.util.http.LongOnlineTimeoutPooling;
import com.wealthfront.util.time.calendar.Calendar;
import com.wealthfront.util.time.calendar.CalendarModule;

public class GetCalendarNamesTest {

  private final WFMockery mockery = Mockeries.mockery();
  private final Calendar calendar = mockery.mock(Calendar.class);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process_findsEndOfDayProcess() {
    assertTrue(getQuery().process().contains("EndOfDayProcess"));
  }

  @Test
  public void process_doesNotFindString() {
    assertFalse(getQuery().process().contains("ABC"));
  }

  @Test
  public void process_findsCustom() {
    assertTrue(getQuery().process().contains("DEF"));
  }

  private GetCalendarNames getQuery() {
    GetCalendarNames query = new GetCalendarNames();
    query.injector = Guice.createInjector(binder -> {
      binder.install(new CalendarModule());

      binder.bind(Calendar.class).toInstance(calendar);
      binder.bind(Key.get(Calendar.class, LongOnlineTimeoutPooling.class)).toInstance(calendar);

      binder.bind(String.class).toInstance("hello there!");
      binder.bind(Key.get(String.class, Names.named("ABC"))).toInstance("hi!");

      binder.bind(Key.get(Calendar.class, Names.named("DEF"))).toInstance(calendar);
    });
    return query;
  }

}