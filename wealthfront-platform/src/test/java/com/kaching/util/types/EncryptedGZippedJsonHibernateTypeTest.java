package com.kaching.util.types;

import com.kaching.security.Cipher;
import com.kaching.security.Ciphers;
import org.junit.Test;

import java.security.SecureRandom;

import static com.google.common.base.Charsets.UTF_8;
import static org.junit.Assert.assertEquals;

public class EncryptedGZippedJsonHibernateTypeTest {

  @Test
  public void testEncrypt() throws Exception {
    SecureRandom random = new SecureRandom();
    byte[] key = new byte[256 / 8];
    random.nextBytes(key);
    final Cipher cipher = Ciphers.aes(key, random);
    EncryptedGZippedJsonHibernateType type = new EncryptedGZippedJsonHibernateType() {
      @Override
      protected Cipher injectCipher() {
        return cipher;
      }
    };
    assertEquals("hello, world.",
        new String(type.decrypt(type.encrypt(new String("hello, world.").getBytes(UTF_8))), UTF_8));
  }

  @Test
  public void testProcess() throws Exception {
    SecureRandom random = new SecureRandom();
    byte[] key = new byte[256 / 8];
    random.nextBytes(key);
    final Cipher cipher = Ciphers.aes(key, random);
    EncryptedGZippedJsonHibernateType type = new EncryptedGZippedJsonHibernateType() {
      @Override
      protected Cipher injectCipher() {
        return cipher;
      }
    };
    assertEquals("hello, world.", type.preProcess(type.postProcess(new String("hello, world."))));
  }

}
