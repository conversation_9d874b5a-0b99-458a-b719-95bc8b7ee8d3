package com.kaching.util.types;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.twolattes.json.Json;

public class JsonValueConverterTest {

  private JsonValueConverter converter;

  public JsonValueConverterTest() {
    converter = new JsonValueConverter();
  }

  @Test
  public void convert() throws Exception {
    convertBackAndForth(Json.object());
    Json.Object object = Json.object();
    object.put(Json.string("a"), Json.number(42));
    object.put(Json.string("b"), Json.string("foo"));
    convertBackAndForth(object);
  }

  private void convertBackAndForth(Json.Object value) throws Exception {
    assertEquals(value, converter.fromString(converter.toString(value)));
  }

}
