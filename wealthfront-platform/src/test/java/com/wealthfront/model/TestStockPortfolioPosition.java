package com.wealthfront.model;

import java.math.BigDecimal;
import java.util.Objects;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;

@com.fasterxml.jackson.annotation.JsonClassDescription("")
@com.kaching.api.ExposeType(value = {com.kaching.api.ExposeTo.LOCAL, com.kaching.api.ExposeTo.BACKEND,
    com.kaching.api.ExposeTo.API_SERVER,
    com.kaching.api.ExposeTo.FRONTEND}, namespace = com.kaching.api.ExposeType.RewriteNamespace.DO_NOT_COPY)
public class TestStockPortfolioPosition {

  private static final com.kaching.platform.common.logging.Log log =
      com.kaching.platform.common.logging.Log.getLog("com.wealthfront.model.nullabilityLogging");

  @Nonnull
  @JsonProperty("instrumentId")
  private Long instrumentId = null;
  @Nonnull
  @JsonProperty("quantity")
  private BigDecimal quantity = null;
  @Nonnull
  @JsonProperty("marketValue")
  private BigDecimal marketValue = null;
  @Nonnull
  @JsonProperty("percentageWeight")
  private BigDecimal percentageWeight = null;
  @Nullable
  @JsonProperty("someAbstractPositionDetails")
  private AbstractPositionDetails someAbstractPositionDetails = null;
  @Nullable
  @JsonProperty("nonNullAbstractPositionDetails")
  private AbstractPositionDetails nonNullAbstractPositionDetails = null;

  public TestStockPortfolioPosition() {}

  private TestStockPortfolioPosition(
      @Nonnull Long instrumentId,
      @Nonnull BigDecimal quantity,
      @Nonnull BigDecimal marketValue,
      @Nonnull BigDecimal percentageWeight,
      @Nullable AbstractPositionDetails someAbstractPositionDetails,
      @Nullable AbstractPositionDetails nonNullAbstractPositionDetails
  ) {
    this.instrumentId = instrumentId;
    this.quantity = quantity;
    this.marketValue = marketValue;
    this.percentageWeight = percentageWeight;
    this.someAbstractPositionDetails = someAbstractPositionDetails;
    this.nonNullAbstractPositionDetails = nonNullAbstractPositionDetails;
  }

  /**
   * Get instrumentId
   *
   * @return instrumentId
   **/
  @Nonnull
  @ApiModelProperty(required = true, value = "")
  public Long getInstrumentId() {
    return instrumentId;
  }

  /**
   * Get quantity
   *
   * @return quantity
   **/
  @Nonnull
  @ApiModelProperty(required = true, value = "")
  public BigDecimal getQuantity() {
    return quantity;
  }

  /**
   * Get marketValue
   *
   * @return marketValue
   **/
  @Nonnull
  @ApiModelProperty(required = true, value = "")
  public BigDecimal getMarketValue() {
    return marketValue;
  }

  /**
   * Get percentageWeight
   *
   * @return percentageWeight
   **/
  @Nonnull
  @ApiModelProperty(required = true, value = "")
  public BigDecimal getPercentageWeight() {
    return percentageWeight;
  }

  /**
   * Get someAbstractPositionDetails
   *
   * @return someAbstractPositionDetails
   **/
  @Nullable
  @ApiModelProperty(value = "")
  public AbstractPositionDetails getSomeAbstractPositionDetails() {
    return someAbstractPositionDetails;
  }

  /**
   * Get nonNullAbstractPositionDetails
   *
   * @return nonNullAbstractPositionDetails
   **/
  @Nullable
  @ApiModelProperty(value = "")
  public AbstractPositionDetails getNonNullAbstractPositionDetails() {
    return nonNullAbstractPositionDetails;
  }

  String nullabilityLogString() {
    StringBuilder sb = new StringBuilder();
    sb.append("nullability log for class StockPortfolioPosition {");
    sb.append("instrumentId:").append(instrumentId == null ? "null" : "present").append(",");
    sb.append("quantity:").append(quantity == null ? "null" : "present").append(",");
    sb.append("marketValue:").append(marketValue == null ? "null" : "present").append(",");
    sb.append("percentageWeight:").append(percentageWeight == null ? "null" : "present").append(",");
    sb.append("someAbstractPositionDetails:").append(someAbstractPositionDetails == null ? "null" : "present")
        .append(",");
    sb.append("nonNullAbstractPositionDetails:").append(nonNullAbstractPositionDetails == null ? "null" : "present")
        .append(",");
    sb.setLength(sb.length() - 1);
    sb.append("}");
    return sb.toString();
  }

  void validate() {
    if (instrumentId == null) {
      throw new IllegalArgumentException("StockPortfolioPosition: Required property instrumentId is null");
    }
    if (quantity == null) {
      throw new IllegalArgumentException("StockPortfolioPosition: Required property quantity is null");
    }
    if (marketValue == null) {
      throw new IllegalArgumentException("StockPortfolioPosition: Required property marketValue is null");
    }
    if (percentageWeight == null) {
      throw new IllegalArgumentException("StockPortfolioPosition: Required property percentageWeight is null");
    }
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class StockPortfolioPosition {\n");

    sb.append("    instrumentId: ").append(instrumentId).append("\n");
    sb.append("    quantity: ").append(quantity).append("\n");
    sb.append("    marketValue: ").append(marketValue).append("\n");
    sb.append("    percentageWeight: ").append(percentageWeight).append("\n");
    sb.append("    someAbstractPositionDetails: ").append(someAbstractPositionDetails).append("\n");
    sb.append("    nonNullAbstractPositionDetails: ").append(nonNullAbstractPositionDetails).append("\n");
    sb.append("}");
    return sb.toString();
  }

  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestStockPortfolioPosition testStockPortfolioPosition = (TestStockPortfolioPosition) o;
    return Objects.equals(this.instrumentId, testStockPortfolioPosition.instrumentId) &&
        Objects.equals(this.quantity == null ? null : this.quantity.stripTrailingZeros(),
            testStockPortfolioPosition.quantity == null ? null : testStockPortfolioPosition.quantity.stripTrailingZeros()) &&
        Objects.equals(this.marketValue == null ? null : this.marketValue.stripTrailingZeros(),
            testStockPortfolioPosition.marketValue == null ? null :
                testStockPortfolioPosition.marketValue.stripTrailingZeros()) &&
        Objects.equals(this.percentageWeight == null ? null : this.percentageWeight.stripTrailingZeros(),
            testStockPortfolioPosition.percentageWeight == null ? null :
                testStockPortfolioPosition.percentageWeight.stripTrailingZeros()) &&
        Objects.equals(this.someAbstractPositionDetails, testStockPortfolioPosition.someAbstractPositionDetails) &&
        Objects.equals(this.nonNullAbstractPositionDetails, testStockPortfolioPosition.nonNullAbstractPositionDetails);
  }

  @Override
  public int hashCode() {
    return Objects.hash(instrumentId, quantity == null ? null : quantity.stripTrailingZeros(),
        marketValue == null ? null : marketValue.stripTrailingZeros(),
        percentageWeight == null ? null : percentageWeight.stripTrailingZeros(), someAbstractPositionDetails,
        nonNullAbstractPositionDetails);
  }

  public Builder copy() {
    return with(this);
  }

  public static Builder with() {
    return new Builder();
  }

  public static Builder with(TestStockPortfolioPosition that) {
    Builder builder = new TestStockPortfolioPosition.Builder();
    builder
        .instrumentId(that.getInstrumentId())
        .quantity(that.getQuantity())
        .marketValue(that.getMarketValue())
        .percentageWeight(that.getPercentageWeight())
        .someAbstractPositionDetails(that.getSomeAbstractPositionDetails())
        .nonNullAbstractPositionDetails(that.getNonNullAbstractPositionDetails());
    return builder;
  }

  public static class Builder {

    @Nonnull
    private Long instrumentId = null;
    @Nonnull
    private BigDecimal quantity = null;
    @Nonnull
    private BigDecimal marketValue = null;
    @Nonnull
    private BigDecimal percentageWeight = null;
    @Nullable
    private AbstractPositionDetails someAbstractPositionDetails = null;
    @Nullable
    private AbstractPositionDetails nonNullAbstractPositionDetails = null;

    public Builder instrumentId(@Nonnull Long instrumentId) {
      this.instrumentId = instrumentId;
      return this;
    }

    public Builder quantity(@Nonnull BigDecimal quantity) {
      this.quantity = quantity;
      return this;
    }

    public Builder marketValue(@Nonnull BigDecimal marketValue) {
      this.marketValue = marketValue;
      return this;
    }

    public Builder percentageWeight(@Nonnull BigDecimal percentageWeight) {
      this.percentageWeight = percentageWeight;
      return this;
    }

    public Builder someAbstractPositionDetails(@Nullable AbstractPositionDetails someAbstractPositionDetails) {
      this.someAbstractPositionDetails = someAbstractPositionDetails;
      return this;
    }

    public Builder nonNullAbstractPositionDetails(@Nullable AbstractPositionDetails nonNullAbstractPositionDetails) {
      this.nonNullAbstractPositionDetails = nonNullAbstractPositionDetails;
      return this;
    }

    public TestStockPortfolioPosition build() {
      TestStockPortfolioPosition val =
          new TestStockPortfolioPosition(instrumentId, quantity, marketValue, percentageWeight, someAbstractPositionDetails,
              nonNullAbstractPositionDetails);
      log.debug(val.nullabilityLogString());
      val.validate();
      return val;
    }

    public TestStockPortfolioPosition buildForTesting() {
      return new TestStockPortfolioPosition(instrumentId, quantity, marketValue, percentageWeight,
          someAbstractPositionDetails, nonNullAbstractPositionDetails);
    }

    @Override
    public boolean equals(java.lang.Object o) {
      if (this == o) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      Builder builder = (Builder) o;
      return Objects.equals(this.instrumentId, builder.instrumentId) &&
          Objects.equals(this.quantity == null ? null : this.quantity.stripTrailingZeros(),
              builder.quantity == null ? null : builder.quantity.stripTrailingZeros()) &&
          Objects.equals(this.marketValue == null ? null : this.marketValue.stripTrailingZeros(),
              builder.marketValue == null ? null : builder.marketValue.stripTrailingZeros()) &&
          Objects.equals(this.percentageWeight == null ? null : this.percentageWeight.stripTrailingZeros(),
              builder.percentageWeight == null ? null : builder.percentageWeight.stripTrailingZeros()) &&
          Objects.equals(this.someAbstractPositionDetails, builder.someAbstractPositionDetails) &&
          Objects.equals(this.nonNullAbstractPositionDetails, builder.nonNullAbstractPositionDetails);
    }

    @Override
    public int hashCode() {
      return Objects.hash(instrumentId, quantity == null ? null : quantity.stripTrailingZeros(),
          marketValue == null ? null : marketValue.stripTrailingZeros(),
          percentageWeight == null ? null : percentageWeight.stripTrailingZeros(), someAbstractPositionDetails,
          nonNullAbstractPositionDetails);
    }

  }

}

