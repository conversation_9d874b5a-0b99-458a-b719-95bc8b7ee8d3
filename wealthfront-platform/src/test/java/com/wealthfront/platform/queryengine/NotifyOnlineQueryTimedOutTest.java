package com.wealthfront.platform.queryengine;

import static com.kaching.Author.BANKING_PLATFORM_TEAM;
import static com.kaching.Author.INVESTMENT_SERVICES_TEAM;
import static com.kaching.platform.functional.Unit.unit;
import static com.kaching.util.mail.Pager.Device.PAGER_BANKING_PLATFORM;
import static com.kaching.util.mail.Pager.Device.PAGER_INVESTMENT_SERVICES;
import static com.kaching.util.schedule.PagingSchedules.OFFICE_HOURS;
import static com.kaching.util.schedule.PagingSchedules.OPS_IS_AWAKE_HOURS;
import static com.kaching.util.schedule.PagingSchedules.TWENTY_FOUR_SEVEN;
import static com.wealthfront.util.time.DateTimeZones.ET;

import org.joda.time.DateTime;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.google.inject.Guice;
import com.google.inject.util.Providers;
import com.kaching.monitor.esp.ResponsiblePartyFinder;
import com.kaching.platform.discovery.ServiceDescriptor;
import com.kaching.platform.discovery.ServiceId;
import com.kaching.platform.functional.Unit;
import com.kaching.platform.guice.KachingServices.BANK;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.QueryProxies;
import com.kaching.platform.queryengine.QueryProxy;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;
import com.kaching.util.functional.Pointer;
import com.kaching.util.mail.DelayedPager;

public class NotifyOnlineQueryTimedOutTest {

  private static final DateTime now = new DateTime(2024, 1, 2, 3, 4, 5, ET);

  private final WFMockery mockery = Mockeries.mockery(true);
  private final QueryProxies queryProxies = mockery.mock(QueryProxies.class);
  private final InMemoryOnlineQueryTimeoutHandler timeoutHandler =
      mockery.mock(InMemoryOnlineQueryTimeoutHandler.class);
  private final Pointer<NotifyOnlineQueryTimedOut.LastPageConfig> lastPageConfig = Pointer.pointer();
  private final DelayedPager delayedPager = mockery.mock(DelayedPager.class);
  private final ResponsiblePartyFinder responsiblePartyFinder = mockery.mock(ResponsiblePartyFinder.class);

  @Before
  public void before() {
    lastPageConfig.set(null);
  }

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process_notLeader_doesNothing() {
    NotifyOnlineQueryTimedOut query = getQuery("SomeQuery", false);
    query.isLeader = Providers.of(false);
    query.process();
  }

  @Test
  public void process_integrationServer_doesNothing() {
    NotifyOnlineQueryTimedOut query = getQuery("SomeQuery", true);
    query.process();
  }

  @Test
  public void process_handlerPass() {
    mockery.checking(new WExpectations() {{
      oneOf(queryProxies).getProxy("SomeQuery");
      will(returnValue(new QueryProxy<>(SomeQuery.class, null)));

      oneOf(responsiblePartyFinder).findResponsibleParties(BANK.class, SomeQuery.class);
      will(returnList(BANKING_PLATFORM_TEAM));

      oneOf(timeoutHandler).handleQueryTimeout(SomeQuery.class);
      will(returnNone());
    }});
    getQuery("SomeQuery", false).process();
  }

  @Test
  public void process_respectsPagerFrequency() {
    mockery.checking(new WExpectations() {{
      oneOf(queryProxies).getProxy("SomeQuery");
      will(returnValue(new QueryProxy<>(SomeQuery.class, null)));

      oneOf(responsiblePartyFinder).findResponsibleParties(BANK.class, SomeQuery.class);
      will(returnList(BANKING_PLATFORM_TEAM));

      oneOf(timeoutHandler).handleQueryTimeout(SomeQuery.class);
      will(returnSome(InMemoryOnlineQueryTimeoutHandler.PagingSchedule.IMMEDIATE));

      oneOf(delayedPager).alert(
          "Online Query Timed Out for BANKING_PLATFORM_TEAM",
          "Query SomeQuery timed out. See logger_name:\"com.wealthfront.platform.queryengine.NotifyOnlineQueryTimedOut\" logs for details",
          PAGER_BANKING_PLATFORM,
          TWENTY_FOUR_SEVEN
      );
    }});
    NotifyOnlineQueryTimedOut query = getQuery("SomeQuery", false);
    query.process();
    mockery.assertIsSatisfied();

    query.now = query.now.plusMinutes(9).plusSeconds(59);
    mockery.checking(new WExpectations() {{
      oneOf(queryProxies).getProxy("SomeQuery");
      will(returnValue(new QueryProxy<>(SomeQuery.class, null)));

      oneOf(responsiblePartyFinder).findResponsibleParties(BANK.class, SomeQuery.class);
      will(returnList(BANKING_PLATFORM_TEAM));

      oneOf(timeoutHandler).handleQueryTimeout(SomeQuery.class);
      will(returnSome(InMemoryOnlineQueryTimeoutHandler.PagingSchedule.IMMEDIATE));
    }});
    query.process();
    mockery.assertIsSatisfied();

    query.now = query.now.plusSeconds(1);
    mockery.checking(new WExpectations() {{
      oneOf(queryProxies).getProxy("SomeQuery");
      will(returnValue(new QueryProxy<>(SomeQuery.class, null)));

      oneOf(responsiblePartyFinder).findResponsibleParties(BANK.class, SomeQuery.class);
      will(returnList(BANKING_PLATFORM_TEAM));

      oneOf(timeoutHandler).handleQueryTimeout(SomeQuery.class);
      will(returnSome(InMemoryOnlineQueryTimeoutHandler.PagingSchedule.IMMEDIATE));

      oneOf(delayedPager).alert(
          "Online Query Timed Out for BANKING_PLATFORM_TEAM",
          "Query SomeQuery timed out. See logger_name:\"com.wealthfront.platform.queryengine.NotifyOnlineQueryTimedOut\" logs for details",
          PAGER_BANKING_PLATFORM,
          TWENTY_FOUR_SEVEN
      );
    }});
    query.process();
  }

  @Test
  public void process_multipleRotations() {
    mockery.checking(new WExpectations() {{
      oneOf(queryProxies).getProxy("SomeQuery");
      will(returnValue(new QueryProxy<>(SomeQuery.class, null)));

      oneOf(responsiblePartyFinder).findResponsibleParties(BANK.class, SomeQuery.class);
      will(returnList(INVESTMENT_SERVICES_TEAM, BANKING_PLATFORM_TEAM));

      oneOf(timeoutHandler).handleQueryTimeout(SomeQuery.class);
      will(returnSome(InMemoryOnlineQueryTimeoutHandler.PagingSchedule.IMMEDIATE));

      oneOf(delayedPager).alert(
          "Online Query Timed Out for BANKING_PLATFORM_TEAM, INVESTMENT_SERVICES_TEAM",
          "Query SomeQuery timed out. See logger_name:\"com.wealthfront.platform.queryengine.NotifyOnlineQueryTimedOut\" logs for details",
          PAGER_BANKING_PLATFORM,
          TWENTY_FOUR_SEVEN
      );

      oneOf(delayedPager).alert(
          "Online Query Timed Out for BANKING_PLATFORM_TEAM, INVESTMENT_SERVICES_TEAM",
          "Query SomeQuery timed out. See logger_name:\"com.wealthfront.platform.queryengine.NotifyOnlineQueryTimedOut\" logs for details",
          PAGER_INVESTMENT_SERVICES,
          TWENTY_FOUR_SEVEN
      );
    }});
    NotifyOnlineQueryTimedOut query = getQuery("SomeQuery", false);
    query.process();
  }

  @Test
  public void process_followingHigherPrioritySchedule_pagesImmediately() {
    mockery.checking(new WExpectations() {{
      oneOf(queryProxies).getProxy("SomeQuery");
      will(returnValue(new QueryProxy<>(SomeQuery.class, null)));

      oneOf(responsiblePartyFinder).findResponsibleParties(BANK.class, SomeQuery.class);
      will(returnList(BANKING_PLATFORM_TEAM));

      oneOf(timeoutHandler).handleQueryTimeout(SomeQuery.class);
      will(returnSome(InMemoryOnlineQueryTimeoutHandler.PagingSchedule.OFFICE_HOURS));

      oneOf(delayedPager).alert(
          "Online Query Timed Out for BANKING_PLATFORM_TEAM",
          "Query SomeQuery timed out. See logger_name:\"com.wealthfront.platform.queryengine.NotifyOnlineQueryTimedOut\" logs for details",
          PAGER_BANKING_PLATFORM,
          OFFICE_HOURS
      );
    }});
    NotifyOnlineQueryTimedOut query = getQuery("SomeQuery", false);
    query.process();
    mockery.assertIsSatisfied();

    mockery.checking(new WExpectations() {{
      oneOf(queryProxies).getProxy("SomeQuery");
      will(returnValue(new QueryProxy<>(SomeQuery.class, null)));

      oneOf(responsiblePartyFinder).findResponsibleParties(BANK.class, SomeQuery.class);
      will(returnList(BANKING_PLATFORM_TEAM));

      oneOf(timeoutHandler).handleQueryTimeout(SomeQuery.class);
      will(returnSome(InMemoryOnlineQueryTimeoutHandler.PagingSchedule.OPS_IS_AWAKE));

      oneOf(delayedPager).alert(
          "Online Query Timed Out for BANKING_PLATFORM_TEAM",
          "Query SomeQuery timed out. See logger_name:\"com.wealthfront.platform.queryengine.NotifyOnlineQueryTimedOut\" logs for details",
          PAGER_BANKING_PLATFORM,
          OPS_IS_AWAKE_HOURS
      );
    }});
    query.process();
    mockery.assertIsSatisfied();

    mockery.checking(new WExpectations() {{
      oneOf(queryProxies).getProxy("SomeQuery");
      will(returnValue(new QueryProxy<>(SomeQuery.class, null)));

      oneOf(responsiblePartyFinder).findResponsibleParties(BANK.class, SomeQuery.class);
      will(returnList(BANKING_PLATFORM_TEAM));

      oneOf(timeoutHandler).handleQueryTimeout(SomeQuery.class);
      will(returnSome(InMemoryOnlineQueryTimeoutHandler.PagingSchedule.OPS_IS_AWAKE));
    }});
    query.process();
    mockery.assertIsSatisfied();

    mockery.checking(new WExpectations() {{
      oneOf(queryProxies).getProxy("SomeQuery");
      will(returnValue(new QueryProxy<>(SomeQuery.class, null)));

      oneOf(responsiblePartyFinder).findResponsibleParties(BANK.class, SomeQuery.class);
      will(returnList(BANKING_PLATFORM_TEAM));

      oneOf(timeoutHandler).handleQueryTimeout(SomeQuery.class);
      will(returnSome(InMemoryOnlineQueryTimeoutHandler.PagingSchedule.IMMEDIATE));

      oneOf(delayedPager).alert(
          "Online Query Timed Out for BANKING_PLATFORM_TEAM",
          "Query SomeQuery timed out. See logger_name:\"com.wealthfront.platform.queryengine.NotifyOnlineQueryTimedOut\" logs for details",
          PAGER_BANKING_PLATFORM,
          TWENTY_FOUR_SEVEN
      );
    }});
    query.process();
  }

  @Test
  public void process_lowerPriorityDoesNotPage() {
    mockery.checking(new WExpectations() {{
      oneOf(queryProxies).getProxy("SomeQuery");
      will(returnValue(new QueryProxy<>(SomeQuery.class, null)));

      oneOf(responsiblePartyFinder).findResponsibleParties(BANK.class, SomeQuery.class);
      will(returnList(BANKING_PLATFORM_TEAM));

      oneOf(timeoutHandler).handleQueryTimeout(SomeQuery.class);
      will(returnSome(InMemoryOnlineQueryTimeoutHandler.PagingSchedule.IMMEDIATE));

      oneOf(delayedPager).alert(
          "Online Query Timed Out for BANKING_PLATFORM_TEAM",
          "Query SomeQuery timed out. See logger_name:\"com.wealthfront.platform.queryengine.NotifyOnlineQueryTimedOut\" logs for details",
          PAGER_BANKING_PLATFORM,
          TWENTY_FOUR_SEVEN
      );
    }});
    NotifyOnlineQueryTimedOut query = getQuery("SomeQuery", false);
    query.process();
    mockery.assertIsSatisfied();

    mockery.checking(new WExpectations() {{
      oneOf(queryProxies).getProxy("SomeQuery");
      will(returnValue(new QueryProxy<>(SomeQuery.class, null)));

      oneOf(responsiblePartyFinder).findResponsibleParties(BANK.class, SomeQuery.class);
      will(returnList(BANKING_PLATFORM_TEAM));

      oneOf(timeoutHandler).handleQueryTimeout(SomeQuery.class);
      will(returnSome(InMemoryOnlineQueryTimeoutHandler.PagingSchedule.OPS_IS_AWAKE));
    }});
    query.process();
    mockery.assertIsSatisfied();

    mockery.checking(new WExpectations() {{
      oneOf(queryProxies).getProxy("SomeQuery");
      will(returnValue(new QueryProxy<>(SomeQuery.class, null)));

      oneOf(responsiblePartyFinder).findResponsibleParties(BANK.class, SomeQuery.class);
      will(returnList(BANKING_PLATFORM_TEAM));

      oneOf(timeoutHandler).handleQueryTimeout(SomeQuery.class);
      will(returnSome(InMemoryOnlineQueryTimeoutHandler.PagingSchedule.OFFICE_HOURS));
    }});
    query.process();
  }

  private NotifyOnlineQueryTimedOut getQuery(String queryName, boolean isIntegrationServer) {
    var query = new NotifyOnlineQueryTimedOut(queryName);
    query.isLeader = Providers.of(true);
    query.isIntegrationServer = isIntegrationServer;
    query.queryProxies = queryProxies;
    query.timeoutHandler = timeoutHandler;
    query.lastPageConfig = lastPageConfig;
    query.now = now;
    query.injector = Guice.createInjector(binder ->
        binder.bind(DelayedPager.class).toInstance(delayedPager)
    );
    query.responsiblePartyFinder = responsiblePartyFinder;
    query.serviceDescriptor = new ServiceDescriptor(new ServiceId("bank1"), BANK.class, null);
    return query;
  }

  private static class SomeQuery extends AbstractQuery<Unit> {

    @Override
    public Unit process() {
      return unit;
    }

  }

}