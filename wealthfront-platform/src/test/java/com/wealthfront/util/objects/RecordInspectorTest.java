package com.wealthfront.util.objects;

import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

public class RecordInspectorTest {

  private record MyRecord(int field1, String field2) {}

  @Test
  public void findGetter() throws NoSuchMethodException {
    assertEquals(MyRecord.class.getDeclaredMethod("field1"), RecordInspector.findGetter(MyRecord::field1));
    assertEquals(MyRecord.class.getDeclaredMethod("field2"), RecordInspector.findGetter(MyRecord::field2));
  }

  @Test
  public void findGetter_notAGetter_throws() {
    assertThrows(RuntimeException.class, () -> RecordInspector.findGetter((MyRecord myRecord) -> myRecord.field1()));
  }

}