package com.wealthfront.util.perf;

import com.kaching.platform.functional.Unchecked;
import com.kaching.platform.functional.Unchecked.ThrowingConsumer;
import com.kaching.platform.functional.Unchecked.ThrowingFunction;

public class FakeJfrRecorder implements JfrRecorder {

  @Override
  public <T> T scope(Config config, Unchecked.ThrowingSupplier<T> scope) {
    CmdJfrRecorder.validateUniqueName(config.uniqueName());
    return Unchecked.get(scope);
  }

  @Override
  public void scope(Config config, Unchecked.ThrowingRunnable scope) {
    CmdJfrRecorder.validateUniqueName(config.uniqueName());
    Unchecked.run(scope);
  }

  @Override
  public <T> T scope(Config config, ThrowingFunction<JfrContext, T> scope) {
    CmdJfrRecorder.validateUniqueName(config.uniqueName());
    return Unchecked.apply(jfrContext(config), scope);
  }

  @Override
  public void scope(Config config, ThrowingConsumer<JfrContext> scope) {
    CmdJfrRecorder.validateUniqueName(config.uniqueName());
    Unchecked.accept(jfrContext(config), scope);
  }

  @Override
  public JfrContext jfrContext(Config config) {
    CmdJfrRecorder.validateUniqueName(config.uniqueName());
    return new JfrContext() {
      @Override
      public void abort() {

      }

      @Override
      public void close() {

      }
    };
  }

}
