package com.wealthfront.util.perf;

import static org.junit.Assert.assertEquals;

import java.util.List;

import org.junit.Test;

public class StackTraceNodeTest {

  @Test
  public void getOrAddChild() {
    StackTraceNode root = new StackTraceNode(new StackTraceElement("java.lang.Thread", "run", null, -2));
    root.addCount(3);
    
    StackTraceNode node = root.getOrAddChild(new StackTraceElement("com.wealthfront.Something", "doSomething", "Something.java", 20));
    node = node.getOrAddChild(new StackTraceElement("com.wealthfront.SomethingElse", "doSomethingElse$$Lambda$17", "SomethingElse.java", 30));
    node.addCount(1);
    
    node = root.getOrAddChild(new StackTraceElement("com.wealthfront.Something", "doSomething", "Something.java", 23));
    node.addCount(1);
    
    node = root.getOrAddChild(new StackTraceElement("com.wealthfront.Something", "doSomething", "Something.java", 20));
    node.getOrAddChild(new StackTraceElement("com.wealthfront.SomethingDifferent", "doSomethingDifferent", "SomethingDifferent.java", 31));
    node.getOrAddChild(new StackTraceElement("com.wealthfront.SomethingA", "doSomethingDifferent", "SomethingDifferent.java", 19));
    node.getOrAddChild(new StackTraceElement("com.wealthfront.SomethingZ", "doSomethingDifferent", "SomethingDifferent.java", 19));
    node.getOrAddChild(new StackTraceElement("com.wealthfront.SomethingY", "doSomethingDifferent", "SomethingDifferent.java", 19));
    
    StackTraceNode expected = new StackTraceNode(
        new StackTraceElement("java.lang.Thread", "run", null, -2),
        3L,
        0L, List.of(
            new StackTraceNode(
                new StackTraceElement("com.wealthfront.Something", "doSomething", "Something.java", 20),
                0L,
                0L, List.of(
                    new StackTraceNode(
                        new StackTraceElement("com.wealthfront.SomethingA", "doSomethingDifferent", "SomethingDifferent.java", 19),
                        0L,
                        0L, List.of()
                    ),
                    new StackTraceNode(
                        new StackTraceElement("com.wealthfront.SomethingElse", "doSomethingElse$$Lambda$17", "SomethingElse.java", 30),
                        1L,
                        0L, List.of()
                    ),
                    new StackTraceNode(
                        new StackTraceElement("com.wealthfront.SomethingDifferent", "doSomethingDifferent", "SomethingDifferent.java", 31),
                        0L,
                        0L, List.of()
                    ),
                    new StackTraceNode(
                        new StackTraceElement("com.wealthfront.SomethingY", "doSomethingDifferent", "SomethingDifferent.java", 19),
                        0L,
                        0L, List.of()
                    ),
                    new StackTraceNode(
                        new StackTraceElement("com.wealthfront.SomethingZ", "doSomethingDifferent", "SomethingDifferent.java", 19),
                        0L,
                        0L, List.of()
                    )
                )
            ),
            new StackTraceNode(
                new StackTraceElement("com.wealthfront.Something", "doSomething", "Something.java", 23),
                1L,
                0L, List.of()
            )
        )
    );
    assertEquals(expected, root);
  }

}