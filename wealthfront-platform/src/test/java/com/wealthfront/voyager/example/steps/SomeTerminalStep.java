package com.wealthfront.voyager.example.steps;

import org.jetbrains.annotations.NotNull;

import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.Id;
import com.wealthfront.voyager.example.navigation.MortgageRoute;
import com.wealthfront.voyager.example.navigation.MortgageStepArguments.ApplicationArguments;
import com.wealthfront.voyager.example.payload.ExampleMortgageResult;
import com.wealthfront.voyager.example.views.ExampleView;
import com.wealthfront.voyager.model.VoyagerRecord;
import com.wealthfront.voyager.navigation.AppCompatibilityRequirement;
import com.wealthfront.voyager.navigation.TerminalVoyagerStep;
import com.wealthfront.voyager.navigation.VoyagerResult;
import com.wealthfront.voyager.navigation.VoyagerRoute;
import com.wealthfront.voyager.navigation.VoyagerStepId;

public class SomeTerminalStep implements TerminalVoyagerStep<ApplicationArguments> {

  @Override
  public ResultBuilder<ApplicationArguments> createStep(
      Id<VoyagerRecord> voyagerRecordId, VoyagerStepId previousStepId) {
    return new ResultBuilder<>(buildResult(), new ApplicationArguments(5));
  }

  @Override
  public VoyagerResult<?> resumeStep(
      Id<VoyagerRecord> voyagerRecordId, ApplicationArguments stepArguments) {
    return buildResult();
  }

  private VoyagerResult<?> buildResult() {
    return ExampleMortgageResult.builder()
        .withView(new ExampleView())
        .build();
  }

  @NotNull
  @Override
  public VoyagerRoute getRoute() {
    return MortgageRoute.APPLICATION_CANCELLED;
  }

  @Override
  public AppCompatibilityRequirement getCompatibilityRequirement() {
    return new AppCompatibilityRequirement(Option.some("2024.1.1"), Option.some("1000"));
  }

}
