package com.wealthfront.voyager.model;

import static com.kaching.DefaultKachingMarshallers.createMarshaller;
import static com.twolattes.json.Json.object;
import static com.wealthfront.test.Assert.assertMarshalling;
import static com.wealthfront.util.time.DateTimeZones.ET;

import org.joda.time.DateTime;
import org.junit.Test;

import com.kaching.platform.hibernate.Id;
import com.kaching.user.UserId;
import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;
import com.wealthfront.voyager.model.VoyagerRecord.State;
import com.wealthfront.voyager.navigation.VoyagerStepId;

public class VoyagerRecordViewTest {

  private static final DateTime NOW = new DateTime(2024, 12, 10, 0, 1, ET);
  private static final Marshaller<VoyagerRecordView> marshaller = createMarshaller(VoyagerRecordView.class);

  @Test
  public void marshalling() {
    VoyagerRecordView recordView = new VoyagerRecordView(
        Id.of(1),
        new UserId(2),
        VoyagerType.MORTGAGE_APPLICATION,
        VoyagerStepId.of("applications/1/loan-type"),
        State.ACTIVE,
        NOW
    );

    Json.Value expected = object(
        "id", 1,
        "userId", 2,
        "voyagerType", "MORTGAGE_APPLICATION",
        "currentStep", "applications/1/loan-type",
        "state", "ACTIVE",
        "createdAt", "2024-12-10 00:01:00.000"
    );

    assertMarshalling(marshaller, expected, recordView);
  }

}