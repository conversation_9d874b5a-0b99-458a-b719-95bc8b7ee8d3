package com.wealthfront.voyager.queries;

import static com.wealthfront.voyager.model.VoyagerRecord.State.ACTIVE;
import static com.wealthfront.voyager.model.VoyagerRecord.State.TERMINATED;
import static com.wealthfront.voyager.model.VoyagerRecordFactory.voyagerRecord;
import static com.wealthfront.voyager.model.VoyagerType.MORTGAGE_APPLICATION;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.junit.Assert.assertThat;

import java.util.List;

import org.junit.Test;

import com.kaching.platform.hibernate.ContentionSimulatingRetryingTransacter;
import com.kaching.user.UserId;
import com.wealthfront.voyager.VoyagerTestBase;
import com.wealthfront.voyager.model.VoyagerRecord;
import com.wealthfront.voyager.queries.GetActiveVoyagerRecordsForUser.VoyagerRecordDetails;

public class GetActiveVoyagerRecordsForUserTest extends VoyagerTestBase {

  @Test
  public void process() {
    UserId userId1 = new UserId(1);
    UserId userId2 = new UserId(2);

    VoyagerRecord record1 = voyagerRecord()
        .withUserId(userId1)
        .withVoyagerType(MORTGAGE_APPLICATION)
        .withState(ACTIVE)
        .buildAndPersist(transacter);
    VoyagerRecord record2 = voyagerRecord()
        .withUserId(userId1)
        .withVoyagerType(MORTGAGE_APPLICATION)
        .withState(ACTIVE)
        .buildAndPersist(transacter);
    VoyagerRecord record3 = voyagerRecord()
        .withUserId(userId1)
        .withVoyagerType(MORTGAGE_APPLICATION)
        .withState(TERMINATED)
        .buildAndPersist(transacter);
    VoyagerRecord record4 = voyagerRecord()
        .withUserId(userId2)
        .withVoyagerType(MORTGAGE_APPLICATION)
        .withState(ACTIVE)
        .buildAndPersist(transacter);

    List<VoyagerRecordDetails> responseList = getQuery(userId1).process().getResponseList();

    assertThat(responseList, containsInAnyOrder(
        new VoyagerRecordDetails(record1.getId(), record1.getVoyagerType()),
        new VoyagerRecordDetails(record2.getId(), record2.getVoyagerType())
    ));

  }

  private GetActiveVoyagerRecordsForUser getQuery(UserId userId) {
    GetActiveVoyagerRecordsForUser query = new GetActiveVoyagerRecordsForUser(userId);
    query.transacter = new ContentionSimulatingRetryingTransacter(transacter);
    return query;
  }

}
