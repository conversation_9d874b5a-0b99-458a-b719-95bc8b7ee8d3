package com.wealthfront.stubqueries.sand;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.api.ExposeQuery;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.entities.Money;
import com.kaching.platform.common.Option;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.queryengine.StubQuery;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Nullable;

@ExposeQuery(ExposeTo.TAOS)
public class GetMyMoneyResult extends StubQuery<GetMyMoneyResult.MyMoneyResult, KachingServices.SAND> {
  public GetMyMoneyResult() {
  }

  @ExposeType(ExposeTo.TAOS)
  @Entity
  public static class MyMoneyResult {
    @Value(
        optional = true,
        nullable = true
    )
    private Map<String, String> errors;

    @Value(
        nullable = true
    )
    private Money value;

    public MyMoneyResult() {
      // JSON
    }

    public MyMoneyResult(Map<String, String> errors, Money value) {
      this.errors = errors;
      this.value = value;
    }

    public Option<Map<String, String>> getErrors() {
      return Option.of(errors);
    }

    public Option<Money> getValue() {
      return Option.of(value);
    }

    public void validate() {
    }

    @Override
    public int hashCode() {
      return Objects.hash(this.errors, this.value);
    }

    @Override
    public boolean equals(Object o) {
      if (o == this) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      MyMoneyResult that = (MyMoneyResult) o;
      return Objects.equals(errors, that.errors) &&
          Objects.equals(value, that.value);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder();
      boolean isExactClass = this.getClass().equals(MyMoneyResult.class);
      if (isExactClass) {
        sb.append("MyMoneyResult {\n");
      }
      sb.append("  errors: ").append(errors == null ? "null" : errors.toString().replaceAll("\n", "\n  ")).append("\n");
      sb.append("  value: ").append(value).append("\n");
      if (isExactClass) {
        sb.append("}");
      }
      return sb.toString();
    }

    public Builder copy() {
      return builder()
        .withErrors(getErrors().getOrNull())
        .withValue(getValue().getOrNull());
    }

    public static Builder builder() {
      return new Builder();
    }

    public static class Builder {
      @Nullable
      private Map<String, String> errors = new HashMap<>();

      @Nullable
      private Money value = null;

      public Builder withErrors(@Nullable Map<String, String> errors) {
        this.errors = errors;
        return this;
      }

      public Builder withValue(@Nullable Money value) {
        this.value = value;
        return this;
      }

      public MyMoneyResult build() {
        MyMoneyResult obj1 = new MyMoneyResult(errors, value);
        obj1.validate();
        return obj1;
      }

      @VisibleForTesting
      public MyMoneyResult buildForTesting() {
        MyMoneyResult obj1 = new MyMoneyResult(errors, value);
        return obj1;
      }
    }
  }
}
