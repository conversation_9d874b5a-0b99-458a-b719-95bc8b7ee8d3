package com.wealthfront.stubqueries.sand;

import com.kaching.api.ExposeQuery;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.api.JavaApiCodeGeneratorImplTest;
import com.kaching.entities.Money;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.queryengine.StubQuery;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@ExposeQuery(ExposeTo.TAOS)
public class GetChildOfNonCodeGenParent extends StubQuery<GetChildOfNonCodeGenParent.ChildOfNonCodeGenParent, KachingServices.SAND> {
  public GetChildOfNonCodeGenParent() {
  }

  @ExposeType({
      ExposeTo.TAOS,
      ExposeTo.API_SERVER
  })
  @Entity
  public static class ChildOfNonCodeGenParent extends JavaApiCodeGeneratorImplTest.NonCodeGeneratedParent {
    @Value(
        nullable = false
    )
    private Money childMoney;

    public ChildOfNonCodeGenParent() {
      // JSON
    }

    public ChildOfNonCodeGenParent(Money parentMoney, Money childMoney) {
      super(parentMoney);
      this.childMoney = childMoney;
    }

    public Money getChildMoney() {
      return childMoney;
    }
  }
}
