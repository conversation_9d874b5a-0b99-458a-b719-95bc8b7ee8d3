package com.wealthfront.stubqueries.sand;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.kaching.api.ExposeQuery;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.entities.Money;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.queryengine.StubQuery;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.wealthfront.auto.types.sand.CodeGenEntitiesNonInlinedNonPolymorphicParent;
import java.util.Objects;
import javax.annotation.Nonnull;

@ExposeQuery(ExposeTo.TAOS)
public class GetNonPolymorphicChild extends StubQuery<GetNonPolymorphicChild.NonPolymorphicChild, KachingServices.SAND> {
  public GetNonPolymorphicChild() {
  }

  @ExposeType(ExposeTo.TAOS)
  @Entity
  public static class NonPolymorphicChild extends CodeGenEntitiesNonInlinedNonPolymorphicParent {
    @Value(
        nullable = false
    )
    private Money childMoney;

    public NonPolymorphicChild() {
      // JSON
    }

    public NonPolymorphicChild(Money parentMoney, Money childMoney) {
      super(parentMoney);
      this.childMoney = childMoney;
    }

    public Money getChildMoney() {
      return childMoney;
    }

    @Override
    public void validate() {
      super.validate();
      Preconditions.checkNotNull(childMoney, "field 'childMoney' should not be null");
    }

    @Override
    public int hashCode() {
      return Objects.hash(super.hashCode(), this.childMoney);
    }

    @Override
    public boolean equals(Object o) {
      if (o == this) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      NonPolymorphicChild that = (NonPolymorphicChild) o;
      return super.equals(that) &&
          Objects.equals(childMoney, that.childMoney);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder();
      boolean isExactClass = this.getClass().equals(NonPolymorphicChild.class);
      if (isExactClass) {
        sb.append("NonPolymorphicChild {\n");
      }
      sb.append(super.toString());
      sb.append("  childMoney: ").append(childMoney).append("\n");
      if (isExactClass) {
        sb.append("}");
      }
      return sb.toString();
    }

    public Builder copy() {
      return builder()
        .withParentMoney(getParentMoney())
        .withChildMoney(getChildMoney());
    }

    public static Builder builder() {
      return new Builder();
    }

    public static class Builder extends CodeGenEntitiesNonInlinedNonPolymorphicParent.Builder {
      @Nonnull
      private Money parentMoney = null;

      @Nonnull
      private Money childMoney = null;

      public Builder withParentMoney(@Nonnull Money parentMoney) {
        this.parentMoney = parentMoney;
        return this;
      }

      public Builder withChildMoney(@Nonnull Money childMoney) {
        this.childMoney = childMoney;
        return this;
      }

      public NonPolymorphicChild build() {
        NonPolymorphicChild obj1 = new NonPolymorphicChild(parentMoney, childMoney);
        obj1.validate();
        return obj1;
      }

      @VisibleForTesting
      public NonPolymorphicChild buildForTesting() {
        NonPolymorphicChild obj1 = new NonPolymorphicChild(parentMoney, childMoney);
        return obj1;
      }
    }
  }
}
