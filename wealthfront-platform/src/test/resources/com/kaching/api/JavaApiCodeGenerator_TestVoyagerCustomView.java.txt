package com.wealthfront.auto.types.global;

import com.google.common.annotations.VisibleForTesting;
import com.twolattes.json.Entity;

@Entity(
    discriminator = "VoyagerCustomView"
)
public class TestVoyagerCustomView extends TestVoyagerView {
  public TestVoyagerCustomView() {
  }

  @Override
  public <T> T visit(TestVoyagerView.Visitor<T> visitor) {
    return visitor.caseTestVoyagerCustomView(this);
  }

  @Override
  public void validate() {
    super.validate();
  }

  @Override
  public int hashCode() {
    return super.hashCode();
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestVoyagerCustomView that = (TestVoyagerCustomView) o;
    return super.equals(that);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(TestVoyagerCustomView.class);
    if (isExactClass) {
      sb.append("TestVoyagerCustomView {\n");
    }
    sb.append(super.toString());
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder();
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    public TestVoyagerCustomView build() {
      TestVoyagerCustomView obj1 = new TestVoyagerCustomView();
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public TestVoyagerCustomView buildForTesting() {
      TestVoyagerCustomView obj1 = new TestVoyagerCustomView();
      return obj1;
    }
  }
}
