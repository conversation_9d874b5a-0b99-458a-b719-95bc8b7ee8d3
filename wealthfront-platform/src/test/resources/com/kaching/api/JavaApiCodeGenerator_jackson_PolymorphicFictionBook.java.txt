package com.wealthfront.auto.types.global;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Nullable;

@Entity(
    discriminator = "fiction"
)
public class PolymorphicFictionBook extends PolymorphicBook {
  @Value(
      optional = true,
      nullable = true
  )
  private List<String> characters;

  @Value(
      optional = true,
      nullable = true
  )
  private GenreEnum genre;

  public PolymorphicFictionBook() {
    // JSON
  }

  public PolymorphicFictionBook(BookTypeEnum bookType, String bookId, String title, String author,
      Integer pagesCount, List<String> characters, GenreEnum genre) {
    super(bookType, bookId, title, author, pagesCount);
    this.characters = characters;
    this.genre = genre;
  }

  @Override
  public <T> T visit(PolymorphicBook.Visitor<T> visitor) {
    return visitor.casePolymorphicFictionBook(this);
  }

  public Option<List<String>> getCharacters() {
    return Option.of(characters);
  }

  public Option<GenreEnum> getGenre() {
    return Option.of(genre);
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    @Nullable
    private BookTypeEnum bookType = null;

    @Nullable
    private String bookId = null;

    @Nullable
    private String title = null;

    @Nullable
    private String author = null;

    @Nullable
    private Integer pagesCount = null;

    @Nullable
    private List<String> characters = new ArrayList<>();

    @Nullable
    private GenreEnum genre = null;

    public Builder withBookType(@Nullable BookTypeEnum bookType) {
      this.bookType = bookType;
      return this;
    }

    public Builder withBookId(@Nullable String bookId) {
      this.bookId = bookId;
      return this;
    }

    public Builder withTitle(@Nullable String title) {
      this.title = title;
      return this;
    }

    public Builder withAuthor(@Nullable String author) {
      this.author = author;
      return this;
    }

    public Builder withPagesCount(@Nullable Integer pagesCount) {
      this.pagesCount = pagesCount;
      return this;
    }

    public Builder withCharacters(@Nullable List<String> characters) {
      this.characters = characters;
      return this;
    }

    public Builder withGenre(@Nullable GenreEnum genre) {
      this.genre = genre;
      return this;
    }

    public PolymorphicFictionBook build() {
      PolymorphicFictionBook obj1 = new PolymorphicFictionBook(bookType, bookId, title, author, pagesCount, characters, genre);
      return obj1;
    }

    @VisibleForTesting
    public PolymorphicFictionBook buildForTesting() {
      PolymorphicFictionBook obj1 = new PolymorphicFictionBook(bookType, bookId, title, author, pagesCount, characters, genre);
      return obj1;
    }
  }
}
