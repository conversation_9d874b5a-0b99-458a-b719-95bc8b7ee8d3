<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping default-access="field" package="com.kaching.platform.hibernate">
  <class name="com.kaching.platform.hibernate.offline.FreshnessEvaluatorImplTest$MutableEntity" table="mutables">
    <id name="id" column="id" type="Id"> 
      <generator class="com.kaching.entities.types.IdGenerator" />
    </id>
    <property name="userName" column="user_name" not-null="true" />
    <property name="updatedAt" column="updated_at" not-null="true" type="com.kaching.util.types.DateTimeHibernateTypeFull" />
  </class>
</hibernate-mapping>