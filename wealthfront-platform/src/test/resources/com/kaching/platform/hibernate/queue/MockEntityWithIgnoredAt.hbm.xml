<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping default-access="field" package="com.kaching.platform.hibernate.queue">
  <class name="MockEntityWithIgnoredAt" table="mock_entities_with_ignored_at" abstract="false">
    <id name="id" column="id" type="Id">
      <generator class="com.kaching.entities.types.IdGenerator" />
    </id>
    <property name="createdAt" column="creation_time" type="DateTime_full" />
    <property name="polledTime" column="polled_time" type="DateTime_full" />
    <property name="sentTime" column="sent_time" type="DateTime_full" />
    <property name="ignoredAt" column="ignored_at" type="DateTime_full" />
    <property name="errorFlag" column="error_flag" type="boolean" not-null="true"/>
  </class>
</hibernate-mapping>
