{"services": [{"id": "PY7PX36", "name": "Brokerage Ops", "description": null, "auto_resolve_timeout": null, "acknowledgement_timeout": 43200, "created_at": "2016-06-16T17:37:51Z", "status": "active", "last_incident_timestamp": "2018-08-15T16:24:50Z", "teams": [], "incident_urgency_rule": {"type": "constant", "urgency": "high"}, "scheduled_actions": [], "support_hours": null, "escalation_policy": {"id": "PCUV11H", "type": "escalation_policy_reference", "summary": "Brokerage Ops", "self": "https://api.pagerduty.com/escalation_policies/PCUV11H", "html_url": "https://wealthfront.pagerduty.com/escalation_policies/PCUV11H"}, "addons": [], "privilege": null, "alert_creation": "create_incidents", "integrations": [{"id": "PK0JW5O", "type": "nagios_inbound_integration_reference", "summary": "<PERSON><PERSON><PERSON>", "self": "https://api.pagerduty.com/services/PY7PX36/integrations/PK0JW5O", "html_url": "https://wealthfront.pagerduty.com/services/PY7PX36/integrations/PK0JW5O"}, {"id": "P0AHZXI", "type": "generic_events_api_inbound_integration_reference", "summary": "Generic API", "self": "https://api.pagerduty.com/services/PY7PX36/integrations/P0AHZXI", "html_url": "https://wealthfront.pagerduty.com/services/PY7PX36/integrations/P0AHZXI"}], "type": "service", "summary": "Brokerage Ops", "self": "https://api.pagerduty.com/services/PY7PX36", "html_url": "https://wealthfront.pagerduty.com/services/PY7PX36"}], "limit": 2, "offset": 2, "total": null, "more": false}