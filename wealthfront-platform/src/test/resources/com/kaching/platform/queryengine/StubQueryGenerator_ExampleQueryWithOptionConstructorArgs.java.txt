package com.wealthfront.stubqueries.nop;

import com.kaching.platform.common.Option;
import com.kaching.platform.converters.Instantiate;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.queryengine.StubQuery;
import org.joda.time.DateTime;

public class ExampleQueryWithOptionConstructorArgs extends StubQuery<String, KachingServices.NOP> {
  private final Long testEntityId;

  private final Integer integer;

  private final Option<DateTime> maybeDateTime;

  private final String anOptionalString;

  @Instantiate
  public ExampleQueryWithOptionConstructorArgs(Long testEntityId, Integer integer,
      Option<DateTime> maybeDateTime, String anOptionalString) {
    this.testEntityId = testEntityId;
    this.integer = integer;
    this.maybeDateTime = maybeDateTime;
    this.anOptionalString = anOptionalString;
  }

  /**
   * Use the non deprecated constructor with Option.none() or null */
  @Deprecated
  public ExampleQueryWithOptionConstructorArgs(Long testEntityId, Integer integer,
      Option<DateTime> maybeDateTime) {
    this(testEntityId, integer, maybeDateTime, null);
  }

  /**
   * Use the non deprecated constructor with Option.none() or null */
  @Deprecated
  public ExampleQueryWithOptionConstructorArgs(Long testEntityId, Integer integer) {
    this(testEntityId, integer, Option.none());
  }

  public Long getTestEntityId() {
    return testEntityId;
  }

  public Integer getInteger() {
    return integer;
  }

  public Option<DateTime> getMaybeDateTime() {
    return maybeDateTime;
  }

  public String getAnOptionalString() {
    return anOptionalString;
  }
}
