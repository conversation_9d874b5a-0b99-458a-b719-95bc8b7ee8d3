<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping default-access="field" package="com.wealthfront.voyager.example">
  <typedef class="com.kaching.util.types.UserTypeAdapter" name="UserId">
    <param name="type">com.kaching.user.UserId</param>
    <param name="converter">com.kaching.user.UserId$Converter</param>
  </typedef>
  <typedef class="com.kaching.util.types.EnumTypeAdapter" name="State">
    <param name="type">com.wealthfront.voyager.example.ExampleMortgageApplication$State</param>
  </typedef>

  <class name="ExampleMortgageApplication" table="mortgage_applications">
    <id name="id" column="id" type="Id">
      <generator class="identity"/>
    </id>

    <property name="userId" column="user_id" type="UserId"/>
    <property name="createdAt" column="created_at" type="DateTime_full" not-null="true"/>
    <property name="voyagerRecordId" column="voyager_record_id" type="Id"/>

    <kawala:archived
        name="state"
        denormalized="true"
        entity-name="mortgage_application_state"
        current-column="state"
        parent-column="mortgage_application_id"
        version-table="version_mortgage_application_state">
      <property name="value" type="State" not-null="true"/>
    </kawala:archived>

  </class>

</hibernate-mapping>